import{z as J,_ as ee,P as H,L as te,$ as ae,a0 as ne,a1 as le,a2 as oe,a3 as ie,a4 as se,m as q,a5 as re,a6 as pe,a7 as ue,a8 as de,a9 as _e,aa as ce,ab as me,ac as fe,ad as ye,ae as ve,af as ke,ag as ge,ah as he}from"./common-1288aff8.js";import{d as M,h as G,r as $,bz as W,K as X,a,n as Z,o as r,k as T,b as e,w as t,p as Q,u as l,c as w,l as I,e as g,t as D,F as O,v as N,z as be,A as xe,_ as E,y as j,ab as Ie,i as K,s as V,f as we,g as Pe,bD as Ce,bE as Te}from"./vendor-f5ec1a19.js";import{F as Se,m as B}from"./antd-a6b46d10.js";const Ue=[{dataIndex:"key",key:"key",title:"多语言key",width:"200px",fixed:"left"},{dataIndex:"category",key:"category",title:"模块",width:"120px"}],Le=[{dataIndex:"id",key:"id",title:"id",width:"60px"},{dataIndex:"gift_id",key:"gift_id",title:"gift_id",width:"140px"},{dataIndex:"name",key:"name",title:"礼包名称(后台)",width:"130px"},{dataIndex:"desc_key",key:"desc_key",title:"礼包名称(前台)",width:"130px"},{dataIndex:"icon_url",key:"icon_url",title:"icon",width:"70px"},{dataIndex:"item_list",key:"item_list",title:"道具详情",width:"230px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],Oe=[{dataIndex:"item_id",key:"item_id",title:"道具",ellipsis:!0},{dataIndex:"num",key:"num",title:"数量",width:"80px"},{dataIndex:"action",key:"action",title:"操作",width:"80px",align:"center"}],$e=[{dataIndex:"id",key:"id",title:"道具ID",width:"60px",fixed:"left",align:"center"},{dataIndex:"internal_id",key:"internal_id",title:"internal_id",width:"100px"},{dataIndex:"item_id",key:"item_id",title:"item_id",width:"230px"},{dataIndex:"image",key:"image",title:"道具icon",width:"80px"},{dataIndex:"name",key:"name",title:"中文名",width:"130px"}],Re=[{dataIndex:"id",key:"id",title:"ID",width:"60px",fixed:"left",align:"center"},{dataIndex:"type",key:"type",title:"道具类型",width:"100px"},{dataIndex:"item_id",key:"item_id",title:"三方券id",width:"230px"},{dataIndex:"name",key:"name",title:"道具名称",width:"130px"},{dataIndex:"image",key:"image",title:"道具icon",width:"80px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],Fe=[{dataIndex:"id",key:"id",title:"ID",width:"80px",fixed:"left",align:"center"},{dataIndex:"item_id",key:"id",title:"道具ID",width:"150px",fixed:"left",align:"center"},{dataIndex:"item_type",key:"item_type",title:"道具类型",width:"120px"},{dataIndex:"icon",key:"icon",title:"道具icon",width:"100px"},{dataIndex:"name_key",key:"name_key",title:"道具名称",width:"150px",ellipsis:!0},{dataIndex:"description_key",key:"description_key",title:"道具说明",width:"200px",ellipsis:!0},{dataIndex:"activation_type",key:"activation_type",title:"激活方式",width:"120px"},{dataIndex:"effect_value",key:"effect_value",title:"参数设置",width:"100px"},{dataIndex:"activation_period",key:"activation_period",title:"有效期(天)",width:"120px",align:"right"},{dataIndex:"effect_period",key:"effect_period",title:"使用时效(小时)",width:"120px",align:"right"},{dataIndex:"rank_limit",key:"rank_limit",title:"等级限制",width:"150px"},{dataIndex:"action",key:"action",title:"操作",width:"160px",fixed:"right",align:"center"}],De=M({__name:"Index",setup(f){const n=G({searchParams:{key:""}}),h=te,u=$(),{configState:d}=W(J()),o=X(()=>{const _=[...d.value.langs.map(c=>({ellipsis:!0,dataIndex:c.value,title:`${c.label}(${c.value})`,key:c.value,type:"lang",width:"200px"})).sort((c,x)=>c.dataIndex<x.dataIndex?1:-1)];return[...Ue,..._]}),m=()=>u.value.requestTableData(!0);return(_,c)=>{const x=a("a-input-search"),v=a("a-col"),i=a("UploadBtn"),p=a("a-row"),s=a("a-card"),P=a("PropIcon"),y=a("a-tooltip"),b=a("CustomTable"),U=Z("has");return r(),T(O,null,[e(s,{class:"filter",border:!1},{default:t(()=>[e(p,{justify:"space-between"},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(x,{value:n.searchParams.key,"onUpdate:value":c[0]||(c[0]=k=>n.searchParams.key=k),placeholder:"请输入搜索内容",style:{width:"300px"},"enter-button":"查询",allowClear:"",onSearch:m},null,8,["value"])]),_:1}),Q(e(i,{ref:"uploadBtn",onUploadSuccess:m,downloadApi:l(ee),fileType:"lang",page:l(H).MULTILINGUAL},null,8,["downloadApi","page"]),[[U,"Operation"]])]),_:1})]),_:1}),e(b,{ref_key:"RefCustomTable",ref:u,"data-api":l(h),params:n.searchParams,columns:l(o)},{bodyCell:t(({column:k,record:S})=>[k.key==="image"?(r(),w(P,{key:0,"prop-detail":S,style:{width:"50px",height:"50px"}},null,8,["prop-detail"])):I("",!0),k.type==="lang"?(r(),w(y,{key:1,title:S[k.dataIndex],placement:"topLeft"},{default:t(()=>[g(D(S[k.dataIndex]),1)]),_:2},1032,["title"])):I("",!0)]),_:1},8,["data-api","params","columns"])],64)}}}),ut=Object.freeze(Object.defineProperty({__proto__:null,default:De},Symbol.toStringTag,{value:"Module"})),Y=f=>(be("data-v-b51eee60"),f=f(),xe(),f),Me={style:{"flex-shrink":"0"}},Ae={key:1,xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 83 83",fill:"none",style:{"flex-shrink":"0"}},Ne=Y(()=>N("circle",{cx:"41.5",cy:"41.5",r:"36.5",fill:"#F1B545",stroke:"#F5F070",strokeWidth:"10"},null,-1)),Ge=Y(()=>N("path",{d:"M56.5652 53.8262L62.0435 29.1741L49.7174 37.3915L41.5 23.6958L33.2826 37.3915L20.9565 29.1741L27.8044 53.8262H56.5652Z",fill:"#F7F473"},null,-1)),qe=[Ne,Ge],Be={style:{margin:"0 5px",overflow:"hidden"}},je={class:"t-name"},Ve={class:"t-name"},ze={key:0,class:"num",style:{"flex-shrink":"0"}},Ee=M({__name:"Item",props:{item:{type:Object,default:()=>({})},showNum:{type:Boolean,default:!0}},setup(f){const n=f;return(h,u)=>{const d=a("a-image"),o=a("a-flex");return r(),T("div",null,[e(o,{align:"center"},{default:t(()=>[N("div",Me,[n.item.gift_type===1||n.item.gift_type===2?(r(),w(d,{key:0,src:n.item.image,width:"50px",preview:!1},null,8,["src"])):I("",!0),n.item.gift_type===0?(r(),T("svg",Ae,qe)):I("",!0)]),N("div",Be,[N("div",je,D(n.item.name),1),N("div",Ve,D(n.item.item_id),1)]),n.showNum?(r(),T("div",ze,"X"+D(n.item.num),1)):I("",!0)]),_:1})])}}});const z=E(Ee,[["__scopeId","data-v-b51eee60"]]),Ke=M({name:"FormPropItem",components:{Item:z},props:{value:{type:Array,default:()=>[]}},setup(f,{emit:n}){const h=Se.useInjectFormItemContext(),u=v=>{n("update:value",v),h.onFieldChange()},d=G({type:void 0,itemKey:void 0,loading:!1,crtItem:{},options:[]}),{configState:o}=W(J());j(()=>f.value,v=>{v&&v.length===0&&h.onFieldChange()},{deep:!0});const m=v=>{console.log("remoteMethod query",v),v&&(d.loading=!0,ae({search_key:v,type:d.type,game_project:localStorage.getItem("crtGame")||"ss_global"}).then(i=>{d.options=i.map(p=>({...p,value:p.item_id,label:p.name})),console.log(d.options)}).finally(()=>{d.loading=!1}))},_=(v,i)=>{console.log("select option",i),d.crtItem=i},c=v=>{console.log("change val",v),d.options=[],d.itemKey=void 0,d.crtItem={}},x=()=>{if(d.type===0){if(f.value.find(v=>v.item_id==="coin"))return B.info("已添加此道具，直接修改数量");u([...f.value,{num:1,game_project:"funplus_zone",item_id:"coin",internal_id:"coin",name:"积分",gift_type:d.type}])}if(d.type===1||d.type===2||d.type===3){if(!d.crtItem.item_id)return;if(f.value.find(v=>v.item_id===d.crtItem.item_id))return B.info("已添加此道具，直接修改数量");u([...f.value,{...d.crtItem,num:1,gift_type:d.type,game_project:localStorage.getItem("crtGame")}])}console.log("state.crtItem",d.crtItem),d.crtItem={},d.itemKey=void 0};return{configState:o,...Ie(d),remoteMethod:m,select:_,change:c,addItem:x,GIFT_ITEM_COLUMNS_MAP:Oe}}});const Je={style:{color:"#999"}};function He(f,n,h,u,d,o){const m=a("a-select"),_=a("a-input-group"),c=a("PlusOutlined"),x=a("a-button"),v=a("a-flex"),i=a("Item"),p=a("a-input-number"),s=a("a-typography-link"),P=a("a-table");return r(),T(O,null,[e(v,null,{default:t(()=>[e(_,{compact:""},{default:t(()=>[e(m,{value:f.type,"onUpdate:value":n[0]||(n[0]=y=>f.type=y),onChange:f.change,placeholder:"道具类型",options:f.configState.prize_type,style:{width:"100px"}},null,8,["value","onChange","options"]),f.type===1||f.type===2||f.type===3?(r(),w(m,{key:0,value:f.itemKey,"onUpdate:value":n[1]||(n[1]=y=>f.itemKey=y),showSearch:"",placement:"bottomRight",placeholder:"请输入多语言key","filter-option":!1,"not-found-content":f.loading?void 0:null,"option-label-prop":"name",onSearch:f.remoteMethod,onSelect:f.select,style:{width:"230px"},options:f.options,"label-in-value":"",dropdownStyle:{width:"300px !important",color:"red"}},{option:t(y=>[N("div",null,D(y.name),1),N("div",Je,D(y.item_id),1)]),_:1},8,["value","not-found-content","onSearch","onSelect","options"])):I("",!0)]),_:1}),e(x,{type:"primary",onClick:f.addItem},{icon:t(()=>[e(c)]),_:1},8,["onClick"])]),_:1}),e(P,{style:{"margin-top":"5px"},size:"small",columns:f.GIFT_ITEM_COLUMNS_MAP,"data-source":f.value,pagination:!1},{bodyCell:t(({record:y,index:b,column:U})=>[U.key==="item_id"?(r(),w(i,{key:0,item:y,"show-num":!1},null,8,["item"])):I("",!0),U.key==="num"?(r(),w(p,{key:1,value:f.value[b].num,"onUpdate:value":k=>f.value[b].num=k,style:{width:"60px"}},null,8,["value","onUpdate:value"])):I("",!0),U.key==="action"?(r(),w(s,{key:2,type:"danger",onClick:()=>f.value.splice(b,1)},{default:t(()=>[g("删除")]),_:2},1032,["onClick"])):I("",!0)]),_:1},8,["columns","data-source"])],64)}const We=E(Ke,[["render",He],["__scopeId","data-v-a994954c"]]),Xe=M({__name:"Form",props:["editId"],emits:["close","refresh"],setup(f,{emit:n}){const h=f,u=$({id:0,gift_id:"",name:"",desc_key:void 0,icon_url:"",item_list:[],gift_value:0}),d=$(!0),o=$(!1),m=()=>{o.value=!0,ne(h.editId).then(v=>{u.value={...u.value,...v},u.value.item_list=JSON.parse(v.item_list)}).finally(()=>o.value=!1)};h.editId&&m();const _=$(!1),c=$(),x=()=>{c.value.validate().then(()=>{_.value=!0;const{id:v,...i}=u.value;i.item_list=JSON.stringify(i.item_list.map(p=>({internal_id:p.internal_id,item_id:p.item_id,num:p.num,gift_type:p.gift_type,game_project:p.game_project}))),h.editId?le(v,i).then(()=>{n("close"),n("refresh")}).catch(()=>{}).finally(()=>{_.value=!1}):oe(i).then(()=>{n("close"),n("refresh")}).catch(()=>{}).finally(()=>{_.value=!1})}).catch(()=>{})};return(v,i)=>{const p=a("a-input"),s=a("a-form-item"),P=a("SelectLang"),y=a("SelectImg"),b=a("a-input-number"),U=a("ExclamationCircleFilled"),k=a("a-typography-text"),S=a("a-button"),L=a("a-form"),A=a("a-spin"),F=a("a-drawer");return r(),w(F,{open:l(d),"onUpdate:open":i[6]||(i[6]=R=>K(d)?d.value=R:null),title:"编辑规则",maskClosable:!1,width:600,onAfterOpenChange:i[7]||(i[7]=R=>!R&&n("close"))},{default:t(()=>[e(A,{spinning:l(o)},{default:t(()=>[e(L,{model:l(u),name:"basic",ref_key:"formRef",ref:c,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:t(()=>[e(s,{label:"gift_id",name:"gift_id"},{default:t(()=>[e(p,{value:l(u).gift_id,"onUpdate:value":i[0]||(i[0]=R=>l(u).gift_id=R),placeholder:"创建后自动生成，不支持修改",disabled:""},null,8,["value"])]),_:1}),e(s,{label:"礼包名称(后台)",name:"name"},{default:t(()=>[e(p,{value:l(u).name,"onUpdate:value":i[1]||(i[1]=R=>l(u).name=R),placeholder:"仅做后台展示用"},null,8,["value"])]),_:1}),e(s,{label:"礼包名称(前台)",name:"desc_key"},{default:t(()=>[e(P,{value:l(u).desc_key,"onUpdate:value":i[2]||(i[2]=R=>l(u).desc_key=R),placeholder:"选择多语言key,不填写则默认使用道具名称"},null,8,["value"])]),_:1}),e(s,{label:"礼包icon",name:"icon_url"},{default:t(()=>[e(y,{value:l(u).icon_url,"onUpdate:value":i[3]||(i[3]=R=>l(u).icon_url=R),"width-height":[72,72]},null,8,["value"])]),_:1}),e(s,{label:"礼包价值",name:"gift_value",rules:[{required:!0,message:"请输入礼包价值"}]},{default:t(()=>[e(b,{value:l(u).gift_value,"onUpdate:value":i[4]||(i[4]=R=>l(u).gift_value=R),placeholder:"请输入礼包价值","addon-after":"积分"},null,8,["value"]),e(k,{type:"warning",style:{"font-size":"12px",display:"block"}},{default:t(()=>[e(U),g(" 会员专享礼包需填写，发生退款时扣除对应积分 ")]),_:1})]),_:1}),e(s,{label:"添加道具",name:"item_list",rules:[{type:"array",required:!0,message:"请添加道具"}]},{default:t(()=>[e(We,{value:l(u).item_list,"onUpdate:value":i[5]||(i[5]=R=>l(u).item_list=R)},null,8,["value"])]),_:1}),e(s,{"wrapper-col":{offset:6,span:16}},{default:t(()=>[e(S,{type:"primary",onClick:x,loading:l(_)},{default:t(()=>[g("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open"])}}}),Ze=M({__name:"Index",setup(f){const n=G({editVisible:!1,editId:0}),h=$(),u=()=>h.value.requestTableData(!0),d=(m,_)=>{n.editVisible=m,n.editId=_||0},o=m=>q("确定要删除此条数据吗？",re,{id:m}).then(()=>u());return(m,_)=>{const c=a("a-button"),x=a("UploadBtn"),v=a("PlusOutlined"),i=a("LangKey"),p=a("a-image"),s=a("a-divider"),P=a("a-typography-link"),y=a("a-space"),b=a("CustomTable"),U=Z("has");return r(),T(O,null,[e(b,{ref_key:"RefCustomTable",ref:h,"data-api":l(se),params:{},columns:l(Le)},{leftTool:t(()=>[e(c,{type:"primary",onClick:_[0]||(_[0]=k=>d(!0))},{default:t(()=>[g("道具打包")]),_:1}),Q(e(x,{style:{"margin-left":"30px"},ref:"uploadBtn",onUploadSuccess:u,fileType:"gift-package",page:l(H).GIFTCONFIG,downloadData:{},hideDownloadBtn:!0},null,8,["page"]),[[U,"Operation"]]),e(c,{style:{"margin-left":"30px"},type:"primary",onClick:_[1]||(_[1]=k=>l(ie)())},{icon:t(()=>[e(v)]),default:t(()=>[g(" 导出礼包 ")]),_:1})]),bodyCell:t(({record:k,column:S})=>[S.key==="desc_key"?(r(),w(i,{key:0,"lang-key":k.desc_key,i18n_name:k.i18n_name},null,8,["lang-key","i18n_name"])):I("",!0),S.key==="icon_url"?(r(),T(O,{key:1},[k.icon_url?(r(),w(p,{key:0,src:k.icon_url,height:60},null,8,["src"])):(r(),T(O,{key:1},[g("-")],64))],64)):I("",!0),S.key==="item_list"?(r(),T(O,{key:2},[k.item_list.length>=3?(r(),T(O,{key:0},[(r(!0),T(O,null,V(k.item_list.slice(0,k.show_all?k.item_list.length:2),(L,A)=>(r(),w(z,{style:{"margin-top":"5px"},item:L,key:A},null,8,["item"]))),128)),k.show_all?I("",!0):(r(),w(c,{key:0,type:"link",onClick:L=>k.show_all=!k.show_all},{default:t(()=>[g("查看全部")]),_:2},1032,["onClick"]))],64)):(r(!0),T(O,{key:1},V(k.item_list,(L,A)=>(r(),w(z,{style:{"margin-top":"5px"},item:L,key:A},null,8,["item"]))),128))],64)):I("",!0),S.key==="action"?(r(),w(y,{key:3},{split:t(()=>[e(s,{type:"vertical",style:{margin:"0"}})]),default:t(()=>[e(P,{onClick:L=>d(!0,k.id)},{default:t(()=>[g("编辑")]),_:2},1032,["onClick"]),e(P,{type:"danger",onClick:L=>o(k.id)},{default:t(()=>[g("删除")]),_:2},1032,["onClick"])]),_:2},1024)):I("",!0)]),_:1},8,["data-api","columns"]),l(n).editVisible?(r(),w(Xe,{key:0,"edit-id":l(n).editId,onClose:_[2]||(_[2]=k=>d(!1)),onRefresh:u},null,8,["edit-id"])):I("",!0)],64)}}}),dt=Object.freeze(Object.defineProperty({__proto__:null,default:Ze},Symbol.toStringTag,{value:"Module"})),Qe=N("span",{class:"self-center"},"-",-1),Ye=M({__name:"PlatformPropsForm",props:["editId"],emits:["close","refresh"],setup(f,{emit:n}){const h=f,u=$(!1),d=()=>{u.value=!0,pe(h.editId).then(p=>{console.log("res",p),o.value=p;const s=JSON.parse(p.rank_limit);console.log("resRankLimit",s),i.min=s.min||1,i.max=s.max||7}).finally(()=>u.value=!1)};h.editId&&d();const o=$({id:void 0,item_type:1,name_key:"",description_key:"",effect_value:null,activation_period:null,effect_period:null,rank_limit:'{"min":1,"max":7}',icon:"",activation_type:1,jump_url:"test",is_stackable:0,status:1}),m=G({optionsType:[{label:"积分加倍卡",value:1},{label:"升级体验卡",value:2},{label:"充值无忧服务卡",value:3}],optionsActivationType:[{label:"手动激活",value:1},{label:"自动激活",value:2}]}),_=$(!0),c=$(!1),x=$(),v=()=>{x.value.validate().then(()=>{const{id:p,...s}=o.value,P=JSON.parse(o.value.rank_limit);if(console.log("limit",P),!P.min||!P.max)return B.error("请输入等级限制");if(P.min>P.max)return B.error("最高等级不能小于最低等级");c.value=!0,o.value.item_type===3&&(s.effect_period=0),(h.editId?ue:de)({...s,id:p||void 0}).then(()=>{n("close"),n("refresh")}).finally(()=>{c.value=!1})}).catch(p=>{console.error("验证失败:",p)})},i=G({min:1,max:7});return j(i,p=>{o.value.rank_limit=JSON.stringify(p)}),j(()=>o.value.item_type,p=>{(p===2||p===3)&&(o.value.effect_value=1)}),(p,s)=>{const P=a("a-select"),y=a("a-form-item"),b=a("SelectImg"),U=a("SelectLang"),k=a("a-typography-text"),S=a("a-input-number"),L=a("a-space"),A=a("a-button"),F=a("a-form"),R=a("a-drawer");return r(),w(R,{open:_.value,"onUpdate:open":s[12]||(s[12]=C=>_.value=C),title:"编辑规则",maskClosable:!1,width:600,onAfterOpenChange:s[13]||(s[13]=C=>!C&&n("close"))},{default:t(()=>[e(F,{model:o.value,"label-col":{span:6},"wrapper-col":{span:18},ref_key:"formRef",ref:x},{default:t(()=>[e(y,{label:"道具类型",required:"",name:"item_type",rules:[{required:!0,message:"请选择道具类型"}]},{default:t(()=>[e(P,{style:{width:"100%"},value:o.value.item_type,"onUpdate:value":s[0]||(s[0]=C=>o.value.item_type=C),options:m.optionsType},null,8,["value","options"])]),_:1}),e(y,{label:"道具icon",required:"",name:"icon",rules:[{required:!0,message:"请上传图标"}]},{default:t(()=>[e(b,{value:o.value.icon,"onUpdate:value":s[1]||(s[1]=C=>o.value.icon=C),"width-height":[60,60]},null,8,["value"])]),_:1}),e(y,{label:"道具名称",required:"",name:"name_key"},{default:t(()=>[e(U,{value:o.value.name_key,"onUpdate:value":s[2]||(s[2]=C=>o.value.name_key=C)},null,8,["value"])]),_:1}),e(y,{label:"道具说明",required:"",name:"description_key"},{default:t(()=>[e(U,{value:o.value.description_key,"onUpdate:value":s[3]||(s[3]=C=>o.value.description_key=C)},null,8,["value"])]),_:1}),e(y,{label:"参数设置",required:"",name:"effect_value",rules:[{required:!0,message:"请输入参数值"},{type:"number",message:"必须为数字"}]},{default:t(()=>[o.value.item_type===1?(r(),w(L,{key:0,align:"center"},{default:t(()=>[e(k,null,{default:t(()=>[g("充值额外获取")]),_:1}),e(S,{value:o.value.effect_value,"onUpdate:value":s[4]||(s[4]=C=>o.value.effect_value=C),min:1,step:1,placeholder:"充值额外获取积分","addon-after":"%积分"},null,8,["value"])]),_:1})):o.value.item_type===2?(r(),w(L,{key:1,align:"center"},{default:t(()=>[e(k,null,{default:t(()=>[g("提升等级数")]),_:1}),e(S,{value:o.value.effect_value,"onUpdate:value":s[5]||(s[5]=C=>o.value.effect_value=C),disabled:!0,min:1,precision:0,placeholder:"提升等级数","addon-after":"级"},null,8,["value"])]),_:1})):o.value.item_type===3?(r(),w(L,{key:2,align:"center"},{default:t(()=>[e(S,{value:o.value.effect_value,"onUpdate:value":s[6]||(s[6]=C=>o.value.effect_value=C),min:1,disabled:!0,"addon-after":"次提工单服务"},null,8,["value"])]),_:1})):I("",!0)]),_:1}),e(y,{label:"激活方式",required:"",name:"activation_type"},{default:t(()=>[e(P,{value:o.value.activation_type,"onUpdate:value":s[7]||(s[7]=C=>o.value.activation_type=C),options:m.optionsActivationType},null,8,["value","options"])]),_:1}),e(y,{label:"有效期",required:"",name:"activation_period"},{default:t(()=>[e(L,{align:"center"},{default:t(()=>[e(k,null,{default:t(()=>[g("获取后")]),_:1}),e(S,{value:o.value.activation_period,"onUpdate:value":s[8]||(s[8]=C=>o.value.activation_period=C),min:1,"addon-after":"天有效",placeholder:"获取后有效天数"},null,8,["value"])]),_:1})]),_:1}),o.value.item_type!==3?(r(),w(y,{key:0,label:"使用时效",required:"",name:"effect_period"},{default:t(()=>[e(L,{align:"center"},{default:t(()=>[e(k,null,{default:t(()=>[g("激活后")]),_:1}),e(S,{value:o.value.effect_period,"onUpdate:value":s[9]||(s[9]=C=>o.value.effect_period=C),min:1,"addon-after":"h有效",placeholder:"激活后有效时长"},null,8,["value"])]),_:1})]),_:1})):I("",!0),e(y,{label:"使用等级限制",required:"",name:"rank_limit"},{default:t(()=>[e(L,{align:"center"},{default:t(()=>[e(S,{value:i.min,"onUpdate:value":s[10]||(s[10]=C=>i.min=C),min:1,placeholder:"最低等级"},null,8,["value"]),Qe,e(S,{value:i.max,"onUpdate:value":s[11]||(s[11]=C=>i.max=C),min:i.min||1,max:7,placeholder:"最高等级"},null,8,["value","min"])]),_:1})]),_:1}),e(y,{"wrapper-col":{offset:6,span:16}},{default:t(()=>[e(A,{type:"primary",onClick:v,loading:c.value},{default:t(()=>[g("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])}}}),et=M({__name:"PlatformProps",setup(f){const n=G({searchParams:{search_key:""},editVisible:!1,editId:0,optionsType:[{label:"积分加倍卡",value:1},{label:"升级体验卡",value:2},{label:"充值无忧服务卡",value:3}]}),h=$(),u=()=>h.value.requestTableData(!0),d=(_,c)=>{n.editVisible=_,n.editId=c||0},o=(_,c)=>q(`确定要删除${c?"选中的":"此条"}数据吗？`,ce,{id:_}).then(()=>u()),m=_=>q("确定要复制此条数据并生成一份新数据吗？",me,{id:_}).then(()=>u());return(_,c)=>{const x=a("PlusOutlined"),v=a("a-button"),i=a("a-input"),p=a("a-space"),s=a("PropIcon"),P=a("CustomTable");return r(),T(O,null,[e(P,{ref_key:"RefCustomTable",ref:h,"data-api":l(_e),params:l(n).searchParams,columns:l(Fe),"no-card":""},{leftTool:t(()=>[e(p,{wrap:""},{default:t(()=>[e(v,{type:"primary",onClick:d},{icon:t(()=>[e(x)]),default:t(()=>[g("新增道具 ")]),_:1}),e(i,{value:l(n).searchParams.search_key,"onUpdate:value":c[0]||(c[0]=y=>l(n).searchParams.search_key=y),clearable:"",placeholder:"请输入item_id或道具名称"},null,8,["value"]),e(v,{type:"primary",onClick:u},{default:t(()=>[g("搜索")]),_:1}),e(v,{onClick:c[1]||(c[1]=()=>{l(n).searchParams.search_key="",u()})},{default:t(()=>[g("重置")]),_:1})]),_:1})]),bodyCell:t(({column:y,record:b})=>{var U,k,S;return[y.dataIndex==="item_id"?(r(),T(O,{key:0},[g(D("platform_item_"+b.id),1)],64)):I("",!0),y.key==="item_type"?(r(),T(O,{key:1},[g(D((U=l(n).optionsType.find(L=>L.value===b.item_type))==null?void 0:U.label),1)],64)):I("",!0),y.key==="name_key"?(r(),T(O,{key:2},[g(D((k=b.i18n_name[0])==null?void 0:k.content),1)],64)):I("",!0),y.key==="activation_type"?(r(),T(O,{key:3},[g(D(b.activation_type===1?"手动激活":"自动激活"),1)],64)):I("",!0),y.key==="description_key"?(r(),T(O,{key:4},[g(D((S=b.i18n_description[0])==null?void 0:S.content),1)],64)):I("",!0),y.key==="icon"?(r(),w(s,{key:5,"prop-detail":{image:b.icon},style:{width:"50px",height:"50px"}},null,8,["prop-detail"])):I("",!0),y.key==="rank_limit"?(r(),T(O,{key:6},[g(D(JSON.parse(b.rank_limit).min)+" - "+D(JSON.parse(b.rank_limit).max),1)],64)):I("",!0),y.key==="effect_value"?(r(),T(O,{key:7},[g(D(b.item_type===1?`充值额外获取${b.effect_value}%积分`:"等级提升1级"),1)],64)):I("",!0),y.key==="action"?(r(),w(p,{key:8,size:0},{default:t(()=>[e(v,{type:"link",onClick:L=>m(b.id)},{default:t(()=>[g("复制")]),_:2},1032,["onClick"]),e(v,{type:"link",onClick:L=>d(!0,b.id)},{default:t(()=>[g("编辑")]),_:2},1032,["onClick"]),e(v,{type:"link",danger:"",onClick:L=>o(b.id,!1)},{default:t(()=>[g("删除")]),_:2},1032,["onClick"])]),_:2},1024)):I("",!0)]}),_:1},8,["data-api","params","columns"]),l(n).editVisible?(r(),w(Ye,{key:0,"edit-id":l(n).editId,onClose:c[2]||(c[2]=y=>l(n).editVisible=!1),onRefresh:u},null,8,["edit-id"])):I("",!0)],64)}}}),tt=M({__name:"GameProps",setup(f){const n=G({searchParams:{search_key:""}}),h=$(),u=()=>h.value.requestTableData(!0);return(d,o)=>{const m=a("a-col"),_=a("a-row"),c=a("a-input"),x=a("a-button"),v=a("a-space"),i=a("PropIcon"),p=a("CustomTable");return r(),T(O,null,[e(_,{justify:"space-between"},{default:t(()=>[e(m,{span:12})]),_:1}),e(p,{ref_key:"RefCustomTable",ref:h,"data-api":l(fe),params:l(n).searchParams,columns:l($e),"no-card":""},{top:t(()=>[e(v,{wrap:""},{default:t(()=>[e(c,{value:l(n).searchParams.search_key,"onUpdate:value":o[0]||(o[0]=s=>l(n).searchParams.search_key=s),placeholder:"请输入item_id或道具名称"},null,8,["value"]),e(x,{type:"primary",onClick:u},{default:t(()=>[g("搜索")]),_:1}),e(x,{onClick:o[1]||(o[1]=()=>{l(n).searchParams.search_key="",u()})},{default:t(()=>[g("重置")]),_:1})]),_:1})]),bodyCell:t(({column:s,record:P})=>[s.key==="image"?(r(),w(i,{key:0,"prop-detail":P,style:{width:"50px",height:"50px"}},null,8,["prop-detail"])):I("",!0)]),_:1},8,["data-api","params","columns"])],64)}}}),at=M({__name:"TripartiteDetail",props:["editId"],emits:["close","refresh"],setup(f,{emit:n}){const h=f;we.extend(Pe);const u=$(!0),d=$(!1),o=()=>{d.value=!0,ye(h.editId).then(i=>{m.value=i,i.online_at&&i.offline_at?(m.value.is_forever=0,m.value.times=[i.online_at,i.offline_at]):(m.value.is_forever=1,m.value.times=void 0)}).finally(()=>d.value=!1)};h.editId&&o();const m=$({id:0,name_key:void 0,item_id:"",image:"",type:2}),_=G({optionsVip:[{label:"三方券",value:2}]}),c=$(!1),x=$(),v=()=>{x.value.validate().then(()=>{c.value=!0;const{id:i,...p}=m.value;h.editId?ve(i,p).then(()=>{n("close"),n("refresh")}).catch(()=>{}).finally(()=>{c.value=!1}):ke(p).then(()=>{n("close"),n("refresh")}).catch(()=>{}).finally(()=>{c.value=!1})}).catch(()=>{})};return(i,p)=>{const s=a("a-select"),P=a("a-form-item"),y=a("a-input"),b=a("SelectLang"),U=a("SelectImg"),k=a("a-button"),S=a("a-form"),L=a("a-spin"),A=a("a-drawer");return r(),w(A,{open:l(u),"onUpdate:open":p[4]||(p[4]=F=>K(u)?u.value=F:null),title:h.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:p[5]||(p[5]=F=>!F&&n("close"))},{default:t(()=>[e(L,{spinning:l(d)},{default:t(()=>[e(S,{model:l(m),name:"basic",ref_key:"formRef",ref:x,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:t(()=>[e(P,{label:"道具类型",name:"type",rules:[{required:!0,message:"请选择道具类型"}]},{default:t(()=>[e(s,{style:{width:"100%"},value:l(m).type,"onUpdate:value":p[0]||(p[0]=F=>l(m).type=F),disabled:!0,options:l(_).optionsVip},null,8,["value","options"])]),_:1}),e(P,{label:"三方券id",name:"item_id",rules:[{required:!0,message:"请输入三方券id"}]},{default:t(()=>[e(y,{value:l(m).item_id,"onUpdate:value":p[1]||(p[1]=F=>l(m).item_id=F),placeholder:"请输入三方券id"},null,8,["value"])]),_:1}),e(P,{label:"道具名称",name:"name_key",rules:[{required:!0,message:"请选择道具名称多语言"}]},{default:t(()=>[e(b,{value:l(m).name_key,"onUpdate:value":p[2]||(p[2]=F=>l(m).name_key=F)},null,8,["value"])]),_:1}),e(P,{label:"道具icon",name:"image",rules:[{required:!0,message:"请上传道具icon"}]},{default:t(()=>[e(U,{value:l(m).image,"onUpdate:value":p[3]||(p[3]=F=>l(m).image=F),"width-height":[60,60]},null,8,["value"])]),_:1}),e(P,{"wrapper-col":{offset:6,span:16}},{default:t(()=>[e(k,{type:"primary",onClick:v,loading:l(c)},{default:t(()=>[g("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),nt=M({__name:"TripartiteProps",setup(f){const n=G({searchParams:{search_key:"",type:2},editVisible:!1,editId:0}),h=$(),u=()=>h.value.requestTableData(!0),d=(m,_)=>{n.editVisible=m,n.editId=_||0},o=(m,_)=>q(`确定要删除${_?"选中的":"此条"}数据吗？`,he,{id:m}).then(()=>u());return(m,_)=>{const c=a("a-col"),x=a("a-row"),v=a("a-input"),i=a("a-button"),p=a("PlusOutlined"),s=a("a-space"),P=a("PropIcon"),y=a("CustomTable");return r(),T(O,null,[e(x,{justify:"space-between"},{default:t(()=>[e(c,{span:12})]),_:1}),e(y,{ref_key:"RefCustomTable",ref:h,"data-api":l(ge),params:l(n).searchParams,columns:l(Re),"no-card":""},{top:t(()=>[e(s,{wrap:""},{default:t(()=>[e(v,{value:l(n).searchParams.search_key,"onUpdate:value":_[0]||(_[0]=b=>l(n).searchParams.search_key=b),placeholder:"请输入道具名称"},null,8,["value"]),e(i,{type:"primary",onClick:u},{default:t(()=>[g("搜索")]),_:1}),e(i,{onClick:_[1]||(_[1]=()=>{l(n).searchParams.search_key="",u()})},{default:t(()=>[g("重置")]),_:1}),e(i,{type:"primary",onClick:_[2]||(_[2]=b=>d(!0))},{icon:t(()=>[e(p)]),default:t(()=>[g(" 新增道具 ")]),_:1})]),_:1})]),bodyCell:t(({column:b,record:U})=>[b.key==="type"?(r(),T(O,{key:0},[g(D(U.type===1?"游戏道具":"三方券"),1)],64)):I("",!0),b.key==="image"?(r(),w(P,{key:1,"prop-detail":U,style:{width:"50px",height:"50px"}},null,8,["prop-detail"])):I("",!0),b.key==="action"?(r(),w(s,{key:2,size:0},{default:t(()=>[e(i,{type:"link",onClick:k=>d(!0,U.id)},{default:t(()=>[g("编辑")]),_:2},1032,["onClick"]),e(i,{type:"link",danger:"",onClick:k=>o(U.id,!1)},{default:t(()=>[g("删除")]),_:2},1032,["onClick"])]),_:2},1024)):I("",!0)]),_:1},8,["data-api","params","columns"]),l(n).editVisible?(r(),w(at,{key:0,"edit-id":l(n).editId,onClose:_[3]||(_[3]=b=>d(!1)),onRefresh:u},null,8,["edit-id"])):I("",!0)],64)}}}),lt={components:{PlatformProps:et,GameProps:tt,TripartiteProps:nt}},ot=M({...lt,__name:"Index",setup(f){const n=[{label:"游戏道具",key:"GameProps"},{label:"三方道具",key:"TripartiteProps"},{label:"平台道具",key:"PlatformProps"}],h=$(n[0].key),u=X(()=>localStorage.getItem("crtGame")==="funplus_zone");return(d,o)=>{const m=a("a-tab-pane"),_=a("a-tabs"),c=a("a-card");return r(),w(c,{class:"mw-child-h-auto"},{default:t(()=>[e(_,{activeKey:l(h),"onUpdate:activeKey":o[0]||(o[0]=x=>K(h)?h.value=x:null),class:"tabs-wrap"},{default:t(()=>[(r(!0),T(O,null,V(n.filter(x=>l(u)?!0:x.key!=="PlatformProps"),x=>(r(),w(m,{tab:x.label,key:x.key,class:"tab-pane"},{default:t(()=>[(r(),w(Ce,null,[x.key===l(h)?(r(),w(Te(x.key),{key:0})):I("",!0)],1024))]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])]),_:1})}}});const it=E(ot,[["__scopeId","data-v-e88bd95d"]]),_t=Object.freeze(Object.defineProperty({__proto__:null,default:it},Symbol.toStringTag,{value:"Module"}));export{ut as I,dt as a,_t as b};
