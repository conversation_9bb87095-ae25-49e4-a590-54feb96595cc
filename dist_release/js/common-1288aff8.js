import{bu as F,h as w,bv as _,_ as R,a as f,o as v,c as E,M as ae,d as O,k as P,b as g,w as h,v as c,e as b,z as V,A as z,j as oe,u as L,m as de,bw as _e,bx as me,by as ge,bz as M,r as he,bA as fe,bB as $,J as U,a9 as ve,ab as x,F as A,l as T,s as Ce,bC as ne,t as j,ac as q,L as Z,K as Y}from"./vendor-f5ec1a19.js";import{m as y,M as ke}from"./antd-a6b46d10.js";const ye=F("system",()=>{const e=w({asideStatus:!1,env:""});return{systemState:e,setAsideStatus:()=>{e.asideStatus=!e.asideStatus},setCrtEnv:s=>{e.env=s}}}),be=[{index:4,path:"/dailyCheck",name:"DailyCheck",redirect:"/dailyCheck/list",meta:{title:"签到活动",icon:"TagsOutlined"},children:[{path:"/dailyCheck/list",name:"DailyCheckList",component:()=>_(()=>import("./dailycheck-08e97356.js").then(e=>e.I),["js/dailycheck-08e97356.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"月签到（旧）"}},{path:"/dailyCheck/detail/:id",name:"DailyCheckDetail",component:()=>_(()=>import("./dailycheck-08e97356.js").then(e=>e.D),["js/dailycheck-08e97356.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"奖励详情",hidden:!0,icon:"",activeMenu:"/dailyCheck/list"}},{path:"/dailyCheck/new",name:"DailyCheckNew",component:()=>_(()=>import("./DailyCheckNew-07c3b82d.js"),["js/DailyCheckNew-07c3b82d.js","js/shared/DailyCheckNew/DailyCheckNewDetail-c47bc7fd.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"月签到（新）"}},{path:"/dailyCheck/new/detail/:id",name:"DailyCheckNewDetail",component:()=>_(()=>import("./DailyCheckNewDetail-ebc058ac.js"),["js/DailyCheckNewDetail-ebc058ac.js","js/shared/DailyCheckNew/DailyCheckNewDetail-c47bc7fd.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"奖励详情",hidden:!0,icon:"",activeMenu:"/dailyCheck/new"}},{path:"/dailyCheck/weekly",name:"DailyCheckWeekly",component:()=>_(()=>import("./DailyCheckWeekly-a0cd0a98.js"),["js/DailyCheckWeekly-a0cd0a98.js","js/shared/DailyCheckWeekly/DailyCheckWeeklyDetail-1f853882.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"周签到"}},{path:"/dailyCheck/weekly/detail/:id",name:"DailyCheckWeeklyDetail",component:()=>_(()=>import("./DailyCheckWeeklyDetail-7df38332.js"),["js/DailyCheckWeeklyDetail-7df38332.js","js/shared/DailyCheckWeekly/DailyCheckWeeklyDetail-1f853882.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"奖励详情",hidden:!0,icon:"",activeMenu:"/dailyCheck/weekly"}},{path:"/dailyCheck/rule",name:"DailyCheckRule",component:()=>_(()=>import("./DailyCheckRule-7773d402.js"),["js/DailyCheckRule-7773d402.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"签到规则"}}]}],Le=Object.freeze(Object.defineProperty({__proto__:null,default:be},Symbol.toStringTag,{value:"Module"})),Pe=[{index:10,path:"/datamanage",name:"DataManage",redirect:"/datamanage/points",meta:{title:"数据明细",icon:"DatabaseOutlined"},children:[{path:"/datamanage/points",name:"DataManagePoints",component:()=>_(()=>import("./datamanage-deae00f6.js").then(e=>e.I),["js/datamanage-deae00f6.js","js/vendor-f5ec1a19.js"]),meta:{title:"积分明细",icon:""}},{path:"/datamanage/growth",name:"DataManageGrowth",component:()=>_(()=>import("./datamanage-deae00f6.js").then(e=>e.a),["js/datamanage-deae00f6.js","js/vendor-f5ec1a19.js"]),meta:{title:"成长值明细",icon:""}}]}],Ee=Object.freeze(Object.defineProperty({__proto__:null,default:Pe},Symbol.toStringTag,{value:"Module"})),Oe=[],Se=Object.freeze(Object.defineProperty({__proto__:null,default:Oe},Symbol.toStringTag,{value:"Module"})),De=[],we=Object.freeze(Object.defineProperty({__proto__:null,default:De},Symbol.toStringTag,{value:"Module"})),Re=[{index:3,path:"/minigame",name:"Minigame",redirect:"/minigame/list",meta:{title:"小游戏",icon:"TrophyOutlined"},children:[{path:"/minigame/list",name:"MinigameList",component:()=>_(()=>import("./minigame-9b833b78.js"),["js/minigame-9b833b78.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"小游戏",icon:"",breadcrumb:!1}}]}],Ie=Object.freeze(Object.defineProperty({__proto__:null,default:Re},Symbol.toStringTag,{value:"Module"})),Te={};function $e(e,t){const n=f("router-view");return v(),E(n)}const Ae=R(Te,[["render",$e]]),Ge=[{index:2,path:"/operation",name:"Operation",redirect:"/operation/sdkvajra",meta:{title:"运营位",icon:"FlagOutlined"},children:[{path:"/operation/sdkvajra",name:"OperationSDKVajra",component:()=>_(()=>import("./operation-db63a910.js").then(e=>e.I),["js/operation-db63a910.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"SDK金刚区",icon:""}},{path:"/operation/h5vajra",name:"OperationH5Vajra",component:()=>_(()=>import("./operation-db63a910.js").then(e=>e.a),["js/operation-db63a910.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"H5金刚区",icon:""}},{path:"/operation/banner",name:"OperationBanner",component:ae(Ae),redirect:"/operation/banner/group",meta:{title:"活动Banner",icon:""},children:[{path:"/operation/banner/group",name:"OperationBannerList",component:()=>_(()=>import("./operation-db63a910.js").then(e=>e.b),["js/operation-db63a910.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"活动Banner",icon:"",breadcrumb:!1}},{path:"/operation/banner/detail/:id",name:"OperationBannerDetail",component:()=>_(()=>import("./operation-db63a910.js").then(e=>e.D),["js/operation-db63a910.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"Banner详情",icon:"",hidden:!0,permission:"OperationBannerList"}}]},{path:"/operation/sdkiconspush",name:"OperationSDKIconsPush",component:()=>_(()=>import("./operation-db63a910.js").then(e=>e.c),["js/operation-db63a910.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"SDK特殊图标推送",icon:"",not_funplus_zone:!0}},{path:"/operation/sdkads",name:"OperationSDKAds",component:()=>_(()=>import("./operation-db63a910.js").then(e=>e.d),["js/operation-db63a910.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"SDK广告位",icon:"",not_funplus_zone:!0}}]}],Me=Object.freeze(Object.defineProperty({__proto__:null,default:Ge},Symbol.toStringTag,{value:"Module"})),je=[{index:1,path:"/platform",name:"Platform",redirect:"/platform/entrance",meta:{title:"平台配置",icon:"GoldOutlined",funplus_zone:!0},children:[{path:"/platform/game",name:"PlatformGame",component:()=>_(()=>import("./platform-9582bfd5.js").then(e=>e.I),["js/platform-9582bfd5.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"游戏接入",icon:""}},{path:"/platform/entrance",name:"PlatformEntrance",component:()=>_(()=>import("./platform-9582bfd5.js").then(e=>e.a),["js/platform-9582bfd5.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"入口管理",icon:""}},{path:"/platform/rule",name:"PlatformRule",component:()=>_(()=>import("./platform-9582bfd5.js").then(e=>e.b),["js/platform-9582bfd5.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"通用规则",icon:""}},{path:"/platform/combinerule",name:"CombineRule",component:()=>_(()=>import("./Index-29029f94.js"),["js/Index-29029f94.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css"]),meta:{title:"融合开关",icon:""}}]}],Fe=Object.freeze(Object.defineProperty({__proto__:null,default:je},Symbol.toStringTag,{value:"Module"})),Ke=[{index:7,path:"/points",name:"Points",redirect:"/points/task",meta:{title:"积分系统",icon:"ProjectOutlined"},children:[{path:"/points/task",name:"PointsTask",component:()=>_(()=>import("./PointsTask-494dce72.js"),["js/PointsTask-494dce72.js","js/antd-a6b46d10.js","js/vendor-f5ec1a19.js","css/antd-25a63267.css","css/Index-70edb989.css"]),meta:{title:"任务体系",icon:""}},{path:"/points/store",name:"PointsStore",component:()=>_(()=>import("./pointsStore-cc1b7869.js"),["js/pointsStore-cc1b7869.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css","css/Index-e24a34d6.css"]),meta:{title:"积分商城",icon:""}}]}],Ue=Object.freeze(Object.defineProperty({__proto__:null,default:Ke},Symbol.toStringTag,{value:"Module"})),Ne=[{index:5,path:"/pushmessage",name:"Pushmessage",redirect:"/pushmessage/list",meta:{title:"消息推送",icon:"MessageOutlined"},children:[{path:"/pushmessage/list",name:"PushmessageList",component:()=>_(()=>import("./pushMessage-836ef181.js"),["js/pushMessage-836ef181.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css","css/Index-95488f3c.css"]),meta:{title:"消息推送",icon:"",breadcrumb:!1}}]}],Be=Object.freeze(Object.defineProperty({__proto__:null,default:Ne},Symbol.toStringTag,{value:"Module"})),Ve=[{index:8,path:"/resource",name:"Resource",redirect:"/resource/language",meta:{title:"资源管理",icon:"DropboxOutlined"},children:[{path:"/resource/language",name:"ResourceLanguage",component:()=>_(()=>import("./resource-9af587fb.js").then(e=>e.I),["js/resource-9af587fb.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css","css/resource-1ccdf5b9.css"]),meta:{title:"多语言管理",icon:""}},{path:"/resource/gift",name:"ResourceGift",component:()=>_(()=>import("./resource-9af587fb.js").then(e=>e.a),["js/resource-9af587fb.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css","css/resource-1ccdf5b9.css"]),meta:{title:"礼包列表",icon:""}},{path:"/resource/props",name:"ResourceProps",component:()=>_(()=>import("./resource-9af587fb.js").then(e=>e.b),["js/resource-9af587fb.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css","css/resource-1ccdf5b9.css"]),meta:{title:"道具表管理",icon:""}}]}],ze=Object.freeze(Object.defineProperty({__proto__:null,default:Ve},Symbol.toStringTag,{value:"Module"})),xe=[{index:6,path:"/rights",name:"Rights",redirect:"/rights/entrance",meta:{title:"权益配置",icon:"RightSquareOutlined"},children:[{path:"/rights/entrance",name:"RightsEntrance",component:()=>_(()=>import("./rights-ca193bbf.js").then(e=>e.I),["js/rights-ca193bbf.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css","css/rights-0c82a52c.css"]),meta:{title:"权益入口",icon:""}},{path:"/rights/gift",name:"RightsGift",component:()=>_(()=>import("./rights-ca193bbf.js").then(e=>e.a),["js/rights-ca193bbf.js","js/vendor-f5ec1a19.js","js/antd-a6b46d10.js","css/antd-25a63267.css","css/rights-0c82a52c.css"]),meta:{title:"专属礼包",icon:"",not_funplus_zone:!0}}]}],We=Object.freeze(Object.defineProperty({__proto__:null,default:xe},Symbol.toStringTag,{value:"Module"})),He=[{index:9,path:"/user",name:"User",redirect:"/user/list",meta:{title:"用户管理",icon:"UsergroupAddOutlined"},children:[{path:"/user/list",name:"UserList",component:()=>_(()=>import("./user-88017ec6.js"),["js/user-88017ec6.js","js/vendor-f5ec1a19.js"]),meta:{title:"用户管理",icon:"",breadcrumb:!1}}]}],qe=Object.freeze(Object.defineProperty({__proto__:null,default:He},Symbol.toStringTag,{value:"Module"})),J=Object.assign({"./dailyCheck.ts":Le,"./dataManage.ts":Ee,"./dev.ts":Se,"./home.ts":we,"./minigame.ts":Ie,"./operation.ts":Me,"./platformConf.ts":Fe,"./points.ts":Ue,"./pushMessage.ts":Be,"./resource.ts":ze,"./rights.ts":We,"./users.ts":qe});let N=[];for(const e in J)N=N.concat(J[e].default);const Ze=N,Ye="/gif/kit-risk-management-forecasting-and-assessment-6a89fdde.gif",I=e=>(V("data-v-1e705d2e"),e=e(),z(),e),Je={class:"wrapper darkAmber"},Qe=I(()=>c("div",{class:"view"},[c("img",{src:Ye,alt:""})],-1)),Xe=I(()=>c("div",{class:"panel-logo"},[c("svg",{width:"100px",height:"25px",viewBox:"0 0 300 75",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",class:"w-100"},[c("g",{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[c("g",{fill:"#FF5A00"},[c("path",{d:"M55.8479648,42.9125049 L55.8479648,62.9227814 C55.8479648,64.4627289 54.6047234,65.7133712 53.0738887,65.7133712 L11.702407,65.7133712 C10.0757079,65.7133712 8.7965173,64.3090355 8.9403139,62.6786801 L12.0798731,26.5908697 C12.2056951,25.1503709 13.4040002,24.0443812 14.8419662,24.0443812 L37.0914951,24.0443812 C36.5462663,22.6641543 36.2466901,21.1573563 36.2466901,19.5812457 C36.2466901,18.0051351 36.5462663,16.4983371 37.0914951,15.1181102 L14.3326866,15.1181102 C8.58082239,15.1181102 3.78161069,19.542069 3.27832257,25.3070778 L0.0428989523,62.5099188 C-0.523300181,69.0343538 4.58747083,74.6456693 11.097263,74.6456693 L53.6281047,74.6456693 C59.7544393,74.6456693 64.7244094,69.6491274 64.7244094,63.4833102 L64.7244094,42.9125049 C63.3523502,43.4609793 61.8544689,43.7623389 60.287685,43.7623389 C58.7209012,43.7623389 57.2200241,43.4609793 55.8479648,42.9125049 Z",id:"Path"}),c("path",{d:"M73.9397415,13.4673005 L70.0663235,13.4673005 C68.027997,13.4673005 66.3752191,11.8145226 66.3752191,9.7761962 L66.3752191,5.9027782 C66.3752191,2.64205363 63.7331655,0 60.4724409,0 C57.2117164,0 54.5696627,2.64205363 54.5696627,5.9027782 L54.5696627,9.7761962 C54.5696627,11.8145226 52.9168848,13.4673005 50.8785584,13.4673005 L47.0051404,13.4673005 C43.7444158,13.4673005 41.1023622,16.1093542 41.1023622,19.3700787 C41.1023622,22.6308033 43.7444158,25.2728569 47.0051404,25.2728569 L50.8785584,25.2728569 C52.9168848,25.2728569 54.5696627,26.9256348 54.5696627,28.9639613 L54.5696627,32.8373793 C54.5696627,36.0981038 57.2117164,38.7401575 60.4724409,38.7401575 C63.7331655,38.7401575 66.3752191,36.0981038 66.3752191,32.8373793 L66.3752191,28.9639613 C66.3752191,26.9256348 68.027997,25.2728569 70.0663235,25.2728569 L73.9397415,25.2728569 C77.2004661,25.2728569 79.8425197,22.6308033 79.8425197,19.3700787 C79.8425197,16.1093542 77.2004661,13.4673005 73.9397415,13.4673005 Z",id:"Path"}),c("path",{d:"M290.974807,40.287923 C287.781185,38.5244652 285.229872,37.0819087 285.229872,34.5986722 C285.229872,32.2743869 286.986513,30.0310767 290.81647,30.0310767 C293.290108,30.0310767 296.002745,31.1527318 296.961727,31.9534856 L298.718369,26.1862588 C298.079047,25.6254313 294.649414,23.6220472 290.338472,23.6220472 C283.075895,23.6220472 277.88962,27.2269388 277.88962,34.1967958 C277.88962,40.0449977 281.322241,43.1700368 285.23286,45.0924457 C289.463139,47.1738058 292.815098,48.8562884 292.815098,51.9033516 C292.815098,55.349292 289.941137,57.2717009 286.272504,57.2717009 C283.398544,57.2717009 280.207909,55.5892183 278.770929,54.4675631 L276.377953,61.5183952 C279.96891,63.6807304 282.442548,64.7244094 286.75349,64.7244094 C294.655389,64.7244094 300,59.3560601 300,52.227252 C299.994025,45.5752973 295.444085,42.7711594 290.974807,40.287923 Z",id:"Path"}),c("path",{d:"M199.113788,24.0944882 L195.96498,24.0944882 L190.743157,24.0944882 L188.503937,24.0944882 L188.503937,64.7244094 L195.96498,64.7244094 L195.96498,50.9325182 L199.11681,50.9325182 C208.070667,50.9325182 213.543307,46.0443676 213.543307,37.5150128 C213.540285,28.9826388 208.067645,24.0944882 199.113788,24.0944882 Z M199.113788,43.4780131 L195.96498,43.4780131 L195.96498,31.5489933 L199.11681,31.5489933 C202.930434,31.5489933 205.251244,33.6201924 205.251244,37.5119936 C205.248223,41.406814 202.927412,43.4780131 199.113788,43.4780131 Z",id:"Shape","fill-rule":"nonzero"}),c("polygon",{id:"Path",points:"226.600431 24.0944882 219.212598 24.0944882 219.212598 64.7244094 221.839782 64.7244094 226.600431 64.7244094 240.472441 64.7244094 240.472441 57.6412709 226.600431 57.6412709"}),c("polygon",{id:"Path",points:"172.128253 48.822069 159.796076 24.0944882 151.653543 24.0944882 151.653543 64.7244094 159.055846 64.7244094 159.055846 39.9998479 171.388023 64.7244094 179.527559 64.7244094 179.527559 24.0944882 172.128253 24.0944882"}),c("path",{d:"M135.249136,51.9500321 C135.249136,55.7694254 132.854357,58.0937472 128.976378,58.0937472 C125.098399,58.0937472 122.70362,55.7694254 122.70362,51.9500321 L122.70362,24.0944882 L115.275591,24.0944882 L115.275591,50.7485272 C115.275591,59.7159301 120.474308,65.1968504 128.976378,65.1968504 C137.478448,65.1968504 142.677165,59.7159301 142.677165,50.7485272 L142.677165,24.0944882 L135.249136,24.0944882 L135.249136,51.9500321 Z",id:"Path"}),c("path",{d:"M264.225514,51.9500321 C264.225514,55.7694254 261.830735,58.0937472 257.952756,58.0937472 C254.074777,58.0937472 251.679998,55.7694254 251.679998,51.9500321 L251.679998,24.0944882 L244.251969,24.0944882 L244.251969,50.7485272 C244.251969,59.7159301 249.450686,65.1968504 257.952756,65.1968504 C266.454826,65.1968504 271.653543,59.7159301 271.653543,50.7485272 L271.653543,24.0944882 L264.225514,24.0944882 L264.225514,51.9500321 Z",id:"Path"}),c("polygon",{id:"Path",points:"88.9916386 24.0944882 86.9291339 24.0944882 86.9291339 64.7244094 94.3523468 64.7244094 94.3523468 47.931393 107.055091 47.931393 107.055091 40.8482545 94.3523468 40.8482545 94.3523468 32.4819387 107.716535 32.4819387 107.716535 24.0944882 94.3523468 24.0944882"})])])]),b(" - "),c("span",{class:"sys-name"},"FunZone")],-1)),et=I(()=>c("div",{class:"panel-style-logo"},null,-1)),tt=I(()=>c("div",{class:"panel-header"},[c("p",null,"私域管理平台")],-1)),at=I(()=>c("i",{class:"icon-feishu"},null,-1)),ot=I(()=>c("span",null,"登录",-1)),nt=O({__name:"Index",setup(e){const t=()=>{location.href="/backend/admin/loginSso?system=1&referer="+encodeURIComponent(`${location.origin}/login`)};return(n,s)=>{const o=f("kinesis-element"),l=f("kinesis-container");return v(),P("div",Je,[g(l,{class:"kinesis-wrap"},{default:h(()=>[g(o,{strength:-8,class:"kinesis-style kinesis-style-1"}),g(o,{strength:10,class:"kinesis-style kinesis-style-2"}),g(o,{strength:-5,class:"kinesis-style kinesis-style-3"}),g(o,{strength:-8,class:"kinesis-style kinesis-style-4"}),g(o,{strength:10,class:"kinesis-style kinesis-style-5"}),g(o,{strength:-5,class:"kinesis-style kinesis-style-6"}),g(o,{strength:-8,class:"kinesis-style kinesis-style-7"}),g(o,{strength:10,class:"kinesis-style kinesis-style-8"}),g(o,{strength:5,class:"kinesis-view"},{default:h(()=>[Qe]),_:1}),c("div",{class:"glass-container kinesis-panel"},[Xe,et,tt,c("div",{class:"panel-btn-main",onClick:t},[at,b(" - "),ot])])]),_:1})])}}});const st=R(nt,[["__scopeId","data-v-1e705d2e"]]),it=O({__name:"Error",setup(e){const t=oe();return(n,s)=>{const o=f("a-button"),l=f("a-space"),i=f("a-result");return v(),E(i,{status:"403",title:"403"},{subTitle:h(()=>[b(" 抱歉，您没有当前页面的权限或页面不存在，请飞书联系管理员: 谷翼涵！ ")]),extra:h(()=>[g(l,null,{default:h(()=>[g(o,{type:"primary",onClick:s[0]||(s[0]=p=>L(t).replace("/"))},{default:h(()=>[b("回到首页")]),_:1})]),_:1})]),_:1})}}});const rt=R(it,[["__scopeId","data-v-11608d83"]]),ct=O({__name:"Redirect",setup(e){const t=de();return oe().replace(t.fullPath.replace("/redirect","")),(s,o)=>(v(),P("div"))}}),lt=[{path:"/login",name:"Login",component:st,meta:{hidden:!0,title:"登录"}},{path:"/redirect/:char(\\S+)+",name:"Redirect",component:ae(ct),meta:{hidden:!0}},{path:"/error",redirect:"/403",meta:{hidden:!0},children:[{path:"/403",name:"Error",component:rt,meta:{hidden:!0}}]}],pt=Object.freeze(Object.defineProperty({__proto__:null,default:lt},Symbol.toStringTag,{value:"Module"})),Q=Object.assign({"./userManager.ts":pt});let B=[];for(const e in Q)B=B.concat(Q[e].default);const ut=B,se=[...ut],dt=[...Ze.sort((e,t)=>e.index-t.index)],D=_e({history:me("/"),routes:se}),C={MULTILINGUAL:"multilingual",PLATFORM_PROPS:"platformProps",DAILY_CHECK:"dailyCheck",LADDERCONFIGS:"ladderConfigs",RIGHTGIFT:"rightGift",TASKCONFIG:"taskConfig",TASKRULE:"taskRule",PRODUCTCONFIG:"productConfig",GIFTCONFIG:"giftConfig",DAILY_CHECK_WEEKLY:"dailyCheckWeekly",DAILY_CHECK_NEW:"dailyCheckNew"};var G=(e=>(e[e.PAGE=1]="PAGE",e[e.BUTTON=3]="BUTTON",e))(G||{}),_t=(e=>(e[e.CLOSE=0]="CLOSE",e[e.OPEN=1]="OPEN",e))(_t||{});const ia=["/login","/403","/error"],ra=[{value:1,img:new URL("/jpeg/banner_type_1-4ffe5097.jpeg",self.location).href,label:"样式一",width:686,height:292},{value:3,img:new URL("/jpeg/banner_type_2-40c10cde.jpeg",self.location).href,label:"样式二",width:686,height:292}],ie=(e=[],t=[])=>{const n=localStorage.getItem("crtGame"),s=[];return t.forEach((o,l)=>{e.forEach((i,p)=>{var u,m;if(!(n==="funplus_zone"&&((u=o.meta)!=null&&u.not_funplus_zone))&&!(n!=="funplus_zone"&&((m=o.meta)!=null&&m.funplus_zone))&&o.name&&i.component_name===o.name&&i.category===G.PAGE){o.meta.id=i.id,o.meta.sort=i.sort,o.key=i.path,i._child=i._child||[];const d=i._child.filter(k=>k.category===G.BUTTON);o.meta.rule={button:d.map(k=>k.component_name.trim())};const r=i._child.filter(k=>k.category===G.PAGE);o.children=ie(r,o.children),o.redirect=o.children[0]?o.children[0].path:"",s.push(o)}})}),s.sort((o,l)=>l.meta.sort-o.meta.sort)},re=e=>{let t={};return e.forEach(n=>{t[n.name]=n.meta.id,n.children&&n.children.length&&(t=Object.assign(t,re(n.children)))}),t},ce=F("permission",()=>{const e=w({routes:[],dynamicRoutes:[],commonBtnPromissions:[],permissionMap:{},subRoutes:[]}),t=i=>{e.permissionMap=i},n=i=>{e.routes=i.concat(se),e.dynamicRoutes=i};return{permissionState:e,setPermissionMap:t,setRoutes:n,setBtnPromise:i=>{i.forEach(p=>{p.name==="通用配置管理"&&p._child&&p._child.forEach(u=>e.commonBtnPromissions.push(u.component_name))})},SET_ROUTES:i=>{const p=ie(i,dt);t(re(p)),n(p)},setSubRoutes:i=>{e.subRoutes=i.filter(p=>{var u;return!((u=p.meta)!=null&&u.hidden)})}}}),mt=e=>{const t=new Blob([e.data],{type:e.headers["content-type"]});if(e.headers["content-type"]==="application/json"){const n=new FileReader;n.readAsText(t,"utf-8"),n.onload=function(){e=JSON.parse(n.result)}}else{const n=(e.headers["content-disposition"]||e.headers["Content-Disposition"]||"").split("filename=")[1],s=document.createElement("a");s.href=window.URL.createObjectURL(t),s.download=n,s.click()}},W=ge.create({timeout:6e4});W.interceptors.request.use(e=>e,e=>(console.log(e),Promise.reject(e)));W.interceptors.response.use(async e=>{if(e.status===200){if(e.config.responseType==="arraybuffer")return mt(e),Promise.resolve("");const t=e.data;if(t.errCode===0||t.code===200){const{setCrtEnv:n}=ye();return t.env&&n(t.env),t.data}else{if(t.code===403)y.error({content:"登录已失效，请重新登录！",onClose:()=>{D.replace("/login")}});else{const n=typeof t.msg=="string"?t.msg:"",s=typeof t.errMsg=="string"?t.errMsg:"",o=n||s;o&&y.error(o)}return Promise.reject(e.data)}}else return X(e),Promise.reject(e.data),e},e=>{const{response:t}=e;return t?(X(t),Promise.reject(t.data)):(y.error("网络连接异常,请稍后再试!"),Promise.reject("网络连接异常,请稍后再试!"))});const X=e=>{switch(e.status){case 401:break;case 403:break;case 404:y.error("网络连接异常,请稍后再试!");break;case 50:break;case 30:y.error({content:"登录已失效，请重新登录！",onClose:()=>{D.replace("/login")}});break;default:y.error(e.msg||e.errMsg)}},ca={isShow:(e,t="button")=>{const n=D.currentRoute;return(n.value.meta.rule&&n.value.meta.rule[t]||[]).indexOf(e)>-1},checkBtnPromission:e=>{const{permissionState:t}=M(ce());return t.value.commonBtnPromissions.indexOf(e)>-1}},ee=e=>e?/^[a-zA-Z0-9_]*$/.test(e):!1,gt=e=>Object.prototype.toString.call(e)==="[object FormData]",la=(e,t,n)=>new Promise((s,o)=>{ke.confirm({title:"提示",content:e,onOk:()=>new Promise((l,i)=>{t(n).then(()=>{y.success("操作成功"),l("操作成功"),s("操作成功")}).catch(()=>i())}),onCancel:()=>{o()}})}),pa=(e,t)=>{const n={f_os:"all",f_channel:"all",lang:"all",f_s_ids:"0-99999",f_lv_ids:"0-99999"};t.forEach(s=>{console.log(e,e.is_filter),e[s]=e.is_filter===1?n[s]:""})},a={get(e,t={}){return a.request("GET",e,{params:S(t)})},post(e,t={}){return a.request("POST",e,{data:S(t)})},put(e,t={}){return a.request("PUT",e,{data:S(t)})},delete(e,t={}){return a.request("DELETE",e,{data:S(t)})},downfile(e,t={}){return a.request("POST",e,{data:S(t),responseType:"arraybuffer"})},downfileGet(e,t={}){return a.request("GET",e,{params:S(t),responseType:"arraybuffer"})},request(e="GET",t,n={}){const{userState:s}=M(le());return t.startsWith("/api")&&s.value.crt_game&&(t=s.value.crt_game.api_server_url+t),t.startsWith("/backend")&&(t=t.replace("/backend","/backend")),new Promise((o,l)=>{W({method:e,url:t,...n}).then(i=>{o(i)}).catch(i=>{l(i)})})}},S=(e={})=>{var o,l;const{userState:t}=M(le()),{permissionState:n}=M(ce());console.log("userState.value.crt_game",t.value.crt_game);const s={admin_project:(o=t.value.crt_game)==null?void 0:o.game_project,game_project:(l=t.value.crt_game)==null?void 0:l.game_project,system:"funplus_zone",ticket:t.value.ticket,permission_id:D.currentRoute.value.meta.permission&&n.value.permissionMap[D.currentRoute.value.meta.permission]||D.currentRoute.value.meta.id||"",user_id:t.value.userInfo.id,user_name:t.value.userInfo.username};return gt(e)?Object.keys(s).forEach(i=>{e.append(i,s[i])}):e={...s,...e},e},ht=e=>a.get("/backend/admin/publicSystemGame",{...e,is_admin:!0}),ft=e=>a.get("/backend/admin/publicChildPermission",{...e,is_admin:!0}),le=F("user",()=>{const e=w({isLogin:!1,ticket:"",loading:!1,userInfo:{email:"",id:void 0,permission_list:[],route_path:[],username:""},commonBtnPromissions:[],game_infos:[]}),t=p=>{e.isLogin=!0,e.userInfo={...p}},n=()=>{const p=localStorage.getItem("ticket");localStorage.removeItem("ticket"),location.href=`/backend/admin/publicLogout?system=1&referer=&ticket=${p}`+encodeURIComponent(`${location.origin}/login`)},s=p=>{e.ticket=p},o=p=>{p.forEach(u=>{u.component==="CommonPromission"&&u._child&&u._child.forEach(m=>{e.commonBtnPromissions.push(m.component)})})};return{userState:e,login:t,logout:n,setTicket:s,saveBtnPromis:o,FETCH_PERMISSION:async()=>new Promise((p,u)=>{if(e.userInfo.permission_list.length)return p("success");e.ticket===""&&u(Error("未检测到ticket!"));const m={ticket:e.ticket};ft(m).then(d=>{o(d.permission_list),t(d),p("success")}).catch(()=>{u(Error("权限请求失败"))})}),FETCH_GAME_PERMISSION:async()=>new Promise((p,u)=>{if(console.log(e.game_infos,"userState.game_infos"),e.ticket==="")return u(Error("未检测到ticket!"));const m={ticket:e.ticket};ht(m).then(d=>{if(e.game_infos=d.map(r=>({...r,gmUrl:{VITE_APP_SYSTEM:"funplus_zone",VITE_APP_ADMIN_CENTER_API:"/backend",BASE_URL:"/",MODE:"release",DEV:!1,PROD:!0,SSR:!1}[`VITE_APP_${r.game_project.toUpperCase()}_GM_URL`]})),console.log(e.game_infos,"userState.game_infos"),e.game_infos.length){const r=localStorage.getItem("crtGame");console.log("localGame",r);const k=e.game_infos.find(ue=>ue.game_project===r),H=e.game_infos[0]||{};console.log("activeGame",k),console.log("filterGame",H);const K=k||H;console.log("crtGame",K),e.crt_game=K,localStorage.setItem("crtGame",K.game_project),p("success")}else localStorage.removeItem("ticket"),u("no game infos")}).catch(()=>{u(Error("权限请求失败"))})})}}),te={[C.PLATFORM_PROPS]:"/api/items/import",[C.MULTILINGUAL]:"/api/lang/upload",[C.DAILY_CHECK]:"/api/active_checkin/update_reward",[C.LADDERCONFIGS]:"/api/vip-right-config/upload-ladder-configs",[C.RIGHTGIFT]:"/api/member-gift/import",[C.TASKCONFIG]:"/api/task-config/import",[C.PRODUCTCONFIG]:"/api/points-mall-product/import",[C.TASKRULE]:"/api/task-config/upload-rule-desc",[C.GIFTCONFIG]:"/api/gift-package/import",[C.DAILY_CHECK_WEEKLY]:"/api/active-checkin-weekly/update-config",[C.DAILY_CHECK_NEW]:"/api/active-checkin-v2/update-config"},ua=(e,t)=>{if(e===C.DAILY_CHECK_WEEKLY||e===C.DAILY_CHECK_NEW){const n=t.get("id");return a.put(`${te[e]}/${n}`,t)}return a.post(te[e],t)},da=e=>a.get("/api/resource/log/list",e),vt=()=>a.get("/api/common/public-enums"),_a=e=>a.downfile("/api/common/downloadTemplate",{file_type:e}),ma=F("config",()=>{const e=he({langs:[],channels:[],platform:[],show_pages:[],prize_type:[],game_projects:[],game_channels:[],task_events:[]});return{configState:e,getConfItem:(o,l)=>e.value[o].find(i=>i.value===l),FETCH_GLOBAL_CONFIG:async(o=!1)=>{!o&&e.value.langs.length||vt().then(l=>{e.value=l})},getGameChannel:o=>{var l;return((l=e.value.game_channels.find(i=>i.game_project===o))==null?void 0:l.sdk_pkg_channels)||[]}}}),ga=fe(),Ct={$on:(...e)=>$.on(...e),$off:(...e)=>$.off(...e),$once:(...e)=>$.once(...e),$emit:(...e)=>$.emit(...e)},ha=e=>a.get("/api/lang",e),fa=()=>a.downfile("/api/lang/export"),va=e=>a.get("/api/lang/keySearch",e),Ca=e=>a.get("/api/lang/keyDetail",e),ka=e=>a.post("/api/common/upload-img",e),kt=e=>a.get("/api/resource/img/list",e),ya=e=>a.get("/api/resource/img/first",e),yt=e=>a.post("/api/resource/img/upload_check",e),bt=e=>a.post("/api/resource/img/upload_batch",e),ba=e=>a.get("/api/gift-package",e),La=()=>a.get("/api/gift-package/enum"),Pa=e=>a.post("/api/gift-package",e),Ea=(e,t)=>a.put(`/api/gift-package/${e}`,t),Oa=e=>a.get(`/api/gift-package/${e}`),Sa=({id:e})=>a.delete(`/api/gift-package/${e}`),Da=()=>a.downfile("/api/gift-package/export"),wa=e=>a.get("/api/item-i18n",e),Ra=({search_key:e,type:t,game_project:n})=>a.get("/api/item-i18n/search",{search_key:e,type:t,game_project:n}),Ia=e=>a.get("/api/item-i18n",e),Ta=e=>a.post("/api/item-i18n",e),$a=(e,t)=>a.put(`/api/item-i18n/${e}`,t),Aa=e=>a.get(`/api/item-i18n/${e}`),Ga=({id:e})=>a.delete(`/api/item-i18n/${e}`),Ma=e=>a.get("/api/platform-item",e),ja=e=>a.get(`/api/platform-item/${e}`),Fa=e=>a.post("/api/platform-item",e),Ka=e=>a.put(`/api/platform-item/${e.id}`,e),Ua=({id:e})=>a.delete(`/api/platform-item/${e}`),Na=({id:e})=>a.put(`/api/platform-item/copy/${e}`),Lt=O({name:"List",emits:["submit","close"],setup(e,t){const n=U("setStep"),s=w({searchParams:{search:"",page:1,page_size:20},crtItem:{},loading:!1,list:[],total:0,imgTypes:[]}),o=()=>{s.searchParams.page=1,l()},l=()=>{const d={...s.searchParams};s.loading=!0,kt(d).then(r=>{s.list=r.data,s.searchParams.page=r.current_page,s.total=r.total}).finally(()=>{s.loading=!1})},i=d=>{s.searchParams.page=d,l()},p=d=>{s.crtItem=d};ve(()=>{l()});const u=()=>{if(!s.crtItem.id)return y.error("请选择一张图片！");t.emit("submit",s.crtItem)},m=()=>t.emit("close");return{...x(s),search:o,setCrt:p,handleCurrentChange:i,setStep:n,close:m,confirm:u}}});const Pt=e=>(V("data-v-644e8c9d"),e=e(),z(),e),Et={class:"img-list"},Ot=Pt(()=>c("div",{class:"left"}," asd ",-1)),St={class:"right"},Dt={class:"filter"},wt={class:"result"},Rt={class:"list"},It=["onClick"],Tt={class:"img"},$t=["src"],At={class:"name"},Gt={class:"pre"},Mt={class:"last"},jt={class:"pagination-wrap"},Ft={class:"footer"};function Kt(e,t,n,s,o,l){const i=f("a-divider"),p=f("a-input-search"),u=f("a-button"),m=f("a-pagination"),d=f("a-space");return v(),P(A,null,[c("div",Et,[e.imgTypes.length>1?(v(),P(A,{key:0},[Ot,g(i,{type:"vertical",style:{height:"100%"}})],64)):T("",!0),c("div",St,[c("div",Dt,[g(p,{value:e.searchParams.search,"onUpdate:value":t[0]||(t[0]=r=>e.searchParams.search=r),placeholder:"请输入图片名称",style:{width:"300px"},"enter-button":"查询",allowClear:"",onSearch:e.search},null,8,["value","onSearch"]),g(u,{onClick:t[1]||(t[1]=r=>e.setStep(2))},{default:h(()=>[b("上传图片")]),_:1})]),c("div",wt,[c("div",Rt,[(v(!0),P(A,null,Ce(e.list,r=>(v(),P("div",{class:ne(["img-item",{active:e.crtItem.id===r.id}]),key:r.id,onClick:k=>e.setCrt(r)},[c("div",Tt,[c("img",{src:r.preview_img},null,8,$t)]),c("div",At,[c("div",Gt,j(r.img_key.length>5?r.img_key.substring(0,r.img_key.length-2):r.img_key),1),c("div",Mt,j(r.img_key.length>5&&r.img_key.substring(r.img_key.length-2)||""),1)])],10,It))),128))]),c("div",jt,[g(m,{current:e.searchParams.page,"onUpdate:current":t[2]||(t[2]=r=>e.searchParams.page=r),"default-page-size":e.searchParams.page_size,total:e.total,onCurrentChange:e.handleCurrentChange},null,8,["current","default-page-size","total","onCurrentChange"])])])])]),c("div",Ft,[g(d,{wrap:""},{default:h(()=>[g(u,{onClick:e.close},{default:h(()=>[b("取消")]),_:1},8,["onClick"]),g(u,{type:"primary",onClick:e.confirm},{default:h(()=>[b("确定")]),_:1},8,["onClick"])]),_:1})])],64)}const Ut=R(Lt,[["render",Kt],["__scopeId","data-v-644e8c9d"]]),Nt=O({name:"Upload",emits:["finish"],setup(e,t){const n=U("setStep"),s=U("isOnlyUpload"),o=w({list:[],loading:!1}),l=(m,d)=>(console.log(d),!1),i=()=>{const m={};for(const d in o.list){const r=o.list[d];if(r.size&&r.size>1024*1024*2)return`文件(${r.name})大小超过大小限制`;if(!ee(r.name.split(".")[0]))return`文件(${r.name})不符合命名规则, 文件名只包含数字、字母、下划线`;if(m[r.name])return`文件(${r.name})重复选择`;m[r.name]=!0}return""},p=async()=>{if(i())return y.error(i());const m=new FormData;q(o.list).map(d=>{m.append("file[]",d.originFileObj)});try{await yt(m),u()}catch(d){d.errCode===50&&d.data.exist.length}},u=async()=>{const m=new FormData;q(o.list).map(d=>{m.append("file[]",d.originFileObj)}),o.loading=!0;try{await bt(m),o.list=[],y.success("操作成功"),t.emit("finish")}catch{}o.loading=!1};return{...x(o),isOnlyUpload:s,imgChange:l,setStep:n,preCheck:p,checkValueLNL:ee}}});const pe=e=>(V("data-v-e3f620ea"),e=e(),z(),e),Bt={key:0},Vt=pe(()=>c("div",{style:{"margin-top":"8px"}},"选择图片",-1)),zt={class:"img-item"},xt={class:"image"},Wt=["src"],Ht=["title"],qt={class:"pre"},Zt={class:"last"},Yt=pe(()=>c("div",{class:"tips"},"单次最多上传 10 张图片, 且每张图片不可大于 2M, 文件名必须是字母、数字和下划线!",-1)),Jt={class:"footer"};function Qt(e,t,n,s,o,l){const i=f("plus-outlined"),p=f("CloseCircleOutlined"),u=f("a-upload"),m=f("a-button"),d=f("a-space");return v(),P(A,null,[g(u,{class:"upload-wrap",ref:"update",action:"#","list-type":"picture-card","file-list":e.list,"onUpdate:fileList":t[0]||(t[0]=r=>e.list=r),"before-upload":e.imgChange,"max-count":10,accept:"image/gif,image/jpeg,image/jpg,image/png,image/svg",multiple:!0},{previewIcon:h(()=>[]),itemRender:h(({file:r,actions:{remove:k}})=>[c("div",zt,[c("div",xt,[c("img",{src:r.thumbUrl},null,8,Wt)]),c("div",{class:ne(["title",{error:!e.checkValueLNL(r.name.split(".")[0])}]),title:r.name},[c("div",qt,j(r.name.length>10?r.name.substring(0,r.name.length-6):r.name),1),c("div",Zt,j(r.name.length>10&&r.name.substring(r.name.length-6)||""),1)],10,Ht),g(p,{class:"del-icon",onClick:k},null,8,["onClick"])])]),tip:h(()=>[Yt]),default:h(()=>[e.list.length<10?(v(),P("div",Bt,[g(i),Vt])):T("",!0)]),_:1},8,["file-list","before-upload"]),c("div",Jt,[g(d,{wrap:""},{default:h(()=>[e.isOnlyUpload?T("",!0):(v(),E(m,{key:0,onClick:t[1]||(t[1]=r=>e.setStep(1))},{default:h(()=>[b("返回")]),_:1})),g(m,{type:"primary",onClick:e.preCheck,loading:e.loading,disabled:e.list.length===0},{default:h(()=>[b("确定")]),_:1},8,["onClick","loading","disabled"])]),_:1})])],64)}const Xt=R(Nt,[["render",Qt],["__scopeId","data-v-e3f620ea"]]),ea=O({__name:"Index",setup(e){let t;const n=w({visible:!1,step:1,isOnlyUpload:!1}),s=()=>{n.visible=!1},o=p=>{n.step=p};Z("isOnlyUpload",n.isOnlyUpload),Z("setStep",o);const l=p=>{t&&t(p),s()},i=()=>{n.step===2?n.step=1:(t&&t(),s())};return Ct.$on("showPhotoGallery",p=>{t=p.callback,n.isOnlyUpload=p.isOnlyUpload||n.isOnlyUpload,n.visible=!0}),(p,u)=>{const m=f("a-modal");return v(),E(m,{open:L(n).visible,"onUpdate:open":u[0]||(u[0]=d=>L(n).visible=d),title:L(n).step===1?"图片库":"上传图片",width:"923px","destroy-on-close":"","wrap-class-name":"fp-mall-admin",footer:null},{default:h(()=>[!L(n).isOnlyUpload&&L(n).step===1?(v(),E(Ut,{key:0,onSubmit:l,onClose:s})):T("",!0),L(n).isOnlyUpload||L(n).step===2?(v(),E(Xt,{key:1,ref:"upload",onFinish:i},null,512)):T("",!0)]),_:1},8,["open","title"])}}}),Ba=Object.freeze(Object.defineProperty({__proto__:null,default:ea},Symbol.toStringTag,{value:"Module"})),ta=O({name:"SelectWithAll",props:{value:{type:String,default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"请选择"}},emits:["update:value","change"],setup(e,t){const n=Y({get:()=>{if(e.value)return e.value==="all"?["all"]:e.value.split("|").filter(o=>o)},set:o=>{let l;!o||o.length===0?l=void 0:o[0]==="all"?l="all":l=`|${o.join("|")}|`,t.emit("update:value",l),t.emit("change",l)}}),s=Y(()=>{const o=e.options.map(i=>typeof i=="string"?{label:i,value:i}:i),l=[{label:"ALL",value:"all"}];return e.value==="all"?l:e.value?o:[...l,...o]});return{...x(e),modelValue:n,computedOptions:s}}});function aa(e,t,n,s,o,l){const i=f("a-select");return v(),E(i,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=p=>e.modelValue=p),options:e.computedOptions,placeholder:e.placeholder,mode:"multiple","allow-clear":""},null,8,["value","options","placeholder"])}const oa=R(ta,[["render",aa]]),Va=Object.freeze(Object.defineProperty({__proto__:null,default:oa},Symbol.toStringTag,{value:"Module"})),za=e=>a.get("/api/active_checkin",e),xa=e=>a.get("/api/active_checkin/check_detail",{id:e}),Wa=e=>a.post("/api/active_checkin/update",e),Ha=e=>a.delete("/api/active_checkin",e),qa=e=>a.get("/api/active_checkin/copy",e),Za=e=>a.get("/api/active_checkin/enable",e),Ya=e=>a.get("/api/active_checkin/detail",e),Ja=e=>a.downfile("/api/active_checkin/export",e),Qa=e=>a.get("/api/active-checkin-v2",e),Xa=e=>a.get(`/api/active-checkin-v2/${e}`),eo=e=>a.post("/api/active-checkin-v2",e),to=e=>{const t=e.get("id");return a.put(`/api/active-checkin-v2/${t}`,e)},ao=e=>a.delete(`/api/active-checkin-v2/${e.id}`,e),oo=e=>a.put(`/api/active-checkin-v2/copy/${e.id}`,e),no=e=>a.put(`/api/active-checkin-v2/enable/${e.id}`,e),so=e=>a.get(`/api/active-checkin-v2/config-detail/${e.id}`,e),io=e=>a.downfileGet(`/api/active-checkin-v2/export/${e.id}`,e),ro=e=>a.get("/api/active-checkin-weekly",e),co=e=>a.get(`/api/active-checkin-weekly/${e}`),lo=e=>a.post("/api/active-checkin-weekly",e),po=e=>{const t=e.get("id");return a.put(`/api/active-checkin-weekly/${t}`,e)},uo=e=>a.delete(`/api/active-checkin-weekly/${e.id}`,e),_o=e=>a.put(`/api/active-checkin-weekly/copy/${e.id}`,e),mo=e=>a.put(`/api/active-checkin-weekly/enable/${e.id}`,e),go=e=>a.get(`/api/active-checkin-weekly/config-detail/${e.id}`,e),ho=e=>a.downfileGet(`/api/active-checkin-weekly/export/${e.id}`,e),fo=e=>a.get("/api/game-platform",e),vo=e=>a.post("/api/game-platform",e),Co=(e,t)=>a.put(`/api/game-platform/${e}`,t),ko=e=>a.get(`/api/game-platform/${e}`),yo=({id:e})=>a.delete(`/api/game-platform/${e}`),bo=(e,t)=>a.put(`/api/game-platform/set-status/${e}`,{status:t}),Lo=e=>a.get("/api/master-switch",e),Po=e=>a.post("/api/master-switch",e),Eo=(e,t)=>a.put(`/api/master-switch/${e}`,t),Oo=e=>a.get(`/api/master-switch/${e}`),So=({id:e})=>a.delete(`/api/master-switch/${e}`),Do=(e,t)=>a.put(`/api/master-switch/set-status/${e}`,{status:t}),wo=e=>a.get("/api/rule",e),Ro=e=>a.get("/api/rule/keyDetail",{key:e}),Io=e=>a.put("/api/rule/update",e),To=e=>a.post("/api/rule/upload",e),$o=e=>a.get("/api/merge-switch",e),Ao=e=>a.post("/api/merge-switch",e),Go=(e,t)=>a.put(`/api/merge-switch/${e}`,t),Mo=e=>a.get(`/api/merge-switch/${e}`),jo=({id:e})=>a.delete(`/api/merge-switch/${e}`),Fo=(e,t)=>a.put(`/api/merge-switch/set-status/${e}`,{status:t});export{Ra as $,La as A,Ca as B,xa as C,Wa as D,za as E,Za as F,Ha as G,qa as H,Ja as I,Ya as J,pa as K,ha as L,ra as M,ko as N,Co as O,C as P,vo as Q,fo as R,bo as S,yo as T,Oo as U,Eo as V,Po as W,Lo as X,Do as Y,So as Z,fa as _,eo as a,Oa as a0,Ea as a1,Pa as a2,Da as a3,ba as a4,Sa as a5,ja as a6,Ka as a7,Fa as a8,Ma as a9,Mo as aA,Go as aB,Ao as aC,$o as aD,Fo as aE,jo as aF,Ua as aa,Na as ab,wa as ac,Aa as ad,$a as ae,Ta as af,Ia as ag,Ga as ah,le as ai,D as aj,ce as ak,da as al,ye as am,ea as an,ia as ao,ua as ap,Ba as aq,Ct as ar,ya as as,ka as at,va as au,oa as av,Va as aw,_t as ax,ca as ay,ga as az,Qa as b,ao as c,_a as d,oo as e,io as f,Xa as g,so as h,Ro as i,Io as j,To as k,wo as l,la as m,co as n,po as o,lo as p,ro as q,mo as r,no as s,uo as t,to as u,_o as v,ho as w,go as x,a as y,ma as z};
