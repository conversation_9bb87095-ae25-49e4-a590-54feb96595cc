import{d as x,m as J,bz as R,K as V,a as i,u as g,o as u,c as h,w as l,b as p,l as M,_ as P,h as G,y as W,a9 as ge,v,q as _e,k as y,F as L,s as q,t as $,bF as Oe,r as A,bC as he,e as S,z as ve,A as ye,j as $e,i as Ie,bE as oe,a0 as xe,f as H,g as be,bG as De,bH as Fe,bI as Le,bJ as Te,bK as ze,bL as je,bd as Me,bM as Pe,ak as Ue,ad as Ee,ap as He,bN as Re,bO as Be,bP as Ae,bQ as Ke,bR as Ve,bp as qe,ai as Ge,bq as Ne,bS as Ye,bm as We,ar as Qe,am as Je,aq as Xe,bT as Ze,aU as et,bU as tt,bV as ot,bW as nt,bg as at,bX as lt,bY as st,bZ as it,b_ as rt,b$ as ct,c0 as ut,c1 as dt,ae as pt,c2 as _t,c3 as mt,c4 as ft,c5 as gt,c6 as ht,c7 as vt,aG as yt,c8 as bt,c9 as wt,aJ as St,ca as kt,cb as Ct,ah as Ot,cc as $t,cd as It,ce as xt,cf as Dt,cg as Ft,ch as Lt,ci as Tt,cj as zt,ck as jt,cl as Mt,cm as Y,aN as Pt,x as Ut,cn as Et,co as N,cp as Ht,a5 as re,cq as Rt,cr as Bt,M as At,O as Kt,cs as Vt,p as ce,a8 as ue,ab as Q,bl as qt,ct as Gt,Z as Nt}from"./vendor-f5ec1a19.js";import{m as Z,z as Yt,A as Wt,i as Qt,a as Jt,B as Xt,b as Zt,c as eo,C as to,d as oo,e as no,f as ao,g as lo,h as so,D as io,j as ro,k as co,l as uo,n as po,o as _o,E as mo,p as fo,F as de,I as go,q as ho,r as vo,s as yo,t as bo,M as wo,P as So,u as ko,R as Co,G as Oo,v as $o,w as Io,x as xo,V as Do,S as Fo,y as Lo,H as To,J as zo,K as jo,T as Mo,L as Po,N as Uo,O as Eo,Q as Ho,U as Ro,W as Bo,X as Ao,Y as Ko}from"./antd-a6b46d10.js";import{ai as ee,aj as K,ak as ne,al as Vo,am as we,an as qo,z as pe,ao as Go,ap as No,d as Yo,aq as Wo,ar as Qo,as as Jo,at as Xo,au as Zo,B as Se,av as en,aw as tn,ax as on,ay as nn,az as an}from"./common-1288aff8.js";const ln={components:{Button:{paddingContentHorizontal:10},Table:{colorBorderSecondary:"#e8e8e8",colorFillAlter:"#f7f7f7"},Menu:{colorItemBgHover:"rgba(255, 255, 255, .1)"},Breadcrumb:{colorBgTextHover:"transparent"}},token:{fontFamily:'"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif'}},sn="/png/fp-logo-1690b683.png",rn=x({__name:"Help",setup(e){const t=J(),o=()=>{window.open(_.value)},{userState:n}=R(ee()),_=V(()=>{if(t.meta.help){if(typeof t.meta.help=="string")return t.meta.help;if(n.value.crt_game&&t.meta.help[n.value.crt_game.game_project])return t.meta.help[n.value.crt_game.game_project]}else return"";return""});return(d,r)=>{const c=i("QuestionCircleOutlined"),a=i("a-button"),s=i("a-tooltip");return g(_)?(u(),h(s,{key:0,title:"帮助"},{default:l(()=>[p(a,{type:"text",onClick:o,class:"tools-item"},{icon:l(()=>[p(c)]),_:1})]),_:1})):M("",!0)}}});const cn=P(rn,[["__scopeId","data-v-ff9a49c8"]]),un=["onClick"],dn={key:0,class:"no-redirect"},pn=["onClick"],_n=x({__name:"Index",setup(e){const t=J(),o=r=>{const{params:c}=t;return Oe(r)(c)},n=G({breadcrumbs:[],getBreadcrumb:()=>{const r=t.matched.filter(c=>c.meta&&c.meta.title);n.breadcrumbs=r.filter(c=>c.meta&&c.meta.title&&c.meta.breadcrumb!==!1)},isDashboard(r){const c=r&&r.name;return c?c.toString().trim().toLocaleLowerCase()==="Dashboard".toLocaleLowerCase():!1},handleLink(r){const{redirect:c,path:a}=r;if(c){K.push(c).catch(s=>{console.warn(s)});return}K.push(o(a)).catch(s=>{console.warn(s)})}});W(()=>t.path,r=>{r.startsWith("/redirect/")||n.getBreadcrumb()});const{permissionState:_}=R(ne()),d=()=>{K.push(_.value.routes.filter(r=>!r.meta||!r.meta.hidden)[0].path)};return ge(()=>{n.getBreadcrumb()}),(r,c)=>{const a=i("home-outlined"),s=i("a-breadcrumb-item"),m=i("a-breadcrumb");return u(),h(m,{class:"app-breadcrumb"},{default:l(()=>[p(s,null,{default:l(()=>[v("a",{onClick:_e(d,["prevent"])},[p(a)],8,un)]),_:1}),(u(!0),y(L,null,q(g(n).breadcrumbs,(f,I)=>(u(),h(s,{key:f.path},{default:l(()=>[f.redirect==="noredirect"||I===g(n).breadcrumbs.length-1?(u(),y("span",dn,$(f.meta.title),1)):(u(),y("a",{key:1,onClick:_e(C=>g(n).handleLink(f),["prevent"])},$(f.meta.title),9,pn))]),_:2},1024))),128))]),_:1})}}});const mn=P(_n,[["__scopeId","data-v-68c3f351"]]),fn=["innerHTML"],gn={class:"pagination"},hn=x({__name:"LogDrawer",setup(e){const t=G({visible:!1,containerH:window.innerHeight-206,tableData:[],loading:!1,page:1,pagesize:20,total:0,showRoute:["/tool/propQuery"]}),o=[{title:"日期",dataIndex:"created_at",key:"created_at",width:"160px"},{title:"操作人员",dataIndex:"user_name",key:"user_name",width:"100px"},{title:"事件",dataIndex:"op_content",key:"op_content",width:"100px"},{title:"内容",dataIndex:"op_remark",key:"op_remark",ellipsis:!0}],n=J(),_=V(()=>{var s;return`用户操作日志 - ${(s=n.meta)==null?void 0:s.title}`});W(()=>t.visible,a=>{a?d():r()});const d=async()=>{const a={page:t.page,page_size:t.pagesize};t.loading=!0;try{const s=await Vo(a);t.tableData=s.data,t.total=s.total}catch(s){console.log("日志数据获取异常",s)}t.loading=!1},r=()=>{t.tableData=[],t.page=1,t.total=0},c=a=>{t.page=a,d()};return(a,s)=>{const m=i("FileTextOutlined"),f=i("a-button"),I=i("a-tooltip"),C=i("a-table"),O=i("a-pagination"),b=i("a-drawer");return u(),y(L,null,[g(t).showRoute.includes(g(n).path)?M("",!0):(u(),h(I,{key:0,title:"日志"},{default:l(()=>[p(f,{type:"text",onClick:s[0]||(s[0]=z=>g(t).visible=!0),class:"tools-item"},{icon:l(()=>[p(m)]),_:1})]),_:1})),p(b,{open:g(t).visible,"onUpdate:open":s[3]||(s[3]=z=>g(t).visible=z),title:g(_),placement:"right",width:"700px"},{default:l(()=>[p(C,{"data-source":g(t).tableData,columns:o,size:"small",pagination:!1,loading:g(t).loading,scroll:{y:g(t).containerH}},{bodyCell:l(({column:z,text:k})=>[z.dataIndex==="op_remark"?(u(),y("div",{key:0,class:"log-content",innerHTML:k,style:{"padding-right":"4px","white-space":"break-spaces"}},null,8,fn)):M("",!0)]),_:1},8,["data-source","loading","scroll"]),v("div",gn,[p(O,{size:"small",current:g(t).page,"onUpdate:current":s[1]||(s[1]=z=>g(t).page=z),"page-size":g(t).pagesize,"onUpdate:pageSize":s[2]||(s[2]=z=>g(t).pagesize=z),total:g(t).total,onChange:c,layout:"total, prev, pager, next, sizes"},null,8,["current","page-size","total"])])]),_:1},8,["open","title"])],64)}}});const vn=P(hn,[["__scopeId","data-v-17dc3b45"]]);function yn(){const e=A(Math.abs(window.screen.height-window.document.documentElement.clientHeight)<=17),t=()=>{if(e.value)document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():Z.error({content:"请升级浏览器!",duration:3}),e.value=!1;else{var o=document.documentElement;o.requestFullscreen?o.requestFullscreen():o.mozRequestFullScreen?o.mozRequestFullScreen():o.webkitRequestFullScreen?o.webkitRequestFullScreen():o.msRequestFullscreen?o.msRequestFullscreen():Z.error({content:"请升级浏览器!",duration:3}),e.value=!0}};return document.addEventListener("fullscreenchange",function(){document.fullscreenElement?(console.log("进入全屏模式"),e.value=!0):(console.log("退出全屏模式"),e.value=!1)}),window.onresize=function(){e.value=Math.abs(window.screen.height-window.document.documentElement.clientHeight)<=17},window.addEventListener("keydown",function(o){o=o||window.event,(o.keyCode===122||o.code==="F11")&&!e.value&&(o.preventDefault(),t())}),[e,t]}const bn=x({__name:"GameList",setup(e){const t=ee(),{userState:o}=R(t),n=_=>{const d=localStorage.getItem("crtGame");console.log("item === ",_,d),_.game_project!==d&&(localStorage.setItem("crtGame",_.game_project),window.location.reload())};return(_,d)=>{var C;const r=i("a-avatar"),c=i("CaretDownFilled"),a=i("a-button"),s=i("a-space"),m=i("a-menu-item"),f=i("a-menu"),I=i("a-dropdown");return(C=g(o).crt_game)!=null&&C.game_name?(u(),h(I,{key:0,placement:"bottom",arrow:"",class:"tools-item"},{overlay:l(()=>[p(f,{class:"game-list"},{default:l(()=>[(u(!0),y(L,null,q(g(o).game_infos,(O,b)=>(u(),h(m,{key:b,onClick:z=>n(O),class:he({"game-item":!0,active:g(o).crt_game.game_project===O.game_project})},{default:l(()=>[p(s,null,{default:l(()=>[p(r,{src:O.game_icon_url,size:"small"},null,8,["src"]),S(" "+$(O.game_name),1)]),_:2},1024)]),_:2},1032,["onClick","class"]))),128))]),_:1})]),default:l(()=>[p(a,{type:"text"},{default:l(()=>[p(r,{src:g(o).crt_game.game_icon_url,size:"small",style:{"margin-right":"5px"}},null,8,["src"]),S(" "+$(g(o).crt_game.game_name)+" ",1),p(c,{style:{"font-size":"12px","margin-left":"5px"}})]),_:1})]),_:1})):M("",!0)}}});const wn=P(bn,[["__scopeId","data-v-6e892279"]]),ke=e=>(ve("data-v-7db46132"),e=e(),ye(),e),Sn=ke(()=>v("img",{src:sn,alt:"",height:"34"},null,-1)),kn=[Sn],Cn={class:"tools-item"},On=ke(()=>v("div",{class:"menu-list"},null,-1)),$n=x({__name:"header",setup(e){const t=we(),{setAsideStatus:o}=t,{systemState:n}=R(t),[_,d]=yn(),r=J(),c=()=>{K.replace(`/redirect${r.fullPath}`)},a=ee(),{logout:s}=a,{userState:m}=R(a),{permissionState:f}=R(ne()),I=()=>{K.push(f.value.dynamicRoutes[0].path)};return(C,O)=>{const b=i("MenuUnfoldOutlined"),z=i("MenuFoldOutlined"),k=i("a-button"),T=i("ReloadOutlined"),U=i("a-tag"),w=i("a-space"),F=i("CompressOutlined"),D=i("ExpandOutlined"),E=i("a-tooltip"),B=i("CaretDownFilled"),le=i("PoweroffOutlined"),j=i("a-menu-item"),X=i("a-menu"),se=i("a-dropdown"),ie=i("a-layout-header");return u(),h(ie,{class:"header"},{default:l(()=>[v("div",{class:"logo",onClick:I},kn),p(w,{size:1},{default:l(()=>[p(k,{class:"tools-item",type:"text",onClick:g(o)},{icon:l(()=>[g(n).asideStatus?(u(),h(b,{key:0})):(u(),h(z,{key:1}))]),_:1},8,["onClick"]),p(k,{class:"tools-item",type:"text",onClick:c},{icon:l(()=>[p(T)]),_:1}),v("div",Cn,[p(U,{color:"#ff4900",style:{"font-weight":"500",margin:"0"}},{default:l(()=>[S($(g(n).env),1)]),_:1})]),p(mn)]),_:1}),On,p(w,{size:1},{default:l(()=>[p(cn),p(vn),p(E,null,{title:l(()=>[S($(g(_)?"退出全屏":"全屏"),1)]),default:l(()=>[p(k,{class:"tools-item",type:"text",onClick:g(d)},{icon:l(()=>[g(_)?(u(),h(F,{key:0})):(u(),h(D,{key:1}))]),_:1},8,["onClick"])]),_:1}),p(wn),p(se,{placement:"bottom",arrow:"",class:"tools-item"},{overlay:l(()=>[p(X,null,{default:l(()=>[p(j,null,{default:l(()=>[p(w,null,{default:l(()=>[p(le),v("a",{href:"javascript:;",onClick:O[0]||(O[0]=(...Ce)=>g(s)&&g(s)(...Ce))},"退出登录")]),_:1})]),_:1})]),_:1})]),default:l(()=>[p(k,{type:"text"},{default:l(()=>[S($(g(m).userInfo.username)+" ",1),p(B,{style:{"font-size":"12px","margin-left":"5px"}})]),_:1})]),_:1})]),_:1})]),_:1})}}});const In=P($n,[["__scopeId","data-v-7db46132"]]),xn={class:"layout-sider-content"},Dn={class:"menu-scroller"},Fn=x({__name:"asider",setup(e){const{systemState:t}=R(we()),{permissionState:o}=R(ne()),n=s=>s.meta&&s.meta.hidden,_=G({openKeys:[],preOpenKeys:[],filterRoutes:[]}),d=J(),r=$e(),c=A([]),a=()=>{d.matched.length>0&&(!t.value.asideStatus&&(_.openKeys=[d.matched[0].path]),c.value=[],d.meta.activeMenu?c.value.push(d.meta.activeMenu||d.path):(d.matched.forEach(s=>c.value.push(s.path)),c.value.push(d.path)))};return W(()=>_.openKeys,(s,m)=>{_.preOpenKeys=m}),W(d,s=>a()),a(),(s,m)=>{const f=i("a-menu-item"),I=i("a-sub-menu"),C=i("a-menu"),O=i("a-layout-sider");return u(),h(O,{collapsed:g(t).asideStatus,"onUpdate:collapsed":m[1]||(m[1]=b=>g(t).asideStatus=b),trigger:null,collapsible:"","collapsed-width":"50"},{default:l(()=>[v("div",xn,[v("div",Dn,[p(C,{selectedKeys:g(c),"onUpdate:selectedKeys":m[0]||(m[0]=b=>Ie(c)?c.value=b:null),openKeys:g(_).openKeys,class:"header-menu",mode:"inline",theme:"dark"},{default:l(()=>[(u(!0),y(L,null,q(g(o).routes,b=>{var z;return u(),y(L,{key:b.path},[n(b)?M("",!0):(u(),y(L,{key:0},[b.children&&b.children.filter(k=>{var T;return!((T=k.meta)!=null&&T.hidden)}).length>1?(u(),h(I,{key:b.path,title:(z=b.meta)==null?void 0:z.title},{icon:l(()=>{var k;return[(k=b.meta)!=null&&k.icon?(u(),h(oe(b.meta.icon),{key:0})):M("",!0)]}),default:l(()=>[(u(!0),y(L,null,q(b.children.filter(k=>{var T;return!((T=k.meta)!=null&&T.hidden)}),k=>(u(),h(f,{key:k.path,onClick:T=>g(r).push(k.path)},{default:l(()=>{var T,U;return[(T=k.meta)!=null&&T.icon?(u(),h(oe(k.meta.icon),{key:0})):M("",!0),S(" "+$((U=k.meta)==null?void 0:U.title),1)]}),_:2},1032,["onClick"]))),128))]),_:2},1032,["title"])):(u(),y(L,{key:1},[n(b)?M("",!0):(u(),h(f,{key:b.path,onClick:k=>g(r).push(b.path)},{default:l(()=>{var k,T;return[(k=b.meta)!=null&&k.icon?(u(),h(oe(b.meta.icon),{key:0})):M("",!0),v("span",null,$((T=b.meta)==null?void 0:T.title),1)]}),_:2},1032,["onClick"]))],64))],64))],64)}),128))]),_:1},8,["selectedKeys","openKeys"])])])]),_:1},8,["collapsed"])}}});const Ln=P(Fn,[["__scopeId","data-v-df68c6bb"]]),Tn={class:"home-wrap"},zn=x({__name:"Index",setup(e){return(t,o)=>{const n=i("a-layout-content"),_=i("router-view"),d=i("a-layout");return u(),y("div",Tn,[p(d,null,{default:l(()=>[p(In),p(d,{class:"content-wrap"},{default:l(()=>[p(Ln),p(_,null,{default:l(({Component:r,route:c})=>[p(xe,{name:"fade-transform",mode:"out-in"},{default:l(()=>[(u(),h(n,{class:"page-content",key:c.fullPath},{default:l(()=>[(u(),y("div",{class:"main-wrap",key:c.fullPath},[(u(),h(oe(r),{key:c.fullPath}))]))]),_:2},1024))]),_:2},1024)]),_:1})]),_:1})]),_:1})])}}});const jn=P(zn,[["__scopeId","data-v-9b01a85c"]]),Mn=x({__name:"App",setup(e){H.extend(be).locale("zh-cn");const t=["/login","/","/403"],o=J(),n=ee(),{userState:_}=R(n);return(d,r)=>{const c=i("router-view"),a=i("a-watermark"),s=i("a-config-provider");return u(),h(s,{theme:g(ln),locale:g(Yt)},{default:l(()=>[p(a,De({content:g(_).userInfo.email},{font:{color:"rgba(0,0,0,0.03)"},gap:[50,50]}),{default:l(()=>[t.indexOf(g(o).path)>-1?(u(),h(c,{key:0})):(u(),h(jn,{key:1})),p(qo)]),_:1},16,["content"])]),_:1},8,["theme","locale"])}}}),Pn=Object.freeze(Object.defineProperty({__proto__:null,Affix:Wt,Alert:Qt,Avatar:Jt,Badge:Xt,Breadcrumb:Zt,Button:eo,Card:to,Checkbox:oo,CheckboxGroup:no,Col:ao,Collapse:lo,ConfigProvider:so,DatePicker:io,Descriptions:ro,DescriptionsItem:co,Divider:uo,Drawer:po,Dropdown:_o,Empty:mo,Flex:fo,Form:de,Image:go,Input:ho,InputNumber:vo,Layout:yo,Menu:bo,Modal:wo,Pagination:So,Popover:ko,Radio:Co,RadioGroup:Oo,RangePicker:$o,Result:Io,Row:xo,Select:Do,SelectOptGroup:Fo,SelectOption:Lo,Space:To,Spin:zo,Switch:jo,TabPane:Mo,Table:Po,Tabs:Uo,Tag:Eo,Tooltip:Ho,Tree:Ro,Typography:Bo,Upload:Ao,Watermark:Ko},Symbol.toStringTag,{value:"Module"})),Un=Object.freeze(Object.defineProperty({__proto__:null,AccountBookOutlined:Fe,ApiOutlined:Le,AppstoreOutlined:Te,ArrowDownOutlined:ze,ArrowUpOutlined:je,CaretDownFilled:Me,CaretRightOutlined:Pe,CheckCircleFilled:Ue,CloseCircleFilled:Ee,CloseCircleOutlined:He,CloudDownloadOutlined:Re,CloudUploadOutlined:Be,ClusterOutlined:Ae,CompressOutlined:Ke,DatabaseOutlined:Ve,DeleteOutlined:qe,DownOutlined:Ge,DownloadOutlined:Ne,DropboxOutlined:Ye,EditOutlined:We,EllipsisOutlined:Qe,ExclamationCircleFilled:Je,ExclamationCircleOutlined:Xe,ExpandOutlined:Ze,EyeOutlined:et,FileTextOutlined:tt,FireOutlined:ot,FlagOutlined:nt,FolderOpenOutlined:at,ForkOutlined:lt,FormOutlined:st,GiftOutlined:it,GoldOutlined:rt,GroupOutlined:ct,HomeOutlined:ut,InboxOutlined:dt,LoadingOutlined:pt,LogoutOutlined:_t,MenuFoldOutlined:mt,MenuUnfoldOutlined:ft,MessageOutlined:gt,NodeIndexOutlined:ht,PayCircleOutlined:vt,PlusOutlined:yt,PoweroffOutlined:bt,ProjectOutlined:wt,QuestionCircleOutlined:St,ReloadOutlined:kt,RightSquareOutlined:Ct,SearchOutlined:Ot,SettingOutlined:$t,SlidersOutlined:It,SolutionOutlined:xt,TagsOutlined:Dt,TeamOutlined:Ft,ToolOutlined:Lt,TrophyOutlined:Tt,UploadOutlined:zt,UsergroupAddOutlined:jt,WarningOutlined:Mt},Symbol.toStringTag,{value:"Module"})),me=Un,fe=Pn;function En(e){for(const t in fe){const o=fe[t];o.install&&e.use(o)}for(const t in me)e.component(t,me[t])}const Hn=Object.freeze(Object.defineProperty({__proto__:null,default:En},Symbol.toStringTag,{value:"Module"}));function Rn(e){const t=Object.assign({"./antd/index.ts":Hn});for(const o in t){const n=t[o].default;typeof n=="function"&&n(e)}}Y.configure({showSpinner:!1});K.beforeEach(async(e,t,o)=>{if(console.log("to -- ",e),console.log("from -- ",t),e.query.game_project&&localStorage.setItem("crtGame",e.query.game_project),e.path==="/"&&!localStorage.getItem("ticket")){o({path:"login",replace:!0});return}if(e.name==="Login"){if(e.query.ticket)localStorage.setItem("ticket",e.query.ticket);else if(!localStorage.getItem("ticket"))return o()}if(e.name==="Error"&&t.name==="Error")return o();const n=ee(),{setTicket:_,FETCH_PERMISSION:d,FETCH_GAME_PERMISSION:r}=n,{userState:c}=R(n),a=ne(),{permissionState:s}=R(a),{SET_ROUTES:m,setBtnPromise:f}=a,{FETCH_GLOBAL_CONFIG:I}=pe();if(Y.start(),localStorage.getItem("ticket")&&_(localStorage.getItem("ticket")),c.value.isLogin&&e.path!=="/")return Y.done(),o();if(Go.indexOf(e.path)>-1&&!localStorage.getItem("ticket"))return Y.done(),o();try{await r(),await d();const C=c.value.userInfo.permission_list;m(C),f(C),s.value.routes.forEach(O=>{K.addRoute(O)}),Y.done(),s.value.dynamicRoutes.length===0?o({path:"/403",replace:!0}):(I(),e.path==="/login"||e.path==="/"?o({path:s.value.dynamicRoutes[0].path}):o({...e,replace:!0}))}catch(C){C==="no game infos"?o({path:"/403"}):(localStorage.removeItem("ticket"),o({path:"/login"})),Y.done()}});K.afterEach(e=>{Y.done();const t=e.matched.filter(o=>{var n,_;return((n=o.meta)==null?void 0:n.breadcrumb)!==!1&&((_=o.meta)==null?void 0:_.title)}).map(o=>{var n;return(n=o.meta)==null?void 0:n.title}).reverse();document.title=(t.length?t.join(" - ")+" - ":"")+"私域管理平台"});const Bn={class:"box-card-content"},An={key:0,class:"custom-table-top"},Kn={class:"tools",ref:"tools"},Vn={class:"left"},qn={class:"right"},Gn={key:0,style:{"font-weight":"400",color:"#777","font-size":"13px"}},Nn={key:1,class:"custom-table-footer"},Yn={name:"CustomTable"},Wn=x({...Yn,props:{height:{default:"auto"},bottomPadding:{default:0},dataApi:{type:Function,default:()=>Promise.resolve()},params:{default:()=>({})},tableModel:{default:"FillLeaveHeight"},rowKey:{},selection:{type:Boolean},columns:{default:null},noCard:{type:Boolean,default:!1},pagination:{type:Boolean,default:!0}},emits:["changeInfo","selection-change"],setup(e,{expose:t,emit:o}){const n=e,_=A(),d=A([]),r=V(()=>d.value.length>0?[...d.value,...n.columns||[]]:n.columns),c=A(!1),a=G({pageIndex:1,pageSize:15,total:1,list:[],sort_field:"",sort_type:"",selectedRowKeys:[]});function s(w=!1){if(console.log("requestTableData resetPage",w),!n.dataApi)return console.error("params dataApi err！");w&&(a.pageIndex=1);const F={...n.params,sort_field:a.sort_field,sort_type:a.sort_type};n.pagination&&(F.page=a.pageIndex,F.page_size=a.pageSize),c.value=!0,n.dataApi(F).then(async D=>{!d.value.length&&D.fields&&(d.value=[]),m(),a.list=D.data.map((E,B)=>D.data.length===1?{...E,first:!0,last:!0}:B===0?{...E,first:!0}:B===D.data.length-1?{...E,last:!0}:E),a.pageIndex=Number(D.current_page),a.total=D.total,a.pageSize=D.per_page,await re(),o("changeInfo",D)}).catch(D=>{console.error(D)}).finally(()=>{c.value=!1})}const m=()=>{a.selectedRowKeys=[],o("selection-change",[])},f=w=>{a.selectedRowKeys.push(w.id),o("selection-change",a.list.filter(F=>a.selectedRowKeys.includes(F.id)))},I=w=>{a.pageIndex=w,s(),m()},C=A(200),O=A("customTable"),b=Pt(async()=>{var w,F,D,E,B;await re(),await re(),C.value=((w=_.value)==null?void 0:w.clientHeight)||0,C.value-=((D=(F=_.value)==null?void 0:F.querySelector(".ant-table-header"))==null?void 0:D.clientHeight)||40,U(["leftTool","rightTool"])&&((E=_.value)!=null&&E.querySelector(".ant-table-title"))&&(C.value-=((B=_.value)==null?void 0:B.querySelector(".ant-table-title")).offsetHeight)},300),z=(w,F,D)=>{var E;a.sort_field=D.columnKey||D.field,a.sort_type=(E=D.order)==null?void 0:E.replace("end",""),a.pageIndex=1,s()},k=async()=>{n.tableModel==="FillLeaveHeight"&&(b(),window.removeEventListener("resize",b),window.addEventListener("resize",b))};Ut(()=>{s(),k()}),t({requestTableData:s,renderHeight:k,customTable:O,clearSelection:m,toggleRowSelection:f});const T=Et(),U=w=>{if(typeof w=="string")return!!T[w];for(let F=0;F<w.length;F++)if(T[w[F]])return!0;return!1};return(w,F)=>{const D=i("a-tooltip"),E=i("a-table"),B=i("a-pagination"),le=i("a-card");return u(),h(le,{class:he(["box-card mw-child-h-auto",{"no-card":w.noCard}]),border:!1},{default:l(()=>[v("div",Bn,[U("top")?(u(),y("div",An,[N(w.$slots,"top",{},void 0,!0)])):M("",!0),v("div",{class:"custom-table-wrap",id:"custom-table-wrap",ref_key:"customTabelWrap",ref:_},[p(E,{loading:c.value,id:"table",ref_key:"customTable",ref:O,"data-source":a.list,"row-selection":w.selection?{fixed:!0,selectedRowKeys:a.selectedRowKeys,onChange:(j,X)=>{a.selectedRowKeys=j,o("selection-change",X)}}:null,pagination:!1,scroll:{x:"100%",y:`${C.value}px`},rowKey:w.rowKey||"id",onChange:z,columns:g(r),size:"small"},Ht({bodyCell:l(({text:j,record:X,index:se,column:ie})=>[N(w.$slots,"bodyCell",{column:ie,record:X,index:se,text:j},void 0,!0)]),headerCell:l(({column:j})=>[N(w.$slots,"headerCell",{column:j},()=>[j.desc?(u(),h(D,{key:0,placement:"top",class:"th-over-hidden"},{title:l(()=>[v("span",null,$(j.desc),1)]),default:l(()=>[v("span",null,$(j.title),1)]),_:2},1024)):(u(),y(L,{key:1},[j.child?(u(),y("span",Gn,$(j.title),1)):(u(),y(L,{key:1},[S($(j.title),1)],64))],64))],!0)]),default:l(()=>[N(w.$slots,"default",{},void 0,!0)]),_:2},[U(["leftTool","rightTool"])?{name:"title",fn:l(()=>[v("div",Kn,[v("div",Vn,[N(w.$slots,"leftTool",{},void 0,!0)]),v("div",qn,[N(w.$slots,"rightTool",{},void 0,!0)])],512)]),key:"0"}:void 0]),1032,["loading","data-source","row-selection","scroll","rowKey","columns"])],512),n.pagination?(u(),y("div",Nn,[p(B,{size:"small",current:a.pageIndex,"onUpdate:current":F[0]||(F[0]=j=>a.pageIndex=j),"page-size":a.pageSize,"onUpdate:pageSize":F[1]||(F[1]=j=>a.pageSize=j),total:a.total,"page-size-options":["15","30","50","100"],onChange:I,"show-quick-jumper":"","show-size-changer":"",layout:"total, prev, pager, next, sizes"},null,8,["current","page-size","total"])])):M("",!0)])]),_:3},8,["class"])}}});const Qn=P(Wn,[["__scopeId","data-v-3668bf36"]]),Jn=Object.freeze(Object.defineProperty({__proto__:null,default:Qn},Symbol.toStringTag,{value:"Module"}));const Xn=x({name:"Editor",components:{WangEditor:Rt,Toolbar:Bt},props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},height:{type:Number,default:300}},emits:["update:value","change"],setup(e,{emit:t}){const o=At(),n=V({get:()=>e.value,set:a=>{t("update:value",a)}}),_={toolbarKeys:["headerSelect","fontSize","bold","underline","italic","through","clearStyle","color","bgColor","sup","sub","bulletedList","numberedList","todo"]},d={placeholder:"请输入内容..."};return W(()=>e.disabled,a=>{o.value&&(a?o.value.disable():o.value.enable())}),Kt(()=>{const a=o.value;a!=null&&a.destroy()}),{editorRef:o,valueHtml:n,mode:"default",toolbarConfig:_,editorConfig:d,handleCreated:a=>{o.value=a},onChange:a=>{a.getText()||t("update:value","")}}}});const Zn={class:"custom-editor"};function ea(e,t,o,n,_,d){const r=i("Toolbar"),c=i("WangEditor");return u(),y("div",Zn,[p(r,{style:{"border-bottom":"1px solid #f0f0f0"},editor:e.editorRef,defaultConfig:e.toolbarConfig,mode:e.mode},null,8,["editor","defaultConfig","mode"]),p(c,{style:Vt(`height: ${e.height}px; overflow-y: hidden;`),modelValue:e.valueHtml,"onUpdate:modelValue":t[0]||(t[0]=a=>e.valueHtml=a),defaultConfig:e.editorConfig,mode:e.mode,onOnCreated:t[1]||(t[1]=a=>e.editorRef=a),onOnChange:e.onChange},null,8,["style","modelValue","defaultConfig","mode","onOnChange"])])}const ta=P(Xn,[["render",ea],["__scopeId","data-v-2edbefed"]]),oa=Object.freeze(Object.defineProperty({__proto__:null,default:ta},Symbol.toStringTag,{value:"Module"})),na={class:"prop-icon"},aa=["src"],la=["src"],sa=["src"],ia={name:"PropIcon"},ra=x({...ia,props:{propDetail:{}},setup(e){const t=e;return(o,n)=>(u(),y("div",na,[ce(v("img",{src:t.propDetail.image_quality},null,8,aa),[[ue,t.propDetail.image_quality]]),ce(v("img",{src:t.propDetail.image},null,8,la),[[ue,t.propDetail.image]]),ce(v("img",{src:t.propDetail.image_chip},null,8,sa),[[ue,t.propDetail.image_chip]])]))}});const ca=P(ra,[["__scopeId","data-v-fd5e0ee4"]]),ua=Object.freeze(Object.defineProperty({__proto__:null,default:ca},Symbol.toStringTag,{value:"Module"})),te=e=>(ve("data-v-8c01742a"),e=e(),ye(),e),da={class:"content-box"},pa=te(()=>v("div",{class:"title"},"1.下载导入模板",-1)),_a=te(()=>v("div",{class:"desc"},"请先下载导入模板，按模板要求填写数据；为避免导入失败，填写过程请勿修改表头",-1)),ma={class:"btns-wrap"},fa={class:"content-box"},ga=te(()=>v("div",{class:"title"},"2.导入文件",-1)),ha={class:"desc"},va=te(()=>v("br",null,null,-1)),ya={class:"btns-wrap"},ba={class:"ant-upload-drag-icon"},wa=te(()=>v("p",{class:"ant-upload-text"},"点击或拖拽文件至此区域即可上传",-1)),Sa={name:"UploadBtn"},ka=x({...Sa,props:{uploadData:{default:()=>({})},page:{default:""},accept:{default:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"},downloadApi:{type:Function,default:()=>{}},fileType:{default:""},downloadData:{default:()=>({})},hideUploadBtn:{type:Boolean,default:!1},hideDownloadBtn:{type:Boolean,default:!1}},emits:["uploadSuccess"],setup(e,{emit:t}){const o=e,n=G({visibleUpload:!1,uploadLoading:!1,fileList:[],file:null});function _(){n.visibleUpload=!1,n.uploadLoading=!1,n.fileList=[]}const d=s=>(n.fileList=[s],!1);function r(s){n.fileList=[s.file]}function c(){if(n.fileList.length===0)return Z.error("未选择文件！");n.uploadLoading=!0;const s=new FormData;s.append("file",n.fileList[0]);for(const m in o.uploadData)s.append(m,o.uploadData[m]);No(o.page,s).then(m=>{t("uploadSuccess",m),_()}).catch(()=>(n.uploadLoading=!1,Z.error("请按照模版上传")))}W(()=>n.visibleUpload,(s,m)=>{s===!1&&m===!0&&(n.uploadLoading=!1,n.fileList=[])});const a=s=>{s?Yo(o.fileType):o.downloadApi({...o.downloadData})};return(s,m)=>{const f=i("a-button"),I=i("CloudDownloadOutlined"),C=i("a-card"),O=i("inbox-outlined"),b=i("a-upload-dragger"),z=i("CloudUploadOutlined"),k=i("a-modal"),T=i("a-space");return u(),h(T,null,{default:l(()=>[o.hideUploadBtn?M("",!0):(u(),h(f,{key:0,onClick:m[0]||(m[0]=U=>n.visibleUpload=!0)},{default:l(()=>[S("批量更新")]),_:1})),o.hideDownloadBtn?M("",!0):(u(),h(f,{key:1,type:"primary",link:"",onClick:m[1]||(m[1]=U=>a(!1))},{default:l(()=>[S("导出配置")]),_:1})),p(k,{open:n.visibleUpload,"onUpdate:open":m[5]||(m[5]=U=>n.visibleUpload=U),title:"批量更新","mask-closable":!1},{footer:l(()=>[p(f,{link:"",onClick:_,disabled:n.uploadLoading},{default:l(()=>[S("取消")]),_:1},8,["disabled"]),p(f,{type:"primary",loading:n.uploadLoading,onClick:c},{icon:l(()=>[p(z)]),default:l(()=>[S(" 上传 ")]),_:1},8,["loading"])]),default:l(()=>[p(C,null,{default:l(()=>[v("div",da,[pa,_a,v("div",ma,[p(f,{type:"primary",onClick:m[2]||(m[2]=U=>a(!0))},{icon:l(()=>[p(I)]),default:l(()=>[S(" 下载导入模板 ")]),_:1})])])]),_:1}),p(C,null,{default:l(()=>[v("div",fa,[ga,v("div",ha,[N(s.$slots,"tip",{},()=>[S(" * 请按照模板格式准备需要导入的数据，更新后，已配置数据将被覆盖"),va,S(" * 文件小于2M，上传后即开始导入 ")],!0)]),v("div",ya,[p(b,{fileList:n.fileList,"onUpdate:fileList":m[3]||(m[3]=U=>n.fileList=U),name:"file",multiple:!1,action:"/",accept:o.accept,onChange:r,onRemove:m[4]||(m[4]=U=>n.fileList=[]),"before-upload":d},{default:l(()=>[v("p",ba,[p(O)]),wa]),_:1},8,["fileList","accept"])])])]),_:3})]),_:3},8,["open"])]),_:3})}}});const Ca=P(ka,[["__scopeId","data-v-8c01742a"]]),Oa=Object.freeze(Object.defineProperty({__proto__:null,default:Ca},Symbol.toStringTag,{value:"Module"}));function $a(e){const t=Object.assign({"../packages/customTable/Index.vue":Jn,"../packages/editor/Index.vue":oa,"../packages/pictrue/Index.vue":Wo,"../packages/propIcon/Index.vue":ua,"../packages/uploadBtn/Index.vue":Oa});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const Ia={install:$a};H.extend(be);const xa=x({name:"SelectDateTime",props:{value:{type:Array,default:()=>[]},isDisabledDate:{type:Boolean,default:!1}},emits:["update:value","change"],setup(e,t){const o=V({get:()=>{if(e.value)return e.value.length===0?[]:e.value.map(d=>d&&H.utc(d*1e3).format("YYYY-MM-DD HH:mm:ss")||void 0)},set:d=>{let r;d!=null&&d.length&&(r=[H.utc(d[0]).valueOf()/1e3,H.utc(d[1]).valueOf()/1e3]),t.emit("update:value",r),t.emit("change",r)}}),n=d=>e.isDisabledDate?d&&d<H().startOf("day"):!1,_=()=>{const d=!!e.isDisabledDate,r=H().hour(),c=H().minute(),a=H().second(),s=e.isDisabledDate?[H().hour(r).minute(c).second(a),H().hour(r).minute(c).second(a)]:[H().hour(0).minute(0).second(0),H().hour(r).minute(c).second(a)];return{hideDisabledOptions:d,defaultValue:s}};return{...Q(e),modelValue:o,disabledDate:n,showTime:_,dayjs:H}}});function Da(e,t,o,n,_,d){const r=i("a-range-picker");return u(),h(r,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=c=>e.modelValue=c),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":e.disabledDate,"show-time":e.showTime()},null,8,["value","disabled-date","show-time"])}const Fa=P(xa,[["render",Da]]),La=Object.freeze(Object.defineProperty({__proto__:null,default:Fa},Symbol.toStringTag,{value:"Module"})),Ta=x({name:"SelectImgGallery",props:{value:{type:String,default:""},preview:{type:String,default:""}},emits:["update:value","change"],setup(e,{emit:t}){const o=de.useInjectFormItemContext(),n=(s,m={})=>{_.url=m.showUrl,t("update:value",s),t("change",m),o.onFieldChange()},_=G({url:"",isFirst:!0,previewVisible:!1}),d=()=>{Qo.$emit("showPhotoGallery",{callback:s=>{n(s.img_key,s)}})},r=()=>{if(!_.isFirst||!e.value)return;const s={search:e.value};Jo(s).then(m=>{n(m.img_key,m.preview_img||"")}).finally(()=>{_.isFirst=!1})};ge(()=>{r()});const c=s=>{s===!1&&(_.previewVisible=!1)},a=()=>{Z.error("选择的图片已不存在，请重新选择！"),n("")};return{...Q(_),chooseImg:d,onVisibleChange:c,imgLoadError:a,triggerChange:n}}});const za={class:"custom-select-img"};function ja(e,t,o,n,_,d){const r=i("EyeOutlined"),c=i("DeleteOutlined"),a=i("a-space"),s=i("a-image"),m=i("PlusOutlined");return u(),y("div",za,[e.url||e.preview?(u(),h(s,{key:0,src:e.url||e.preview,style:{width:"auto","max-width":"100%","max-height":"100%"},preview:{visible:e.previewVisible,onVisibleChange:e.onVisibleChange},onError:e.imgLoadError},{previewMask:l(()=>[p(a,{align:"center"},{default:l(()=>[p(r,{onClick:t[0]||(t[0]=f=>e.previewVisible=!0)}),p(c,{onClick:t[1]||(t[1]=f=>e.triggerChange(""))})]),_:1})]),_:1},8,["src","preview","onError"])):(u(),y("div",{key:1,class:"img-plus",onClick:t[2]||(t[2]=(...f)=>e.chooseImg&&e.chooseImg(...f))},[p(m)]))])}const Ma=P(Ta,[["render",ja],["__scopeId","data-v-0be816df"]]),Pa=Object.freeze(Object.defineProperty({__proto__:null,default:Ma},Symbol.toStringTag,{value:"Module"})),Ua=x({name:"SelectImg",props:{value:{type:String,default:""},widthHeight:{type:Array,default:()=>[]},tips:{type:String,default:""}},emits:["update:value","change"],setup(e,{emit:t}){const o=de.useInjectFormItemContext(),n=f=>{t("update:value",f),t("change",f),o.onFieldChange()},_=G({loading:!1,previewVisible:!1}),d=V({get:()=>e.value,set:f=>{n(f)}}),r=V(()=>d.value?[{uid:Math.floor(Math.random()*100),name:"image.png",status:"done",url:d.value}]:[]),c=async f=>new Promise((I,C)=>{const O=new FormData;O.append("image",f),Xo(O).then(b=>{d.value=b.url,I("")}).catch(()=>{C("")})}),a=async f=>{if(!f)return!1;try{_.loading=!0,await c(f)}catch{}return _.loading=!1,!1},s=f=>{console.log("info=====",f)},m=f=>{_.previewVisible=f};return{...Q(e),...Q(_),imageUrl:d,fileList:r,triggerChange:n,beforeUpload:a,handleChange:s,setVisible:m}}}),Ea={key:0},Ha=v("div",{class:"ant-upload-text"},"Upload",-1),Ra={style:{display:"none"}};function Ba(e,t,o,n,_,d){const r=i("loading-outlined"),c=i("plus-outlined"),a=i("a-upload"),s=i("ExclamationCircleFilled"),m=i("a-typography-text"),f=i("a-image");return u(),y(L,null,[p(a,{style:{height:"110px"},"file-list":e.fileList,"onUpdate:fileList":t[0]||(t[0]=I=>e.fileList=I),action:"#","list-type":"picture-card",accept:"image/gif,image/jpeg,image/jpg,image/png,image/svg","before-upload":e.beforeUpload,onPreview:t[1]||(t[1]=()=>e.setVisible(!0)),onRemove:t[2]||(t[2]=I=>e.imageUrl="")},{default:l(()=>[e.imageUrl?M("",!0):(u(),y("div",Ea,[e.loading?(u(),h(r,{key:0})):(u(),h(c,{key:1})),Ha]))]),_:1},8,["file-list","before-upload"]),e.widthHeight.length>0?(u(),h(m,{key:0,type:"warning",style:{"font-size":"12px"}},{default:l(()=>[p(s),S(" "+$((e.tips?`${e.tips} `:"前台展示图片，建议尺寸比例: ")+`${e.widthHeight[0]} * ${e.widthHeight[1]}`),1)]),_:1})):M("",!0),v("div",Ra,[p(f,{width:200,preview:{visible:e.previewVisible,onVisibleChange:e.setVisible},src:e.value},null,8,["preview","src"])])],64)}const Aa=P(Ua,[["render",Ba]]),Ka=Object.freeze(Object.defineProperty({__proto__:null,default:Aa},Symbol.toStringTag,{value:"Module"})),Va=x({name:"SelectLang",props:{value:{type:String,default:void 0}},emits:["update:value","updateLabel","initLabel"],setup(e,t){const o=G({loading:!1,options:[]});W(()=>e.value,(r,c)=>{r&&!c&&!o.options.length&&Se({key:r}).then(a=>{o.options.push({label:a.zh_cn||a.en,value:r})})});const n=V({get:()=>{var r;return e.value&&t.emit("initLabel",(r=o.options.find(c=>c.value===e.value))==null?void 0:r.label),e.value},set:r=>{var c;t.emit("update:value",r),t.emit("updateLabel",(c=o.options.find(a=>a.value===r))==null?void 0:c.label)}}),_=r=>{if(!r){o.options=[];return}o.loading=!0,Zo({key:r}).then(c=>{o.options=c}).finally(()=>{o.loading=!1})},d=r=>{t.emit("update:value",r)};return{...Q(o),modelValue:n,remoteMethod:_,handleChange:d}}});const qa={class:"lang-select-wrap"},Ga={class:"item"},Na={class:"t"},Ya={class:"b"};function Wa(e,t,o,n,_,d){const r=i("a-spin"),c=i("a-select");return u(),y("div",qa,[p(c,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=a=>e.modelValue=a),allowClear:"",showSearch:"",placeholder:"请输入多语言key","filter-option":!1,"not-found-content":e.loading?void 0:null,"option-label-prop":"label",onSearch:e.remoteMethod,onChange:e.handleChange,style:{"min-width":"200px"},options:e.options},{notFoundContent:l(()=>[p(r,{size:"small"})]),option:l(({value:a,label:s})=>[v("div",Ga,[v("div",Na,$(s),1),v("div",Ya,$(a),1)])]),_:1},8,["value","not-found-content","onSearch","onChange","options"])])}const Qa=P(Va,[["render",Wa],["__scopeId","data-v-e7d0c585"]]),Ja=Object.freeze(Object.defineProperty({__proto__:null,default:Qa},Symbol.toStringTag,{value:"Module"})),Xa=x({name:"SelectWithAllComp",components:{SelectWithAll:en},props:{value:{type:String,default:""},placeholder:{type:String,default:"请选择操作系统"},type:{type:String,default:"platform",validate:e=>["platform","langs","channels"].includes(e)}},emits:["update:value","change"],setup(e,t){const{configState:o}=R(pe()),n=V({get:()=>e.value,set:_=>{t.emit("update:value",_),t.emit("change",_)}});return{...Q(e),modelValue:n,configState:o}}});function Za(e,t,o,n,_,d){const r=i("SelectWithAll");return u(),h(r,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=c=>e.modelValue=c),placeholder:e.placeholder,options:e.configState[e.type]},null,8,["value","placeholder","options"])}const el=P(Xa,[["render",Za]]),tl=Object.freeze(Object.defineProperty({__proto__:null,default:el},Symbol.toStringTag,{value:"Module"})),ol=x({name:"UploadFile",props:{value:{type:String,default:""}},setup(e,t){return{}}});function nl(e,t,o,n,_,d){return null}const al=P(ol,[["render",nl]]),ll=Object.freeze(Object.defineProperty({__proto__:null,default:al},Symbol.toStringTag,{value:"Module"}));function sl(e){const t=Object.assign({"./select-date-time/Index.vue":La,"./select-img-gallery/Index.vue":Pa,"./select-img/Index.vue":Ka,"./select-lang/Index.vue":Ja,"./select-with-all-comp/Index.vue":tl,"./select-with-all/Index.vue":tn,"./upload-file/Index.vue":ll});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const il={install:sl},rl=x({name:"FilterCell"}),cl=x({...rl,props:{record:{type:Object,default:()=>({})}},setup(e){const t=e,{getConfItem:o}=pe();return(n,_)=>{const d=i("a-tag"),r=i("a-descriptions-item"),c=i("a-descriptions"),a=i("a-typography-link"),s=i("a-tooltip");return t.record.is_filter===g(on).CLOSE?(u(),y(L,{key:0},[S("关闭")],64)):(u(),h(s,{key:1,placement:"left",color:"white",destroyTooltipOnHide:"",overlayStyle:{"max-width":"350px"}},{title:l(()=>[p(c,{column:1,size:"small",bordered:"",labelStyle:{width:"88px","white-space":"nowrap"}},{default:l(()=>[p(r,{label:"操作系统"},{default:l(()=>[t.record.f_os==="all"?(u(),y(L,{key:0},[S("ALL")],64)):(u(!0),y(L,{key:1},q(t.record.f_os.split("|").filter(m=>m),m=>(u(),h(d,{key:m},{default:l(()=>{var f;return[S($((f=g(o)("platform",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),p(r,{label:"语种"},{default:l(()=>[t.record.lang==="all"?(u(),y(L,{key:0},[S("ALL")],64)):(u(!0),y(L,{key:1},q(t.record.lang.split("|").filter(m=>m),m=>(u(),h(d,{key:m},{default:l(()=>{var f;return[S($((f=g(o)("langs",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),p(r,{label:"渠道"},{default:l(()=>[t.record.f_channel==="all"?(u(),y(L,{key:0},[S("ALL")],64)):(u(!0),y(L,{key:1},q(t.record.f_channel.split("|").filter(m=>m),m=>(u(),h(d,{key:m},{default:l(()=>{var f;return[S($((f=g(o)("channels",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),p(r,{label:"服务器"},{default:l(()=>[S($(e.record.f_s_ids),1)]),_:1}),p(r,{label:"城堡等级"},{default:l(()=>[S($(e.record.f_lv_ids),1)]),_:1}),e.record.uids!==void 0?(u(),h(r,{key:0,label:"uid 白名单"},{default:l(()=>[S($(e.record.uids||"-"),1)]),_:1})):M("",!0)]),_:1})]),default:l(()=>[p(a,null,{default:l(()=>[S("开启")]),_:1})]),_:1}))}}}),ul=Object.freeze(Object.defineProperty({__proto__:null,default:cl},Symbol.toStringTag,{value:"Module"})),dl=x({name:"LangKey"}),pl=x({...dl,props:{langKey:{default:""},i18n_name:{default:()=>[]}},setup(e){const t=e,o=A({}),n=A(!1),_=()=>{JSON.stringify(o.value)==="{}"&&(n.value=!0,Se({key:t.langKey}).then(d=>{o.value=d}).finally(()=>n.value=!1))};return(d,r)=>{const c=i("a-spin"),a=i("a-typography-text"),s=i("a-descriptions-item"),m=i("a-descriptions"),f=i("ExclamationCircleOutlined"),I=i("a-popover");return u(),y(L,null,[S($((t.i18n_name.find(C=>C.key===t.langKey)||{}).content||t.langKey||"-")+" ",1),t.langKey?(u(),h(I,{key:0,placement:"rightTop",onOpenChange:_,overlayInnerStyle:{"max-height":"500px","overflow-y":"auto"}},{content:l(()=>[g(n)?(u(),h(c,{key:0})):!g(n)&&JSON.stringify(g(o))==="[]"?(u(),h(a,{key:1,type:"secondary"},{default:l(()=>[S("无数据")]),_:1})):(u(),h(m,{key:2,column:1,size:"small",bordered:"",labelStyle:{width:"88px","white-space":"nowrap"},contentStyle:{"max-width":"400px"}},{default:l(()=>[(u(!0),y(L,null,q(g(o),(C,O)=>(u(),h(s,{key:O,label:O},{default:l(()=>[S($(C),1)]),_:2},1032,["label"]))),128))]),_:1}))]),default:l(()=>[p(f)]),_:1})):M("",!0)],64)}}}),_l=Object.freeze(Object.defineProperty({__proto__:null,default:pl},Symbol.toStringTag,{value:"Module"}));function ml(e){const t=Object.assign({"./filter-cell/Index.vue":ul,"./lang-key/Index.vue":_l});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const fl={install:ml},gl={name:"has",beforeMount(e,t,o){var d;const n=t.value;nn.checkBtnPromission(n)?o.type==="template"&&e.replaceWith(...e.children):e.parentNode&&((d=e.parentNode)==null?void 0:d.removeChild).call(d,e)},mounted(){},beforeUpdate(){},updated(){},beforeUnmount(){},unmounted(){}},hl=Object.freeze(Object.defineProperty({__proto__:null,default:gl},Symbol.toStringTag,{value:"Module"})),vl=e=>{const t=Object.assign({"./CommonPermission.ts":hl});for(const o in t){const{name:n,beforeMount:_}=t[o].default;e.directive(n,_)}},yl={install:vl};let ae=null;ae=qt({render:()=>Nt(Mn)});Rn(ae);console.log("发布",20241231);ae.use(an).use(K).use(yl).use(Ia).use(il).use(fl).use(Gt);ae.mount("#app");
