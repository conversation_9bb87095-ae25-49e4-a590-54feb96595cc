import{y as U,L as Y,d as le,A as Z,P as ae,m as W}from"./common-1288aff8.js";import{d as K,r as V,h as H,x as J,a as r,o as v,c as S,w as a,b as t,u as e,v as E,e as g,l as w,i as ee,z as oe,A as ie,_ as Q,k as G,F as O,t as N,n as ne,p as se}from"./vendor-f5ec1a19.js";import{m as X,M as te}from"./antd-a6b46d10.js";const pe=d=>U.get("/api/vip-right-config",d),ue=d=>U.post("/api/vip-right-config",d),de=(d,y)=>U.put(`/api/vip-right-config/${d}`,y),re=d=>U.get(`/api/vip-right-config/${d}`),_e=(d,y)=>U.put(`/api/vip-right-config/set-status/${d}`,{status:y}),ce=d=>U.post("/api/vip-right-config/upload-ladder-configs",d),ge=({id:d})=>U.downfile(`/api/vip-right-config/export-ladders/${d}`),fe=d=>(oe("data-v-2583038c"),d=d(),ie(),d),ve={class:"btns-wrap"},ye={class:"ant-upload-drag-icon"},me=fe(()=>E("p",{class:"ant-upload-text"},"点击或拖拽文件至此区域即可上传",-1)),he={style:{"margin-top":"10px",display:"flex","align-items":"center",gap:"30px"}},be=K({__name:"Form",props:["editId"],emits:["close","refresh"],setup(d,{emit:y}){const u=d,C=V(!0),$=V(!1),I=()=>{$.value=!0,re(u.editId).then(s=>{o.value=s}).finally(()=>$.value=!1)};u.editId&&I();const o=V({id:0,rights_key:"",rights_type:1,rights_name:"",rights_img:"",rights_desc:"",dimension:"",ladder_configs:"{}",collection_method:"",button:"",collection_url:"",status:1,name_key:"en",quantity:0,is_filter:!1}),n=H({uploadLoading:!1,fileList:[],optionsRights:[{label:"平台特权",value:1},{label:"游戏特权",value:2}],optionsDimension:[{label:"账户",value:0},{label:"角色",value:1}],optionsCollection:[{label:"当前页面领取",value:1},{label:"跳转页面",value:2},{label:"无引导",value:3}],optionsKeys:[{label:"rights_gift_day",value:"rights_gift_day"},{label:"rights_gift_week",value:"rights_gift_week"},{label:"rights_gift_month",value:"rights_gift_month"},{label:"rights_gift_level",value:"rights_gift_level"},{label:"rights_mall",value:"rights_mall"},{label:"rights_signin",value:"rights_signin"},{label:"rights_activity",value:"rights_activity"}],optionsBtnText:[{label:"立即领取",value:1},{label:"立即前往",value:2},{label:"联系客服",value:3},{label:"我知道了",value:4},{label:"提交工单",value:5}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],jumpType:[{label:"无跳转",value:1},{label:"跳转",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:1},{label:"按角色",value:2}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5},{label:"终身领取",value:6}],optionsRightsName:[],optionsRightsDesc:[]}),T=V(!1),L=V();J(()=>{m({page:1,page_size:1e3})});const m=s=>{Y(s).then(l=>{console.log("langGetDataFunc res",l),l.data&&l.data.length>0&&(n.optionsTwo=l.data.map(h=>({label:h.zh_cn,value:h.key})),n.optionsRightsName=[...n.optionsTwo],n.optionsRightsDesc=[...n.optionsTwo])})},R=s=>{const l=n.optionsTwo.filter(h=>{var b;return((b=h.label)==null?void 0:b.indexOf(s))>-1});n.optionsRightsName=l},c=s=>{s===void 0&&(n.optionsRightsName=n.optionsTwo)},_=s=>{const l=n.optionsTwo.filter(h=>{var b;return((b=h.label)==null?void 0:b.indexOf(s))>-1});n.optionsRightsDesc=l},p=s=>{s===void 0&&(n.optionsRightsDesc=n.optionsTwo)},P=s=>(n.fileList=[s],!1);function x(s){n.fileList=[s.file],console.log("state.fileList",n.fileList),console.log("state.fileList.length",n.fileList.length),n.fileList.length>0&&F()}function F(){if(console.log("submitUpload",1),n.fileList.length===0)return X.error("未选择文件！");n.uploadLoading=!0;const s=new FormData;s.append("file",n.fileList[0]),ce(s).then(l=>{console.log("uploadConfigs res",l),X.success("上传成功"),o.value.ladder_configs=JSON.stringify(l)}).catch(()=>{n.uploadLoading=!1})}const A=()=>{L.value.validate().then(()=>{T.value=!0;const{id:s,...l}=o.value;u.editId?de(s,l).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}):ue(l).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}),setTimeout(()=>{T.value=!1},1e3)}).catch(()=>{})};return(s,l)=>{const h=r("a-select"),b=r("a-form-item"),q=r("inbox-outlined"),k=r("a-upload-dragger"),i=r("a-button"),M=r("a-input"),B=r("a-space"),z=r("a-form"),j=r("a-spin"),D=r("a-drawer");return v(),S(D,{open:e(C),"onUpdate:open":l[12]||(l[12]=f=>ee(C)?C.value=f:null),title:u.editId?"编辑权益":"新增权益",maskClosable:!1,width:800,onAfterOpenChange:l[13]||(l[13]=f=>!f&&y("close"))},{default:a(()=>[t(j,{spinning:e($)},{default:a(()=>[t(z,{model:e(o),name:"basic",ref_key:"formRef",ref:L,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:a(()=>[t(b,{label:"权益key",name:"rights_key",rules:[{required:!0,message:"请选择权益key"}]},{default:a(()=>[t(h,{disabled:"",style:{width:"100%"},value:e(o).rights_key,"onUpdate:value":l[0]||(l[0]=f=>e(o).rights_key=f),options:e(n).optionsKeys},null,8,["value","options"])]),_:1}),t(b,{label:"权益类型",name:"rights_type",rules:[{required:!0,message:"请选择权益类型"}]},{default:a(()=>[t(h,{style:{width:"100%"},value:e(o).rights_type,"onUpdate:value":l[1]||(l[1]=f=>e(o).rights_type=f),options:e(n).optionsRights},null,8,["value","options"])]),_:1}),t(b,{label:"权益名称",name:"rights_name",rules:[{required:!0,message:"请选择权益名称"}]},{default:a(()=>[t(h,{style:{width:"100%"},value:e(o).rights_name,"onUpdate:value":l[2]||(l[2]=f=>e(o).rights_name=f),options:e(n).optionsRightsName,allowClear:"","filter-option":!1,showSearch:"",onSearch:R,onChange:c},null,8,["value","options"])]),_:1}),t(b,{label:"权益简介",name:"rights_desc",rules:[{required:!0,message:"请选择权益简介"}]},{default:a(()=>[t(h,{style:{width:"100%"},value:e(o).rights_desc,"onUpdate:value":l[3]||(l[3]=f=>e(o).rights_desc=f),options:e(n).optionsRightsDesc,allowClear:"","filter-option":!1,showSearch:"",onSearch:_,onChange:p},null,8,["value","options"])]),_:1}),t(b,{label:"梯度配置",name:"gift_value"},{default:a(()=>[E("div",ve,[t(k,{fileList:e(n).fileList,"onUpdate:fileList":l[4]||(l[4]=f=>e(n).fileList=f),name:"file",multiple:!1,action:"/",accept:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel",onChange:x,onRemove:l[5]||(l[5]=f=>e(n).fileList=[]),"before-upload":P},{default:a(()=>[E("p",ye,[t(q)]),me]),_:1},8,["fileList"])]),E("div",he,[t(i,{type:"primary",onClick:l[6]||(l[6]=f=>e(le)("vip-right-ladder-configs"))},{default:a(()=>[g("下载模版")]),_:1}),e(o).ladder_configs&&e(o).ladder_configs.length>0?(v(),S(i,{key:0,type:"primary",onClick:l[7]||(l[7]=f=>e(ge)({id:u.editId}))},{default:a(()=>[g("导出梯度配置")]),_:1})):w("",!0)])]),_:1}),t(b,{label:"领取维度",name:"dimension",rules:[{required:!0,message:"请选择权益维度"}]},{default:a(()=>[t(h,{style:{width:"100%"},value:e(o).dimension,"onUpdate:value":l[8]||(l[8]=f=>e(o).dimension=f),options:e(n).optionsDimension},null,8,["value","options"])]),_:1}),t(b,{label:"领取文案及跳转"},{default:a(()=>[t(B,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:a(()=>[t(h,{style:{width:"100%"},value:e(o).collection_method,"onUpdate:value":l[9]||(l[9]=f=>e(o).collection_method=f),options:e(n).optionsCollection},null,8,["value","options"]),t(h,{style:{width:"100%"},disbaled:"",value:e(o).button,"onUpdate:value":l[10]||(l[10]=f=>e(o).button=f),options:e(n).optionsBtnText},null,8,["value","options"]),e(o).collection_method===2?(v(),S(M,{key:0,style:{width:"100%"},value:e(o).collection_url,"onUpdate:value":l[11]||(l[11]=f=>e(o).collection_url=f),placeholder:"请输入页面url"},null,8,["value"])):w("",!0)]),_:1})]),_:1}),t(b,{"wrapper-col":{offset:10,span:12}},{default:a(()=>[t(i,{type:"primary",onClick:A,loading:e(T)},{default:a(()=>[g("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const ke=Q(be,[["__scopeId","data-v-2583038c"]]),we=K({__name:"Index",setup(d){const y=[{dataIndex:"id",key:"id",title:"ID",width:"100px"},{dataIndex:"rights_key",key:"rights_key",title:"权益key",width:"150px"},{dataIndex:"rights_type",key:"rights_type",title:"权益分类",width:"100px"},{dataIndex:"rights_name",key:"rights_name",title:"权益名称",width:"130px"},{dataIndex:"rights_desc",key:"rights_desc",title:"简介",width:"130px"},{dataIndex:"dimension",key:"dimension",title:"统计维度",width:"130px"},{dataIndex:"ladder_configs",key:"ladder_configs",title:"梯度配置",width:"130px"},{dataIndex:"collection_method",key:"collection_method",title:"领取方式",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],u=H({editVisible:!1,editId:0,searchParams:{rights_type:null},previewOpen:!1,previewData:{},optionsRights:[{label:"平台特权",value:1},{label:"游戏特权",value:2}],optionsDimension:[{label:"账户",value:0},{label:"角色",value:1}],optionsCollection:[{label:"当前页面领取",value:1},{label:"跳转页面",value:2},{label:"无引导",value:3}],optionsKeys:[{label:"日礼包",value:"rights_gift_day"},{label:"周礼包",value:"rights_gift_week"},{label:"月礼包",value:"rights_gift_month"},{label:"等级礼包",value:"rights_gift_level"},{label:"特惠商城",value:"rights_mall"},{label:"签到福利",value:"rights_signin"},{label:"特惠活动",value:"rights_activity"}],optionsBtnText:[{label:"立即领取",value:1},{label:"立即前往",value:2},{label:"联系客服",value:3},{label:"我知道了",value:4},{label:"提交工单",value:5}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],optionsTwo:[]}),C=V([]),$=V(),I=()=>$.value.requestTableData(!0);J(()=>{o({page:1,page_size:1e3})});const o=L=>{Y(L).then(m=>{m.data&&m.data.length>0&&(u.optionsTwo=m.data.map(R=>({label:R.zh_cn,value:R.key})))})},n=(L,m)=>{u.editVisible=L,u.editId=m||0},T=(L,m,R)=>{C.value[R]=!0,m.status=1-L;const c=L!==1?"关闭后前端将不展示该权益，即刻生效，请谨慎操作，是否确认关闭":"关闭后前端将展示该权益，即刻生效，是否确认开启";te.confirm({title:"提示",content:c,okText:"确定",cancelText:"取消",onOk:()=>{C.value[R]=!1,_e(m.id,L).finally(()=>I())},onCancel:()=>{C.value[R]=!1}})};return(L,m)=>{const R=r("a-select"),c=r("a-button"),_=r("a-space"),p=r("a-image"),P=r("a-switch"),x=r("a-divider"),F=r("a-typography-link"),A=r("CustomTable");return v(),G(O,null,[t(A,{ref_key:"RefCustomTable",ref:$,"data-api":e(pe),params:e(u).searchParams,columns:y},{top:a(()=>[t(_,{direction:"vertical"},{default:a(()=>[t(_,{wrap:"",style:{gap:"20px"}},{default:a(()=>[t(R,{style:{width:"215px"},allowClear:"",value:e(u).searchParams.rights_type,"onUpdate:value":m[0]||(m[0]=s=>e(u).searchParams.rights_type=s),options:e(u).optionsRights,placeholder:"请选择权益类型"},null,8,["value","options"]),t(c,{type:"primary",onClick:I},{default:a(()=>[g("搜索")]),_:1}),t(c,{onClick:m[1]||(m[1]=()=>{e(u).searchParams.rights_type="",I()})},{default:a(()=>[g("重置")]),_:1})]),_:1}),t(_,{wrap:"",style:{padding:"20px 0",gap:"20px"}})]),_:1})]),bodyCell:a(({column:s,record:l,index:h})=>{var b,q,k;return[s.key==="rights_type"?(v(),G(O,{key:0},[g(N((b=e(u).optionsRights.find(i=>i.value===l[s.key]))==null?void 0:b.label),1)],64)):w("",!0),s.key==="rights_name"?(v(),G(O,{key:1},[g(N((q=e(u).optionsTwo.find(i=>i.value===l[s.key]))==null?void 0:q.label),1)],64)):w("",!0),s.key==="rights_img"?(v(),S(p,{key:2,src:l.rights_img,height:60},null,8,["src"])):w("",!0),s.key==="rights_desc"?(v(),G(O,{key:3},[g(N((k=e(u).optionsTwo.find(i=>i.value===l[s.key]))==null?void 0:k.label),1)],64)):w("",!0),s.key==="dimension"?(v(),G(O,{key:4},[g(N(l[s.key]===0?"账户":"角色"),1)],64)):w("",!0),s.key==="ladder_configs"?(v(),G(O,{key:5},[g(N(l[s.key]&&l[s.key].length>0?"已上传":"未上传"),1)],64)):w("",!0),s.key==="collection_method"?(v(),G(O,{key:6},[g(N(l[s.key]===1?"当前页面领取":l[s.key]===2?"跳转页面":"无引导"),1)],64)):w("",!0),s.key==="status"?(v(),S(P,{key:7,checked:l.status,"onUpdate:checked":i=>l.status=i,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:e(C)[h],onClick:i=>T(i,l,h)},null,8,["checked","onUpdate:checked","loading","onClick"])):w("",!0),s.key==="action"?(v(),S(_,{key:8},{split:a(()=>[t(x,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(F,{onClick:i=>n(!0,l.id)},{default:a(()=>[g("编辑")]),_:2},1032,["onClick"])]),_:2},1024)):w("",!0)]}),_:1},8,["data-api","params"]),e(u).editVisible?(v(),S(ke,{key:0,"edit-id":e(u).editId,onClose:m[2]||(m[2]=s=>n(!1)),onRefresh:I},null,8,["edit-id"])):w("",!0)],64)}}});const xe=Q(we,[["__scopeId","data-v-17225d75"]]),qe=Object.freeze(Object.defineProperty({__proto__:null,default:xe},Symbol.toStringTag,{value:"Module"})),Ce=d=>U.get("/api/member-gift",d),Ie=d=>U.post("/api/member-gift",d),Le=(d,y)=>U.put(`/api/member-gift/${d}`,y),Te=d=>U.get(`/api/member-gift/${d}`),Pe=({petId:d})=>U.delete(`/api/member-gift/${d}`),De=(d,y)=>U.put(`/api/member-gift/set-status/${d}`,{status:y}),Ue=({id:d})=>U.put(`/api/member-gift/copy/${d}`),Re=d=>U.downfile("/api/member-gift/export",d),Ve=K({__name:"Form",props:["editId"],emits:["close","refresh"],setup(d,{emit:y}){const u=d,C=V(!0),$=V(!1),I=()=>{$.value=!0,Te(u.editId).then(_=>{o.value=_}).finally(()=>$.value=!1)};u.editId&&I();const o=V({id:0,gift_packages_id:null,vip:1,game_growth:0,role_growth:0,cycle_shape:1,cycle_type:1,cycle_times:0,gift_value:0,f_s_ids:"",name_key:"en",quantity:0,is_filter:!1}),n=H({uploadLoading:!1,optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],jumpType:[{label:"无跳转",value:1},{label:"跳转",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:1},{label:"按角色",value:2}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5},{label:"终身领取",value:6}]}),T=V(!1),L=V();J(()=>{m()});const m=()=>{Z().then(_=>{console.log("getGiftPkgNumList res",_),_&&_.length>0&&(n.optionsTwo=_)})},R=_=>{const p=n.optionsTwo.find(P=>P.value===_);console.log("handleGiftPackage item",p),p&&(o.value.gift_value=p.gift_value)},c=()=>{L.value.validate().then(()=>{T.value=!0;const{id:_,...p}=o.value;u.editId?Le(_,p).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}):Ie(p).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}),setTimeout(()=>{T.value=!1},1e3)}).catch(()=>{})};return(_,p)=>{const P=r("a-select"),x=r("a-form-item"),F=r("a-input-number"),A=r("a-space"),s=r("a-input"),l=r("a-textarea"),h=r("a-button"),b=r("a-form"),q=r("a-spin"),k=r("a-drawer");return v(),S(k,{open:e(C),"onUpdate:open":p[10]||(p[10]=i=>ee(C)?C.value=i:null),title:u.editId?"编辑礼包":"新增礼包",maskClosable:!1,width:800,onAfterOpenChange:p[11]||(p[11]=i=>!i&&y("close"))},{default:a(()=>[t(q,{spinning:e($)},{default:a(()=>[t(b,{model:e(o),name:"basic",ref_key:"formRef",ref:L,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:a(()=>[t(x,{label:"礼包类型",name:"cycle_type",rules:[{required:!0,message:"请选择礼包类型"}]},{default:a(()=>[t(P,{style:{width:"100%"},value:e(o).cycle_type,"onUpdate:value":p[0]||(p[0]=i=>e(o).cycle_type=i),options:e(n).optionsGift},null,8,["value","options"])]),_:1}),t(x,{label:"礼包名称（后台）",name:"gift_packages_id",rules:[{required:!0,message:"请选择礼包名称"}]},{default:a(()=>[t(P,{style:{width:"100%"},value:e(o).gift_packages_id,"onUpdate:value":p[1]||(p[1]=i=>e(o).gift_packages_id=i),options:e(n).optionsTwo,onChange:R},null,8,["value","options"])]),_:1}),t(x,{label:"VIP等级",name:"vip",rules:[{required:!0,message:"请选择VIP等级"}]},{default:a(()=>[t(P,{style:{width:"100%"},value:e(o).vip,"onUpdate:value":p[2]||(p[2]=i=>e(o).vip=i),options:e(n).optionsVip},null,8,["value","options"])]),_:1}),t(x,{label:"游戏成长值",name:"game_growth"},{default:a(()=>[t(F,{style:{width:"100%"},value:e(o).game_growth,"onUpdate:value":p[3]||(p[3]=i=>e(o).game_growth=i),min:0,max:15e5,placeholder:"请输入游戏成长值"},null,8,["value"])]),_:1}),t(x,{label:"角色成长值",name:"role_growth"},{default:a(()=>[t(F,{style:{width:"100%"},value:e(o).role_growth,"onUpdate:value":p[4]||(p[4]=i=>e(o).role_growth=i),min:0,max:15e5,placeholder:"请输入角色成长值"},null,8,["value"])]),_:1}),t(x,{label:"领取维度及周期",name:"cycle_times",rules:[{required:!0,message:"请填写领取维度及周期"}]},{default:a(()=>[t(A,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:a(()=>[t(P,{style:{width:"100%"},value:e(o).cycle_shape,"onUpdate:value":p[5]||(p[5]=i=>e(o).cycle_shape=i),options:e(n).trigger1},null,8,["value","options"]),t(P,{style:{width:"100%"},disabled:"",value:e(o).cycle_type,"onUpdate:value":p[6]||(p[6]=i=>e(o).cycle_type=i),options:e(n).trigger2},null,8,["value","options"]),t(F,{style:{width:"100%"},value:e(o).cycle_times,"onUpdate:value":p[7]||(p[7]=i=>e(o).cycle_times=i),min:0,max:1e5,placeholder:"请填写次数"},null,8,["value"]),g("次 ")]),_:1})]),_:1}),t(x,{label:"礼包价值",name:"gift_value"},{default:a(()=>[t(s,{style:{width:"100px","margin-right":"10px"},readonly:"",value:e(o).gift_value,"onUpdate:value":p[8]||(p[8]=i=>e(o).gift_value=i)},null,8,["value"]),g("积分（发生退款时扣除对应积分） ")]),_:1}),t(x,{label:"服务器",name:"f_s_ids"},{default:a(()=>[t(l,{value:e(o).f_s_ids,"onUpdate:value":p[9]||(p[9]=i=>e(o).f_s_ids=i),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(x,{"wrapper-col":{offset:10,span:12}},{default:a(()=>[t(h,{type:"primary",onClick:c,loading:e(T)},{default:a(()=>[g("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const $e=Q(Ve,[["__scopeId","data-v-435269d3"]]),Ge=K({__name:"Index",setup(d){const y=[{dataIndex:"id",key:"id",title:"ID",width:"100px"},{dataIndex:"cycle_type",key:"cycle_type",title:"礼包类型",width:"100px"},{dataIndex:"gift_packages_id",key:"gift_packages_id",title:"礼包名称（后台）",width:"130px"},{dataIndex:"vip",key:"vip",title:"VIP等级",width:"80px"},{dataIndex:"game_growth",key:"game_growth",title:"游戏成长值",width:"130px"},{dataIndex:"role_growth",key:"role_growth",title:"角色成长值",width:"130px"},{dataIndex:"gift_value",key:"gift_value",title:"礼包积分价值",width:"130px"},{dataIndex:"cycle_shape",key:"cycle_shape",title:"领取维度",width:"130px"},{dataIndex:"cycle_type",key:"cycle_type_1",title:"周期",width:"130px"},{dataIndex:"cycle_times",key:"cycle_times",title:"周期内次数",width:"130px"},{dataIndex:"f_s_ids",key:"f_s_ids",title:"服务器ID",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],u=H({editVisible:!1,editId:0,searchParams:{cycle_type:null},previewOpen:!1,previewData:{},optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],optionsTwo:[]}),C=V([]),$=V(),I=c=>$.value.requestTableData(!c);J(()=>{o()});const o=()=>{Z().then(c=>{console.log("getGiftPkgNumList res",c),c&&c.length>0&&(u.optionsTwo=c)})},n=(c,_)=>{u.editVisible=c,u.editId=_||0},T=c=>W("确定要复制此条数据并生成一份新数据吗？",Ue,{id:c}).then(()=>I(!0)),L=(c,_)=>W(`确定要删除${_?"选中的":"此条"}数据吗？`,Pe,{petId:c}).then(()=>I()),m=c=>{u.previewOpen=!0,console.log("record",c),u.previewData=c},R=(c,_,p)=>{C.value[p]=!0,_.status=1-c,te.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{C.value[p]=!1,De(_.id,c).finally(()=>I(!0))},onCancel:()=>{C.value[p]=!1}})};return(c,_)=>{const p=r("a-select"),P=r("a-button"),x=r("a-space"),F=r("UploadBtn"),A=r("PlusOutlined"),s=r("a-switch"),l=r("a-typography-link"),h=r("a-divider"),b=r("CustomTable"),q=ne("has");return v(),G(O,null,[t(b,{ref_key:"RefCustomTable",ref:$,"data-api":e(Ce),params:e(u).searchParams,columns:y},{top:a(()=>[t(x,{direction:"vertical"},{default:a(()=>[t(x,{wrap:"",style:{gap:"20px"}},{default:a(()=>[t(p,{style:{width:"215px"},allowClear:"",value:e(u).searchParams.cycle_type,"onUpdate:value":_[0]||(_[0]=k=>e(u).searchParams.cycle_type=k),options:e(u).optionsGift,placeholder:"请选择礼包类型"},null,8,["value","options"]),t(P,{type:"primary",onClick:I},{default:a(()=>[g("搜索")]),_:1}),t(P,{onClick:_[1]||(_[1]=()=>{e(u).searchParams.cycle_type="",I()})},{default:a(()=>[g("重置")]),_:1})]),_:1}),t(x,{wrap:"",style:{padding:"20px 0",gap:"20px"}},{default:a(()=>[se(t(F,{ref:"uploadBtn",onUploadSuccess:I,downloadApi:e(Re),fileType:"member-gifts",page:e(ae).RIGHTGIFT},null,8,["downloadApi","page"]),[[q,"Operation"]]),t(P,{type:"primary",onClick:_[2]||(_[2]=k=>n(!0))},{icon:a(()=>[t(A)]),default:a(()=>[g(" 新增礼包 ")]),_:1})]),_:1})]),_:1})]),bodyCell:a(({column:k,record:i,index:M})=>{var B,z,j;return[k.key==="gift_packages_id"?(v(),G(O,{key:0},[g(N((B=e(u).optionsTwo.find(D=>D.value===i[k.key]))==null?void 0:B.label),1)],64)):w("",!0),k.key==="cycle_type"?(v(),G(O,{key:1},[g(N((z=e(u).optionsGift.find(D=>D.value===i[k.key]))==null?void 0:z.label),1)],64)):w("",!0),k.key==="cycle_type_1"?(v(),G(O,{key:2},[g(N((j=e(u).optionsGift1.find(D=>D.value===i.cycle_type))==null?void 0:j.label),1)],64)):w("",!0),k.key==="cycle_shape"?(v(),G(O,{key:3},[g(N(i[k.key]===1?"账户":"角色"),1)],64)):w("",!0),k.key==="status"?(v(),S(s,{key:4,checked:i.status,"onUpdate:checked":D=>i.status=D,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:e(C)[M],onClick:D=>R(D,i,M)},null,8,["checked","onUpdate:checked","loading","onClick"])):w("",!0),k.key==="preview"?(v(),S(l,{key:5,onClick:D=>m(i)},{default:a(()=>[g("预览")]),_:2},1032,["onClick"])):w("",!0),k.key==="action"?(v(),S(x,{key:6},{split:a(()=>[t(h,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(l,{type:"success",onClick:D=>T(i.id)},{default:a(()=>[g("复制")]),_:2},1032,["onClick"]),t(l,{onClick:D=>n(!0,i.id)},{default:a(()=>[g("编辑")]),_:2},1032,["onClick"]),t(l,{type:"danger",danger:"",onClick:D=>L(i.id,!1)},{default:a(()=>[g("删除")]),_:2},1032,["onClick"])]),_:2},1024)):w("",!0)]}),_:1},8,["data-api","params"]),e(u).editVisible?(v(),S($e,{key:0,"edit-id":e(u).editId,onClose:_[3]||(_[3]=k=>n(!1)),onRefresh:I},null,8,["edit-id"])):w("",!0)],64)}}});const Oe=Q(Ge,[["__scopeId","data-v-843a2840"]]),Ae=Object.freeze(Object.defineProperty({__proto__:null,default:Oe},Symbol.toStringTag,{value:"Module"}));export{qe as I,Ae as a};
