# 通用 API 接入工作流（简化版）

## 1. API 接口分析

### 目的

从 api.json 中提取关键信息，确保接口定义与业务需求一致

### 内容

- 请求方式（GET/POST）
- 请求路径
- 请求参数
- 响应数据结构
- 字段类型及含义
- 是否需要 token 认证

### 示例说明

```markdown
### API 接口分析

- 请求方式: POST
- 请求路径: /api/user/login
- 请求参数: { username: string, password: string }
- 响应数据: { token: string, userInfo: { id: string, name: string } }
- 认证: 需要 token（返回中包含）
```

## 2. 类型定义

### 目的

使用 TypeScript 定义接口请求与响应的数据类型，确保类型安全

### 示例

```typescript
interface LoginRequest {
  username: string
  password: string
}

interface LoginResponse {
  token: string
  userInfo: {
    id: string
    name: string
  }
}
```

## 3. Store 层管理 / API 函数创建

### 目的

统一管理接口调用状态（loading、error、data），或直接在 API 目录创建请求函数

### 示例（基于 Redux Toolkit）

```typescript
import { createSlice } from '@reduxjs/toolkit'
import axios from 'utils/axios'

interface LoginState {
  data: LoginResponse | null
  loading: boolean
  error: string | null
}

const initialState: LoginState = {
  data: null,
  loading: false,
  error: null
}

const loginSlice = createSlice({
  name: 'login',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    setError: (state, action) => {
      state.error = action.payload
    },
    setData: (state, action) => {
      state.data = action.payload
    }
  }
})

export const { setLoading, setError, setData } = loginSlice.actions
export default loginSlice.reducer

// API 函数
export const login = (params: LoginRequest) => async (dispatch: any) => {
  try {
    dispatch(setLoading(true))
    const response = await axios.post<LoginResponse>('/api/user/login', params)
    dispatch(setData(response.data))
  } catch (error: any) {
    dispatch(setError(error.message))
  } finally {
    dispatch(setLoading(false))
  }
}
```

## 4. 组件接入

### 目的

在组件中调用 API 函数或 Hook，并引入对应的类型

### 示例

```typescript
import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { login } from 'store/slices/loginSlice'

const LoginComponent: React.FC = () => {
  const dispatch = useDispatch()
  const { data, loading, error } = useSelector((state: any) => state.login)

  useEffect(() => {
    dispatch(login({ username: 'test', password: '123456' }))
  }, [dispatch])

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  return <div>User: {data?.userInfo.name}</div>
}

export default LoginComponent
```

## 5. 最佳实践总结

- 使用 TypeScript 定义接口类型，保证调用时参数及响应数据的准确性
- 统一管理 API 状态，集中处理 loading 与 error
- 保持代码简洁、注释清晰，便于后期维护与扩展

## 6. 注意事项

- 接口同步：确保 api.json 与实际接口保持一致
- 认证安全：妥善处理 token 等认证逻辑
- 异常捕获：完善错误处理机制，防止异常影响用户体验
- 性能优化：对高频接口考虑数据缓存和分页加载
