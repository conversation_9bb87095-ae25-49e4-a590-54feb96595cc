{
  "editor.defaultFormatter": "esbenp.prettier-vscode", // 默认格式化器
  "editor.formatOnSave": true, // 保存时自动格式化
  "eslint.alwaysShowStatus": true, // 总是显示 ESLint 状态
  "prettier.semi": false, // 不添加分号
  "prettier.printWidth": 140, // 打印宽度
  "prettier.trailingComma": "none", // 不添加尾随逗号
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit" // 保存时自动修复 ESLint 错误
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // 默认格式化器
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // 默认格式化器
  },
  "files.associations": {
    "*.tsx": "typescriptreact" // 关联 TSX 文件
  },
  "i18n-ally.localesPaths": ["src/utils/locales"], // 国际化路径
  "i18n-ally.keystyle": "nested", // 国际化键风格
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features" // JSON 格式化器
  },
  // TypeScript and Vue 智能提示增强配置
  "typescript.preferences.includePackageJsonAutoImports": "on", // 自动导入 package.json 中的依赖
  "typescript.suggest.autoImports": true, // 自动导入依赖
  "typescript.preferences.importModuleSpecifier": "relative", // 导入模块使用相对路径
  "typescript.inlayHints.parameterNames.enabled": "all", // 显示函数参数名
  "typescript.inlayHints.variableTypes.enabled": true, // 显示变量类型
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true, // 显示函数返回类型
  "editor.inlayHints.enabled": "onUnlessPressed", // 显示函数参数名

  // Vue 相关配置
  "vetur.experimental.templateInterpolationService": true, // 模板插值服务
  "vetur.validation.template": false, // 禁用模板验证
  "vetur.validation.script": false, // 禁用脚本验证
  "vetur.validation.style": false, // 禁用样式验证

  // 启用更好的跳转和悬浮功能
  "editor.gotoLocation.multipleReferences": "goto", // 多引用跳转
  "editor.gotoLocation.multipleDefinitions": "goto", // 多定义跳转
  "editor.gotoLocation.multipleDeclarations": "goto", // 多声明跳转
  "editor.hover.enabled": true, // 启用悬浮提示
  "editor.hover.delay": 300, // 悬浮提示延迟
  "editor.quickSuggestions": {
    "other": true, // 快速建议
    "comments": false, // 注释
    "strings": true // 字符串
  }
}
