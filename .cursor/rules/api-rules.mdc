---
description: api接入工作流
globs: **/{*.ts,*.tsx,*.js,*.jsx,*.json}
---
# API 接入工作流规范

## 1. API 接口分析

### 目的

从接口文档中提取关键信息，确保接口定义与业务需求一致。

### 内容

- 请求方式（GET/POST/PUT/DELETE）
- 请求路径（注意区分 `/api` 和 `/backend` 前缀）
- 请求参数
- 响应数据结构
- 是否需要 ticket 认证
- 是否需要添加通用参数

### 示例说明

```markdown
### API 接口分析

- 请求方式: GET
- 请求路径: /backend/admin/publicSystemGame
- 请求参数: { is_admin: true, ...params }
- 响应数据: Array<GameInfo>
- 认证: 需要 ticket
- 通用参数: 是（自动注入）
```

## 2. 类型定义

### 目的

使用 TypeScript 定义接口请求与响应的数据类型，确保类型安全。

### 示例

```typescript
// src/@types/api.d.ts
interface GameInfo {
  game_project: string
  game_name: string
  api_server_url: string
  // ... 其他字段
}

// src/api/index.ts
export const publicGameList = (
  params: Partial<{
    is_admin: boolean
    game_project?: string
    // ... 其他参数
  }>
) => Request.get<GameInfo[]>('/backend/admin/publicSystemGame', params)
```

## 3. API 模块创建

### 目的

按功能模块组织 API 文件，统一管理接口调用。

### 规范

1. 文件组织

   - 按功能模块在 `src/api` 目录下创建对应文件
   - 相关接口统一在一个文件中管理
   - 导出函数使用具有描述性的名称

2. 接口实现

   ```typescript
   // src/api/resource.ts
   import Request from '@/server'

   // 获取资源列表
   export const getResourceList = (params: ResourceQuery) => Request.get<ResourceResponse>('/api/resource/list', params)

   // 创建资源
   export const createResource = (params: ResourceCreate) => Request.post<ResourceResponse>('/api/resource/create', params)
   ```

## 4. 状态管理集成

### 目的

使用 Pinia 进行状态管理，处理异步请求状态。

### 示例

```typescript
// src/store/resource.ts
import { defineStore } from 'pinia'
import { getResourceList } from '@/api/resource'

export const useResourceStore = defineStore('resource', {
  state: () => ({
    resourceList: [] as ResourceItem[],
    loading: false,
    error: null as Error | null
  }),

  actions: {
    async fetchResourceList(params: ResourceQuery) {
      this.loading = true
      this.error = null
      try {
        const data = await getResourceList(params)
        this.resourceList = data
      } catch (err) {
        this.error = err as Error
      } finally {
        this.loading = false
      }
    }
  }
})
```

## 5. 组件使用

### 目的

在 Vue 组件中调用 API 或 Store，处理数据展示和交互。

### 示例

```vue
<template>
  <div class="resource-list">
    <div v-if="loading">加载中...</div>
    <div v-else-if="error">{{ error.message }}</div>
    <div v-else>
      <div v-for="item in resourceList" :key="item.id">
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useResourceStore } from '@/store/resource'

const resourceStore = useResourceStore()
const { resourceList, loading, error } = storeToRefs(resourceStore)

onMounted(async () => {
  await resourceStore.fetchResourceList({})
})
</script>
```

## 6. 请求配置说明

### 通用参数

以下参数会被自动注入到请求中：

```typescript
{
  admin_project: string // 当前游戏项目
  system: string // 系统标识
  ticket: string // 用户凭证
  permission_id: string // 权限ID
  user_id: string // 用户ID
  user_name: string // 用户名
}
```

### URL 规则

- `/api` 开头：会自动添加当前游戏的 api_server_url
- `/backend` 开头：会自动替换为管理中心 API 地址
- 其他：直接使用原始地址

### 特殊处理

- FormData 请求：通用参数会被自动 append 到 FormData 中
- 游戏项目请求：自动添加 game_project 参数
- 运营中心请求：需要设置 is_admin: true

## 7. 最佳实践

### 类型定义

- 为请求参数和响应数据定义接口
- 避免使用 any 类型
- 使用 TypeScript 的类型推导

### 错误处理

- 使用 try/catch 捕获异常
- 在 store 中统一处理错误状态
- 在组件中优雅降级展示

### 代码组织

- 按功能模块组织 API 文件
- 相关接口放在同一文件中
- 使用有意义的函数名称

### 性能优化

- 合理使用缓存
- 避免重复请求
- 控制请求频率

## 8. 注意事项

- 接口同步：确保与后端接口文档保持一致
- 权限处理：注意检查接口权限要求
- 参数校验：前端做基本的参数验证
- 错误提示：给用户友好的错误提示
- 状态管理：合理使用 Pinia store
- 类型安全：充分利用 TypeScript 类型系统
