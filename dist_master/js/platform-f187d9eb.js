import{N as ee,O as te,Q as ae,z as B,R as le,m as W,S as ne,T as oe,K as se,U as ie,V as ue,W as de,X as re,Y as _e,Z as ce,i as pe,j as me,k as fe,d as J,l as ge}from"./common-83df99a7.js";import{d as N,r as y,a as l,o as p,c as S,w as a,b as t,u as e,e as C,i as A,h as K,k as M,l as T,F as q,t as V,bz as ve,q as ye,s as z,v as ke}from"./vendor-8e323468.js";import{M as H,m as be}from"./antd-bb254174.js";const he=[{dataIndex:"id",key:"id",title:"id",width:"70px"},{dataIndex:"game_project",key:"game_project",title:"game_project",width:"140px"},{dataIndex:"game_title",key:"game_title",title:"game_name",width:"200px"},{dataIndex:"icon_url",key:"icon_url",title:"icon",width:"80px"},{dataIndex:"game_ids",key:"game_ids",title:"game_id",width:"140px"},{dataIndex:"status",key:"status",title:"状态",width:"140px"},{dataIndex:"action",key:"action",title:"操作",width:"140px",fixed:"right",align:"center"}],Ce=N({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(D,{emit:r}){const w=D,i=y(!0),m=y(!1),j=()=>{m.value=!0,ee(w.editId).then(_=>{d.value=_,d.value.game_ids=JSON.parse(_.game_ids)}).finally(()=>m.value=!1)};w.editId&&j();const d=y({id:0,game_project:void 0,game_title:void 0,status:0,icon_url:"",game_ids:[]}),k=y(!1),b=y(),n=()=>{b.value.validate().then(()=>{k.value=!0;const{id:_,...u}=d.value;u.game_ids=JSON.stringify(u.game_ids),w.editId?te(_,u).then(()=>{r("close"),r("refresh")}).catch(()=>{}).finally(()=>{k.value=!1}):ae(u).then(()=>{r("close"),r("refresh")}).catch(()=>{}).finally(()=>{k.value=!1})}).catch(()=>{})};return(_,u)=>{const O=l("a-input"),x=l("a-form-item"),s=l("SelectLang"),U=l("SelectImg"),o=l("a-select"),h=l("a-button"),f=l("a-form"),L=l("a-spin"),v=l("a-drawer");return p(),S(v,{open:e(i),"onUpdate:open":u[4]||(u[4]=c=>A(i)?i.value=c:null),title:"接入游戏",maskClosable:!1,width:500,onAfterOpenChange:u[5]||(u[5]=c=>!c&&r("close"))},{default:a(()=>[t(L,{spinning:e(m)},{default:a(()=>[t(f,{model:e(d),name:"basic",ref_key:"formRef",ref:b,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off",class:"modal-form"},{default:a(()=>[t(x,{label:"game_project",name:"game_project",rules:[{required:!0,message:"请输入项目名称"}]},{default:a(()=>[t(O,{value:e(d).game_project,"onUpdate:value":u[0]||(u[0]=c=>e(d).game_project=c),placeholder:" 请输入项目名称"},null,8,["value"])]),_:1}),t(x,{label:"game_name",name:"game_title",rules:[{required:!0,message:"请选择多语言"}]},{default:a(()=>[t(s,{value:e(d).game_title,"onUpdate:value":u[1]||(u[1]=c=>e(d).game_title=c)},null,8,["value"])]),_:1}),t(x,{label:"icon",name:"icon_url",rules:[{required:!0,message:"请上传通道ICON"}]},{default:a(()=>[t(U,{value:e(d).icon_url,"onUpdate:value":u[2]||(u[2]=c=>e(d).icon_url=c),"width-height":[72,72]},null,8,["value"])]),_:1}),t(x,{label:"game_ids",name:"game_ids",rules:[{required:!0,message:"请选择游戏"}]},{default:a(()=>[t(o,{value:e(d).game_ids,"onUpdate:value":u[3]||(u[3]=c=>e(d).game_ids=c),mode:"tags",style:{width:"100%"},placeholder:"请输入 game_ids 并回车创建"},null,8,["value"])]),_:1}),t(x,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[t(h,{type:"primary",onClick:n,loading:e(k)},{default:a(()=>[C("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open"])}}}),we=N({__name:"Index",setup(D){const r=K({editVisible:!1,editId:0}),w=y(),i=()=>w.value.requestTableData(!0),m=(n,_)=>{r.editVisible=n,r.editId=_||0},j=n=>W("删除后将无法拉取到项目下的角色，请谨慎操作",oe,{id:n}).then(()=>i()),{FETCH_GLOBAL_CONFIG:d}=B(),k=y([]),b=(n,_,u)=>{k.value[u]=!0,_.status=1-n,console.log(n);const O=n===0?"是否关闭当前项目，关闭后将无法拉到项目下的角色，请谨慎操作":"是否开启当前项目";H.confirm({title:"提示",content:O,okText:"确定",cancelText:"取消",onOk:()=>{k.value[u]=!1,ne(_.id,n).finally(()=>{i(),d(!0)})},onCancel:()=>{k.value[u]=!1}})};return(n,_)=>{const u=l("PlusOutlined"),O=l("a-button"),x=l("a-switch"),s=l("LangKey"),U=l("a-image"),o=l("a-divider"),h=l("a-typography-link"),f=l("a-space"),L=l("CustomTable");return p(),M(q,null,[t(L,{ref_key:"RefCustomTable",ref:w,"data-api":e(le),params:{},columns:e(he)},{leftTool:a(()=>[t(O,{type:"primary",onClick:_[0]||(_[0]=v=>m(!0))},{icon:a(()=>[t(u)]),default:a(()=>[C(" 新增 ")]),_:1})]),bodyCell:a(({record:v,column:c,index:$})=>[c.key==="status"?(p(),S(x,{key:0,checked:v.status,"onUpdate:checked":R=>v.status=R,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(k)[$],onClick:R=>b(R,v,$)},null,8,["checked","onUpdate:checked","loading","onClick"])):T("",!0),c.key==="game_title"?(p(),S(s,{key:1,"lang-key":v.game_title,i18n_name:v.i18n_name},null,8,["lang-key","i18n_name"])):T("",!0),c.key==="icon_url"?(p(),S(U,{key:2,src:v.icon_url,height:60},null,8,["src"])):T("",!0),c.key==="game_ids"?(p(),M(q,{key:3},[C(V(JSON.parse(v.game_ids).join("、")),1)],64)):T("",!0),c.key==="action"?(p(),S(f,{key:4},{split:a(()=>[t(o,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(h,{onClick:R=>m(!0,v.id)},{default:a(()=>[C("编辑")]),_:2},1032,["onClick"]),t(h,{type:"danger",onClick:R=>j(v.id)},{default:a(()=>[C("删除")]),_:2},1032,["onClick"])]),_:2},1024)):T("",!0)]),_:1},8,["data-api","columns"]),e(r).editVisible?(p(),S(Ce,{key:0,"edit-id":e(r).editId,onClose:_[1]||(_[1]=v=>m(!1)),onRefresh:i},null,8,["edit-id"])):T("",!0)],64)}}}),qe=Object.freeze(Object.defineProperty({__proto__:null,default:we},Symbol.toStringTag,{value:"Module"})),xe=[{dataIndex:"id",key:"id",title:"id",width:"70px"},{dataIndex:"game_project",key:"game_project",title:"游戏",width:"100px"},{dataIndex:"status",key:"status",title:"状态",width:"100px"},{dataIndex:"filter",key:"filter",title:"筛选器",width:"60px"},{dataIndex:"action",key:"action",title:"操作",width:"100px",fixed:"right",align:"center"}],Ie=N({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(D,{emit:r}){const w=D,i=B(),{configState:m}=ve(i),{getGameChannel:j}=i,d=y(!0),k=y(!1),b=()=>{k.value=!0,ie(w.editId).then(x=>{n.value=x}).finally(()=>k.value=!1)};w.editId&&b();const n=y({id:0,game_project:void 0,status:0,is_red_dot:0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:"",uids:""}),_=y(!1),u=y(),O=()=>{u.value.validate().then(()=>{if(_.value=!0,w.editId){const{id:x,...s}=n.value;s.uids=s.uids.replace(/\s+/g,"").replace(/，/g,",").replace(/,+/g,","),ue(x,s).then(()=>{r("close"),r("refresh")}).catch(()=>{}).finally(()=>{_.value=!1})}else de(n.value).then(()=>{r("close"),r("refresh")}).catch(()=>{}).finally(()=>{_.value=!1})}).catch(()=>{})};return(x,s)=>{const U=l("a-select"),o=l("a-form-item"),h=l("a-radio"),f=l("a-radio-group"),L=l("SelectWithAllComp"),v=l("SelectWithAll"),c=l("a-textarea"),$=l("a-button"),R=l("a-form"),E=l("a-spin"),F=l("a-drawer");return p(),S(F,{open:e(d),"onUpdate:open":s[11]||(s[11]=g=>A(d)?d.value=g:null),title:w.editId?"编辑入口":"新增入口",maskClosable:!1,width:500,onAfterOpenChange:s[12]||(s[12]=g=>!g&&r("close"))},{default:a(()=>[t(E,{spinning:e(k)},{default:a(()=>[t(R,{model:e(n),name:"basic",ref_key:"formRef",ref:u,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off",class:"modal-form"},{default:a(()=>[t(o,{label:"游戏",name:"game_project",rules:[{required:!0,message:"请选择游戏"}]},{default:a(()=>[t(U,{value:e(n).game_project,"onUpdate:value":s[0]||(s[0]=g=>e(n).game_project=g),placeholder:"请选择游戏",options:e(m).game_projects,onChange:s[1]||(s[1]=()=>e(n).f_channel=void 0)},{option:a(({value:g,label:G})=>[C(V(G)+"-"+V(g),1)]),_:1},8,["value","options"])]),_:1}),t(o,{label:"是否需要红点",name:"is_red_dot"},{default:a(()=>[t(f,{value:e(n).is_red_dot,"onUpdate:value":s[2]||(s[2]=g=>e(n).is_red_dot=g)},{default:a(()=>[t(h,{value:1},{default:a(()=>[C("需要")]),_:1}),t(h,{value:0},{default:a(()=>[C("不需要")]),_:1})]),_:1},8,["value"])]),_:1}),t(o,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:a(()=>[t(f,{value:e(n).is_filter,"onUpdate:value":s[3]||(s[3]=g=>e(n).is_filter=g),onChange:s[4]||(s[4]=g=>e(se)(e(n),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:a(()=>[t(h,{value:1},{default:a(()=>[C("开启")]),_:1}),t(h,{value:0},{default:a(()=>[C("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(n).is_filter===1?(p(),M(q,{key:0},[t(o,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:a(()=>[t(L,{value:e(n).f_os,"onUpdate:value":s[5]||(s[5]=g=>e(n).f_os=g),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(o,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:a(()=>[t(L,{value:e(n).lang,"onUpdate:value":s[6]||(s[6]=g=>e(n).lang=g),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(o,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:a(()=>[t(v,{value:e(n).f_channel,"onUpdate:value":s[7]||(s[7]=g=>e(n).f_channel=g),placeholder:"请选择渠道",options:e(n).game_project?e(j)(e(n).game_project||""):[]},null,8,["value","options"])]),_:1}),t(o,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:a(()=>[t(c,{value:e(n).f_s_ids,"onUpdate:value":s[8]||(s[8]=g=>e(n).f_s_ids=g),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(o,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:a(()=>[t(c,{value:e(n).f_lv_ids,"onUpdate:value":s[9]||(s[9]=g=>e(n).f_lv_ids=g),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1}),t(o,{label:"uid白名单",name:"uids"},{default:a(()=>[t(c,{value:e(n).uids,"onUpdate:value":s[10]||(s[10]=g=>e(n).uids=g),placeholder:"请输入uid，多个uid以“,”隔开","allow-clear":""},null,8,["value"])]),_:1})],64)):T("",!0),t(o,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[t($,{type:"primary",onClick:O,loading:e(_)},{default:a(()=>[C("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),Se=N({__name:"Index",setup(D){const r=K({editVisible:!1,editId:0}),w=y(),i=()=>w.value.requestTableData(!0),m=(b,n)=>{r.editVisible=b,r.editId=n||0},j=b=>W("删除后游戏内私域入口将消失，请谨慎操作",ce,{id:b}).then(()=>i()),d=y([]),k=(b,n,_)=>{d.value[_]=!0,n.status=1-b,H.confirm({title:"提示",content:b===0?"关闭后游戏内私域入口将消失，请谨慎操作，是否确认关闭？":"开启后游戏内将展示私域入口，实时生效，是否确认开启？",okText:"确定",cancelText:"取消",onOk:()=>{d.value[_]=!1,_e(n.id,b).finally(()=>i())},onCancel:()=>{d.value[_]=!1}})};return(b,n)=>{const _=l("PlusOutlined"),u=l("a-button"),O=l("a-switch"),x=l("FilterCell"),s=l("a-divider"),U=l("a-typography-link"),o=l("a-space"),h=l("CustomTable");return p(),M(q,null,[t(h,{ref_key:"RefCustomTable",ref:w,"data-api":e(re),params:{},columns:e(xe)},{leftTool:a(()=>[t(u,{type:"primary",onClick:n[0]||(n[0]=f=>m(!0))},{icon:a(()=>[t(_)]),default:a(()=>[C(" 新增 ")]),_:1})]),bodyCell:a(({record:f,column:L,index:v})=>[L.key==="status"?(p(),S(O,{key:0,checked:f.status,"onUpdate:checked":c=>f.status=c,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(d)[v],onClick:c=>k(c,f,v)},null,8,["checked","onUpdate:checked","loading","onClick"])):T("",!0),L.key==="filter"?(p(),S(x,{key:1,record:f},null,8,["record"])):T("",!0),L.key==="action"?(p(),S(o,{key:2},{split:a(()=>[t(s,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(U,{onClick:c=>m(!0,f.id)},{default:a(()=>[C("编辑")]),_:2},1032,["onClick"]),t(U,{type:"danger",onClick:c=>j(f.id)},{default:a(()=>[C("删除")]),_:2},1032,["onClick"])]),_:2},1024)):T("",!0)]),_:1},8,["data-api","columns"]),e(r).editVisible?(p(),S(Ie,{key:0,"edit-id":e(r).editId,onClose:n[1]||(n[1]=f=>m(!1)),onRefresh:i},null,8,["edit-id"])):T("",!0)],64)}}}),De=Object.freeze(Object.defineProperty({__proto__:null,default:Se},Symbol.toStringTag,{value:"Module"})),Te=[{dataIndex:"name",key:"name",title:"规则分类"},{dataIndex:"key",key:"key",title:"规则key"},{dataIndex:"action",key:"action",title:"操作",align:"center"}],Oe=["innerHTML"],Ue=N({__name:"Form",props:["editKey"],emits:["close","refresh"],setup(D,{emit:r}){const w=D,i=y({name:void 0,key:"",file:"",content:{},last_uploaded_file:""}),m=y("en"),j=y(!0),d=y(!1);(()=>{d.value=!0,pe(w.editKey).then(U=>{i.value={...i.value,...U}}).finally(()=>d.value=!1)})();const b=y(!1),n=y(),_=()=>{n.value.validate().then(()=>{b.value=!0,me(i.value).then(()=>{r("close"),r("refresh")}).catch(()=>{}).finally(()=>{b.value=!1})}).catch(()=>{})},u=y({fileList:[],url:""}),O=async U=>{if(!U)return!1;try{const o=new FormData;o.append("file",U),o.append("key",i.value.key),fe(o).then(h=>{i.value.content=h.content,be.success("上传解析成功")}).catch()}catch{}return!1},x=()=>{i.value.last_uploaded_file?window.open(i.value.last_uploaded_file,"_blank"):J(i.value.key)},s=async(U,o)=>{let h=!0;if(i.value.key==="faq_rules")h=!1;else for(const f in o){if((f==="en"||f==="zh_cn")&&!o[f].text)return Promise.reject("英文或中文内容不可为空");o[f].text||(h=!1)}return h?Promise.reject("内容不可为空"):Promise.resolve()};return(U,o)=>{const h=l("a-input"),f=l("a-form-item"),L=l("upload-outlined"),v=l("a-button"),c=l("CloudDownloadOutlined"),$=l("a-space"),R=l("a-upload"),E=l("a-radio"),F=l("a-radio-group"),g=l("a-descriptions-item"),G=l("a-descriptions"),Y=l("Editor"),Q=l("a-form"),X=l("a-spin"),Z=l("a-drawer");return p(),S(Z,{open:e(j),"onUpdate:open":o[6]||(o[6]=I=>A(j)?j.value=I:null),title:"编辑规则",maskClosable:!1,width:600,onAfterOpenChange:o[7]||(o[7]=I=>!I&&r("close"))},{default:a(()=>[t(X,{spinning:e(d)},{default:a(()=>[t(Q,{model:e(i),name:"basic",ref_key:"formRef",ref:n,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:a(()=>[t(f,{label:"规则分类",name:"name",rules:[{required:!0,message:"请选择规则分类"}]},{default:a(()=>[t(h,{value:e(i).name,"onUpdate:value":o[0]||(o[0]=I=>e(i).name=I),placeholder:"请输入规则分类"},null,8,["value"])]),_:1}),t(f,{label:"规则KEY",name:"key",rules:[{required:!0,message:"请输入规则KEY"}]},{default:a(()=>[t(h,{value:e(i).key,"onUpdate:value":o[1]||(o[1]=I=>e(i).key=I),placeholder:"请输入规则key",disabled:""},null,8,["value"])]),_:1}),t(f,{label:"规则文档",name:"file"},{default:a(()=>[t(R,{name:"file",style:{height:"110px"},"file-list":e(u).fileList,"onUpdate:fileList":o[2]||(o[2]=I=>e(u).fileList=I),action:"#",accept:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel","before-upload":O,onRemove:o[3]||(o[3]=I=>e(u).url="")},{default:a(()=>[t($,null,{default:a(()=>[t(v,null,{default:a(()=>[t(L),C(" 点击上传 ")]),_:1}),t(v,{type:"primary",onClick:ye(x,["stop"])},{icon:a(()=>[t(c)]),default:a(()=>[C(V(e(i).last_uploaded_file?"下载文档":"下载模板"),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["file-list"])]),_:1}),t(f,{label:"按语种编辑",name:"content",rules:[{validator:s}]},{default:a(()=>[t(F,{value:e(m),"onUpdate:value":o[4]||(o[4]=I=>A(m)?m.value=I:null),style:{"line-height":"32px","margin-bottom":"8px"}},{default:a(()=>[t($,{wrap:""},{default:a(()=>[(p(!0),M(q,null,z(e(i).content,(I,P)=>(p(),S(E,{key:P,value:P},{default:a(()=>[C(V(P),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["value"]),e(m)?(p(),M(q,{key:0},[e(i).key==="faq_rules"?(p(),M(q,{key:0},[e(i).content[e(m)].length?(p(),S(G,{key:0,layout:"vertical",bordered:"",column:1,size:"small"},{default:a(()=>[(p(!0),M(q,null,z(e(i).content[e(m)],(I,P)=>(p(),S(g,{label:I[`question${P+1}`]},{default:a(()=>[ke("span",{innerHTML:I[`answer${P+1}`]},null,8,Oe)]),_:2},1032,["label"]))),256))]),_:1})):T("",!0)],64)):JSON.stringify(e(i).content)!=="{}"?(p(),S(Y,{key:e(m),value:e(i).content[e(m)].text,"onUpdate:value":o[5]||(o[5]=I=>e(i).content[e(m)].text=I)},null,8,["value"])):T("",!0)],64)):T("",!0)]),_:1},8,["rules"]),t(f,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[t(v,{type:"primary",onClick:_,loading:e(b)},{default:a(()=>[C("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open"])}}}),je=N({__name:"Index",setup(D){const r=K({editVisible:!1,key:""}),w=y(),i=()=>w.value.requestTableData(!0),m=(d,k)=>{r.editVisible=d,r.key=k||""},j=d=>{J(d.key==="faq_rules"?"faq":"rule")};return(d,k)=>{const b=l("a-divider"),n=l("a-typography-link"),_=l("a-space"),u=l("CustomTable");return p(),M(q,null,[t(u,{ref_key:"RefCustomTable",ref:w,"data-api":e(ge),params:{},columns:e(Te),pagination:!1},{bodyCell:a(({record:O,column:x})=>[x.key==="action"?(p(),S(_,{key:0},{split:a(()=>[t(b,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(n,{onClick:s=>m(!0,O.key)},{default:a(()=>[C("编辑")]),_:2},1032,["onClick"]),t(n,{onClick:s=>j(O)},{default:a(()=>[C("下载模板")]),_:2},1032,["onClick"])]),_:2},1024)):T("",!0)]),_:1},8,["data-api","columns"]),e(r).editVisible?(p(),S(Ue,{key:0,"edit-key":e(r).key,onClose:k[0]||(k[0]=O=>m(!1)),onRefresh:i},null,8,["edit-key"])):T("",!0)],64)}}}),$e=Object.freeze(Object.defineProperty({__proto__:null,default:je},Symbol.toStringTag,{value:"Module"}));export{qe as I,De as a,$e as b};
