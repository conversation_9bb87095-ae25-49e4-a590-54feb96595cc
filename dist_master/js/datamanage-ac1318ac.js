import{_ as e}from"./vendor-8e323468.js";const r={};function o(t,n){return" 积分明细 "}const c=e(r,[["render",o]]),s=Object.freeze(Object.defineProperty({__proto__:null,default:c},Symbol.toStringTag,{value:"Module"})),_={};function a(t,n){return" 成长值明细 "}const d=e(_,[["render",a]]),l=Object.freeze(Object.defineProperty({__proto__:null,default:d},Symbol.toStringTag,{value:"Module"}));export{s as I,l as a};
