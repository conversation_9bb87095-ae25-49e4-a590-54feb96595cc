import{d as E,r as q,a as i,o as f,c as h,w as l,b as t,u as e,l as k,e as m,k as Y,F as V,i as Q,f as K,g as z,h as G,t as N,bz as de,s as te,j as re,v as _e,m as ne}from"./vendor-8e323468.js";import{y as O,K as ae,m as B,z as le,M as ee}from"./common-83df99a7.js";import{M as X,m as ie}from"./antd-bb254174.js";const pe=d=>O.get("/api/sdk-diamond-position",d),fe=d=>O.post("/api/sdk-diamond-position",d),me=d=>O.get(`/api/sdk-diamond-position/${d}`),ce=({id:d})=>O.put(`/api/sdk-diamond-position/copy/${d}`),ve=(d,_)=>O.put(`/api/sdk-diamond-position/${d}`,_),ge=(d,_)=>O.put(`/api/sdk-diamond-position/set-status/${d}`,{status:_}),ye=({id:d,type:_})=>O.put(`/api/sdk-diamond-position/set-listorder/${d}`,{type:_}),ke=({id:d})=>O.delete(`/api/sdk-diamond-position/${d}`),be=d=>O.get("/api/priv-diamond-position",d),he=d=>O.post("/api/priv-diamond-position",d),we=d=>O.get(`/api/priv-diamond-position/${d}`),Ce=({id:d})=>O.put(`/api/priv-diamond-position/copy/${d}`),Ie=(d,_)=>O.put(`/api/priv-diamond-position/${d}`,_),xe=(d,_)=>O.put(`/api/priv-diamond-position/set-status/${d}`,{status:_}),Ue=({id:d,type:_})=>O.put(`/api/priv-diamond-position/set-listorder/${d}`,{type:_}),De=({id:d})=>O.delete(`/api/priv-diamond-position/${d}`),$e=d=>O.get("/api/activity-banner-group",d),Se=d=>O.post("/api/activity-banner-group",d),oe=d=>O.get(`/api/activity-banner-group/${d}`),Te=(d,_)=>O.put(`/api/activity-banner-group/${d}`,_),Oe=(d,_)=>O.put(`/api/activity-banner-group/set-status/${d}`,{status:_}),qe=({id:d})=>O.put(`/api/activity-banner-group/copy/${d}`),Le=({id:d})=>O.delete(`/api/activity-banner-group/${d}`),Re=d=>O.get("/api/activity-banner-item",d),Ae=d=>O.post("/api/activity-banner-item",d),Ye=d=>O.get(`/api/activity-banner-item/${d}`),Ve=(d,_)=>O.put(`/api/activity-banner-item/${d}`,_),Me=(d,_)=>O.put(`/api/activity-banner-item/set-status/${d}`,{status:_}),Pe=({id:d,params:_})=>O.put(`/api/activity-banner-item/set-listorder/${d}`,_),je=({id:d})=>O.delete(`/api/activity-banner-item/${d}`),He=d=>O.get("/api/corner-mark-config",d),Ke=d=>O.post("/api/corner-mark-config",d),Ne=d=>O.get(`/api/corner-mark-config/${d}`),Be=(d,_)=>O.put(`/api/corner-mark-config/${d}`,_),Ee=(d,_)=>O.put(`/api/corner-mark-config/set-status/${d}`,{status:_}),Fe=({id:d})=>O.delete(`/api/corner-mark-config/${d}`),ze=d=>O.get("/api/ad-config",d),We=d=>O.post("/api/ad-config",d),Je=d=>O.get(`/api/ad-config/${d}`),Ge=(d,_)=>O.put(`/api/ad-config/${d}`,_),Qe=(d,_)=>O.put(`/api/ad-config/set-status/${d}`,{status:_}),Xe=({id:d})=>O.delete(`/api/ad-config/${d}`),Ze=({id:d,type:_})=>O.put(`/api/ad-config/set-listorder/${d}`,{type:_}),se=[{dataIndex:"id",key:"id",title:"ID",width:"50px"},{dataIndex:"list_order",key:"list_order",title:"排序",width:"50px"},{dataIndex:"entrance_name",key:"entrance_name",title:"入口名称",width:"200px"},{dataIndex:"img_id",key:"img_id",title:"Icon",width:"100px"},{dataIndex:"status",key:"status",title:"状态",width:"80px"},{dataIndex:"link_type",key:"link_type",title:"跳转类型",width:"100px"},{dataIndex:"link_url",key:"link_url",title:"跳转链接",width:"200px"},{dataIndex:"times",key:"times",title:"上线时间",width:"160px"},{dataIndex:"filter",key:"filter",title:"筛选器",width:"60px"},{dataIndex:"action",key:"action",title:"操作",width:"260px",fixed:"right",align:"center"}],et=[{dataIndex:"id",key:"id",title:"id",width:"50px"},{dataIndex:"name",key:"name",title:"后台名称",width:"200px"},{dataIndex:"img_url",key:"img_url",title:"角标预览",width:"100px"},{dataIndex:"is_online",key:"is_online",title:"状态",width:"80px"},{dataIndex:"target_url",key:"target_url",title:"跳转链接",width:"200px"},{dataIndex:"validity_type",key:"validity_type",title:"上线时间",width:"160px"},{dataIndex:"weight",key:"weight",title:"权重",width:"60px"},{dataIndex:"action",key:"action",title:"操作",width:"260px",fixed:"right",align:"center"}],tt=[{dataIndex:"id",key:"id",title:"id",width:"50px"},{dataIndex:"name",key:"name",title:"后台名称",width:"200px"},{dataIndex:"img_lang_type",key:"img_lang_type",title:"banner图",width:"100px"},{dataIndex:"is_online",key:"is_online",title:"状态",width:"80px"},{dataIndex:"target_url",key:"target_url",title:"跳转链接",width:"200px"},{dataIndex:"validity_type",key:"validity_type",title:"上线时间",width:"160px"},{dataIndex:"action",key:"action",title:"操作",width:"260px",fixed:"right",align:"center"}],Z={1:{label:"私域内跳",value:1},2:{label:"打开新页面",value:2}},ue={1:{label:"站内文章",value:1},2:{label:"站内H5",value:2},3:{label:"站外链接",value:3}},at=[{dataIndex:"id",key:"id",title:"组id",width:"50px"},{dataIndex:"group_name",key:"group_name",title:"组名称",width:"100px"},{dataIndex:"style_type",key:"style_type",title:"样式",width:"100px"},{dataIndex:"status",key:"status",title:"状态",width:"80px"},{dataIndex:"show_page",key:"show_page",title:"展示页面",width:"200px"},{dataIndex:"times",key:"times",title:"上线时间",width:"160px"},{dataIndex:"action",key:"action",title:"操作",width:"190px",fixed:"right",align:"center"}],lt=[{dataIndex:"id",key:"id",title:"ID",width:"50px"},{dataIndex:"title",key:"title",title:"名称",width:"200px"},{dataIndex:"img_id",key:"img_id",title:"banner图",width:"150px"},{dataIndex:"status",key:"status",title:"状态",width:"80px"},{dataIndex:"type",key:"type",title:"跳转类型",width:"100px"},{dataIndex:"link_url",key:"link_url",title:"跳转链接",width:"200px"},{dataIndex:"times",key:"times",title:"上线时间",width:"160px"},{dataIndex:"action",key:"action",title:"操作",width:"220px",fixed:"right",align:"center"}],nt=E({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(d,{emit:_}){const g=d,x=q(!0),S=q(!1),R=()=>{S.value=!0,me(g.editId).then(p=>{n.value=p,p.online_at&&p.offline_at?(n.value.is_forever=0,n.value.times=[p.online_at,p.offline_at]):(n.value.is_forever=1,n.value.times=void 0)}).finally(()=>S.value=!1)};g.editId&&R();const n=q({id:0,entrance_name:void 0,link_type:void 0,link_url:"",img_id:"",is_forever:0,times:void 0,is_red_dot:0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:""}),r=q(!1),U=q(),w=()=>{U.value.validate().then(()=>{r.value=!0;const{id:p,...a}=n.value;a.is_forever===0&&a.times?(a.online_at=a.times[0],a.offline_at=a.times[1]):(a.online_at=K.utc().unix(),a.offline_at=""),g.editId?ve(p,a).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{r.value=!1}):fe(a).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{r.value=!1})}).catch(()=>{})};return(p,a)=>{const o=i("SelectLang"),s=i("a-form-item"),b=i("a-select"),L=i("a-input"),A=i("SelectImg"),M=i("a-radio-group"),P=i("SelectDateTime"),D=i("a-radio"),T=i("SelectWithAllComp"),j=i("a-textarea"),C=i("a-button"),$=i("a-form"),c=i("a-spin"),v=i("a-drawer");return f(),h(v,{open:e(x),"onUpdate:open":a[15]||(a[15]=u=>Q(x)?x.value=u:null),title:g.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:a[16]||(a[16]=u=>!u&&_("close"))},{default:l(()=>[t(c,{spinning:e(S)},{default:l(()=>[t($,{model:e(n),name:"basic",ref_key:"formRef",ref:U,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(s,{label:"入口名称",name:"entrance_name",rules:[{required:!0,message:"请选择入口名称多语言"}]},{default:l(()=>[t(o,{value:e(n).entrance_name,"onUpdate:value":a[0]||(a[0]=u=>e(n).entrance_name=u),placeholder:"请选择入口名称多语言"},null,8,["value"])]),_:1}),t(s,{label:"跳转类型",name:"link_type",rules:[{required:!0,message:"请选择跳转类型"}]},{default:l(()=>[t(b,{value:e(n).link_type,"onUpdate:value":a[1]||(a[1]=u=>e(n).link_type=u),options:Object.values(e(Z)),placeholder:"请选择跳转类型"},null,8,["value","options"])]),_:1}),t(s,{label:"跳转URL",name:"link_url",rules:[{required:!0,message:"请输入跳转URL"}]},{default:l(()=>[t(L,{value:e(n).link_url,"onUpdate:value":a[2]||(a[2]=u=>e(n).link_url=u),placeholder:"请输入跳转URL"},null,8,["value"])]),_:1}),t(s,{label:"入口icon",name:"img_id",rules:[{required:!0,message:"请上传入口icon"}]},{default:l(()=>[t(A,{value:e(n).img_id,"onUpdate:value":a[3]||(a[3]=u=>e(n).img_id=u),"width-height":[56,56]},null,8,["value"])]),_:1}),t(s,{label:"上线时间",onChange:a[5]||(a[5]=u=>e(n).times=void 0)},{default:l(()=>[t(M,{value:e(n).is_forever,"onUpdate:value":a[4]||(a[4]=u=>e(n).is_forever=u),options:[{label:"永久",value:1},{label:"定时",value:0}]},null,8,["value"])]),_:1}),e(n).is_forever===0?(f(),h(s,{key:0,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:l(()=>[t(P,{value:e(n).times,"onUpdate:value":a[6]||(a[6]=u=>e(n).times=u)},null,8,["value"])]),_:1})):k("",!0),t(s,{label:"是否需要红点",name:"is_red_dot"},{default:l(()=>[t(M,{value:e(n).is_red_dot,"onUpdate:value":a[7]||(a[7]=u=>e(n).is_red_dot=u)},{default:l(()=>[t(D,{value:1},{default:l(()=>[m("需要")]),_:1}),t(D,{value:0},{default:l(()=>[m("不需要")]),_:1})]),_:1},8,["value"])]),_:1}),t(s,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:l(()=>[t(M,{value:e(n).is_filter,"onUpdate:value":a[8]||(a[8]=u=>e(n).is_filter=u),onChange:a[9]||(a[9]=u=>e(ae)(e(n),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:l(()=>[t(D,{value:1},{default:l(()=>[m("开启")]),_:1}),t(D,{value:0},{default:l(()=>[m("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(n).is_filter===1?(f(),Y(V,{key:1},[t(s,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:l(()=>[t(T,{value:e(n).f_os,"onUpdate:value":a[10]||(a[10]=u=>e(n).f_os=u),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(s,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:l(()=>[t(T,{value:e(n).lang,"onUpdate:value":a[11]||(a[11]=u=>e(n).lang=u),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(s,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:l(()=>[t(T,{value:e(n).f_channel,"onUpdate:value":a[12]||(a[12]=u=>e(n).f_channel=u),placeholder:"请选择渠道",type:"channels"},null,8,["value"])]),_:1}),t(s,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:l(()=>[t(j,{value:e(n).f_s_ids,"onUpdate:value":a[13]||(a[13]=u=>e(n).f_s_ids=u),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(s,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:l(()=>[t(j,{value:e(n).f_lv_ids,"onUpdate:value":a[14]||(a[14]=u=>e(n).f_lv_ids=u),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1})],64)):k("",!0),t(s,{"wrapper-col":{offset:6,span:16}},{default:l(()=>[t(C,{type:"primary",onClick:w,loading:e(r)},{default:l(()=>[m("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),it=E({__name:"Index",setup(d){K.extend(z);const _=G({editVisible:!1,editId:0}),g=q(),x=()=>g.value.requestTableData(!0),S=(p,a)=>{_.editVisible=p,_.editId=a||0},R=(p,a)=>B(`确定要${a==="up"?"上移":"下移"}此条数据吗？`,ye,{id:p,type:a}).then(()=>x()),n=p=>B("确定要复制此条数据并生成一份新数据吗？",ce,{id:p}).then(()=>x()),r=p=>B("删除后游戏内浮层将不展示该项，即刻生效，是否确认删除？",ke,{id:p}).then(()=>x()),U=q([]),w=(p,a,o)=>{U.value[o]=!0,a.status=1-p,X.confirm({title:"提示",content:p===0?"关闭后游戏内浮层将不展示该项，即刻生效，是否确认关闭？":"开启后游戏内浮层将展示该项，即刻生效，是否确认开启？",okText:"确定",cancelText:"取消",onOk:()=>{U.value[o]=!1,ge(a.id,p).finally(()=>x())},onCancel:()=>{U.value[o]=!1}})};return(p,a)=>{const o=i("PlusOutlined"),s=i("a-button"),b=i("a-image"),L=i("LangKey"),A=i("a-switch"),M=i("FilterCell"),P=i("a-divider"),D=i("ArrowUpOutlined"),T=i("a-typography-link"),j=i("ArrowDownOutlined"),C=i("a-space"),$=i("CustomTable");return f(),Y(V,null,[t($,{ref_key:"RefCustomTable",ref:g,"data-api":e(pe),params:{},columns:e(se),pagination:!1},{leftTool:l(()=>[t(s,{type:"primary",onClick:a[0]||(a[0]=c=>S(!0))},{icon:l(()=>[t(o)]),default:l(()=>[m(" 新增 ")]),_:1})]),bodyCell:l(({record:c,column:v,index:u})=>[v.key==="img_id"?(f(),h(b,{key:0,src:c.img_id,height:60},null,8,["src"])):k("",!0),v.key==="times"?(f(),Y(V,{key:1},[m(N(c.offline_at?`${e(K).utc(c.online_at*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${e(K).utc(c.offline_at*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):k("",!0),v.key==="entrance_name"?(f(),h(L,{key:2,"lang-key":c.entrance_name,i18n_name:c.i18n_name},null,8,["lang-key","i18n_name"])):k("",!0),v.key==="status"?(f(),h(A,{key:3,checked:c.status,"onUpdate:checked":y=>c.status=y,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(U)[u],onClick:y=>w(y,c,u)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),v.key==="link_type"?(f(),Y(V,{key:4},[m(N(c.link_type?e(Z)[c.link_type].label:"-"),1)],64)):k("",!0),v.key==="filter"?(f(),h(M,{key:5,record:c},null,8,["record"])):k("",!0),v.key==="action"?(f(),h(C,{key:6},{split:l(()=>[t(P,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(T,{onClick:y=>R(c.id,"up"),disabled:c.first},{default:l(()=>[t(D),m("上移")]),_:2},1032,["onClick","disabled"]),t(T,{onClick:y=>R(c.id,"down"),disabled:c.last},{default:l(()=>[t(j),m("下移")]),_:2},1032,["onClick","disabled"]),t(T,{type:"success",onClick:y=>n(c.id)},{default:l(()=>[m("复制")]),_:2},1032,["onClick"]),t(T,{onClick:y=>S(!0,c.id)},{default:l(()=>[m("编辑")]),_:2},1032,["onClick"]),t(T,{type:"danger",onClick:y=>r(c.id)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]),_:1},8,["data-api","columns"]),e(_).editVisible?(f(),h(nt,{key:0,"edit-id":e(_).editId,onClose:a[1]||(a[1]=c=>S(!1)),onRefresh:x},null,8,["edit-id"])):k("",!0)],64)}}}),bt=Object.freeze(Object.defineProperty({__proto__:null,default:it},Symbol.toStringTag,{value:"Module"})),ot=E({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(d,{emit:_}){const g=d;K.extend(z);const x=q(!0),S=q(!1),R=()=>{S.value=!0,we(g.editId).then(p=>{n.value={...n.value,...p},p.online_at&&p.offline_at?(n.value.is_forever=0,n.value.times=[p.online_at,p.offline_at]):(n.value.is_forever=1,n.value.times=void 0)}).finally(()=>S.value=!1)};g.editId&&R();const n=q({id:0,entrance_name:void 0,link_type:1,link_url:"",img_id:"",is_forever:0,times:void 0,is_red_dot:0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:""}),r=q(!1),U=q(),w=()=>{U.value.validate().then(()=>{r.value=!0;const{id:p,...a}=n.value;a.is_forever===0&&a.times?(a.online_at=a.times[0],a.offline_at=a.times[1]):(a.online_at=K.utc().unix(),a.offline_at=""),g.editId?Ie(p,a).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{r.value=!1}):he(a).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{r.value=!1})}).catch(()=>{})};return(p,a)=>{const o=i("SelectLang"),s=i("a-form-item"),b=i("a-select"),L=i("a-input"),A=i("SelectImg"),M=i("a-radio-group"),P=i("SelectDateTime"),D=i("a-radio"),T=i("SelectWithAllComp"),j=i("a-textarea"),C=i("a-button"),$=i("a-form"),c=i("a-spin"),v=i("a-drawer");return f(),h(v,{open:e(x),"onUpdate:open":a[15]||(a[15]=u=>Q(x)?x.value=u:null),title:g.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:a[16]||(a[16]=u=>!u&&_("close"))},{default:l(()=>[t(c,{spinning:e(S)},{default:l(()=>[t($,{model:e(n),name:"basic",ref_key:"formRef",ref:U,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(s,{label:"入口名称",name:"entrance_name",rules:[{required:!0,message:"请选择入口名称多语言"}]},{default:l(()=>[t(o,{value:e(n).entrance_name,"onUpdate:value":a[0]||(a[0]=u=>e(n).entrance_name=u)},null,8,["value"])]),_:1}),t(s,{label:"跳转类型",name:"link_type",rules:[{required:!0,message:"请选择跳转类型"}]},{default:l(()=>[t(b,{value:e(n).link_type,"onUpdate:value":a[1]||(a[1]=u=>e(n).link_type=u),options:Object.values(e(Z)),placeholder:"请选择跳转类型"},null,8,["value","options"])]),_:1}),t(s,{label:"跳转URL",name:"link_url",rules:[{required:!0,message:"请输入跳转URL"}]},{default:l(()=>[t(L,{value:e(n).link_url,"onUpdate:value":a[2]||(a[2]=u=>e(n).link_url=u),placeholder:"请输入跳转URL"},null,8,["value"])]),_:1}),t(s,{label:"入口icon",name:"img_id",rules:[{required:!0,message:"请上传入口icon"}]},{default:l(()=>[t(A,{value:e(n).img_id,"onUpdate:value":a[3]||(a[3]=u=>e(n).img_id=u),"width-height":[80,80]},null,8,["value"])]),_:1}),t(s,{label:"上线时间",onChange:a[5]||(a[5]=u=>e(n).times=void 0)},{default:l(()=>[t(M,{value:e(n).is_forever,"onUpdate:value":a[4]||(a[4]=u=>e(n).is_forever=u),options:[{label:"永久",value:1},{label:"定时",value:0}]},null,8,["value"])]),_:1}),e(n).is_forever===0?(f(),h(s,{key:0,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:l(()=>[t(P,{value:e(n).times,"onUpdate:value":a[6]||(a[6]=u=>e(n).times=u)},null,8,["value"])]),_:1})):k("",!0),t(s,{label:"是否需要红点",name:"is_red_dot"},{default:l(()=>[t(M,{value:e(n).is_red_dot,"onUpdate:value":a[7]||(a[7]=u=>e(n).is_red_dot=u)},{default:l(()=>[t(D,{value:1},{default:l(()=>[m("需要")]),_:1}),t(D,{value:0},{default:l(()=>[m("不需要")]),_:1})]),_:1},8,["value"])]),_:1}),t(s,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:l(()=>[t(M,{value:e(n).is_filter,"onUpdate:value":a[8]||(a[8]=u=>e(n).is_filter=u),onChange:a[9]||(a[9]=u=>e(ae)(e(n),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:l(()=>[t(D,{value:1},{default:l(()=>[m("开启")]),_:1}),t(D,{value:0},{default:l(()=>[m("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(n).is_filter===1?(f(),Y(V,{key:1},[t(s,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:l(()=>[t(T,{value:e(n).f_os,"onUpdate:value":a[10]||(a[10]=u=>e(n).f_os=u),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(s,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:l(()=>[t(T,{value:e(n).lang,"onUpdate:value":a[11]||(a[11]=u=>e(n).lang=u),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(s,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:l(()=>[t(T,{value:e(n).f_channel,"onUpdate:value":a[12]||(a[12]=u=>e(n).f_channel=u),placeholder:"请选择渠道",type:"channels"},null,8,["value"])]),_:1}),t(s,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:l(()=>[t(j,{value:e(n).f_s_ids,"onUpdate:value":a[13]||(a[13]=u=>e(n).f_s_ids=u),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(s,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:l(()=>[t(j,{value:e(n).f_lv_ids,"onUpdate:value":a[14]||(a[14]=u=>e(n).f_lv_ids=u),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1})],64)):k("",!0),t(s,{"wrapper-col":{offset:6,span:16}},{default:l(()=>[t(C,{type:"primary",onClick:w,loading:e(r)},{default:l(()=>[m("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),st=E({__name:"Index",setup(d){K.extend(z);const _=G({editVisible:!1,editId:0}),g=q(),x=()=>g.value.requestTableData(!0),S=(p,a)=>{_.editVisible=p,_.editId=a||0},R=(p,a)=>B(`确定要${a==="up"?"上移":"下移"}此条数据吗？`,Ue,{id:p,type:a}).then(()=>x()),n=p=>B("确定要复制此条数据并生成一份新数据吗？",Ce,{id:p}).then(()=>x()),r=p=>B("删除后私域首页将不展示该项，即刻生效，是否确认删除？",De,{id:p}).then(()=>x()),U=q([]),w=(p,a,o)=>{U.value[o]=!0,a.status=1-p;const s=Math.floor(K.utc().valueOf()/1e3);if(a.status===0&&a.offline_at&&s>a.offline_at){ie.warning("已过期，请修改上线时间后操作"),U.value[o]=!1;return}X.confirm({title:"提示",content:p===0?"关闭后私域首页将不展示该项，即刻生效，是否确认关闭？":"开启后私域首页将展示该项，即刻生效，是否确认开启？",okText:"确定",cancelText:"取消",onOk:()=>{U.value[o]=!1,xe(a.id,p).finally(()=>x())},onCancel:()=>{U.value[o]=!1}})};return(p,a)=>{const o=i("PlusOutlined"),s=i("a-button"),b=i("a-image"),L=i("a-switch"),A=i("LangKey"),M=i("FilterCell"),P=i("a-divider"),D=i("ArrowUpOutlined"),T=i("a-typography-link"),j=i("ArrowDownOutlined"),C=i("a-space"),$=i("CustomTable");return f(),Y(V,null,[t($,{ref_key:"RefCustomTable",ref:g,"data-api":e(be),params:{},columns:e(se),pagination:!1},{leftTool:l(()=>[t(s,{type:"primary",onClick:a[0]||(a[0]=c=>S(!0))},{icon:l(()=>[t(o)]),default:l(()=>[m(" 新增 ")]),_:1})]),bodyCell:l(({record:c,column:v,index:u})=>[v.key==="img_id"?(f(),h(b,{key:0,src:c.img_id,height:60},null,8,["src"])):k("",!0),v.key==="times"?(f(),Y(V,{key:1},[m(N(c.offline_at?`${e(K).utc(c.online_at*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${e(K).utc(c.offline_at*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):k("",!0),v.key==="status"?(f(),h(L,{key:2,checked:c.status,"onUpdate:checked":y=>c.status=y,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(U)[u],onClick:y=>w(y,c,u)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),v.key==="entrance_name"?(f(),h(A,{key:3,"lang-key":c.entrance_name,i18n_name:c.i18n_name},null,8,["lang-key","i18n_name"])):k("",!0),v.key==="link_type"?(f(),Y(V,{key:4},[m(N(c.link_type?e(Z)[c.link_type].label:"-"),1)],64)):k("",!0),v.key==="filter"?(f(),h(M,{key:5,record:c},null,8,["record"])):k("",!0),v.key==="action"?(f(),h(C,{key:6},{split:l(()=>[t(P,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(T,{onClick:y=>R(c.id,"up"),disabled:c.first},{default:l(()=>[t(D),m("上移")]),_:2},1032,["onClick","disabled"]),t(T,{onClick:y=>R(c.id,"down"),disabled:c.last},{default:l(()=>[t(j),m("下移")]),_:2},1032,["onClick","disabled"]),t(T,{type:"success",onClick:y=>n(c.id)},{default:l(()=>[m("复制")]),_:2},1032,["onClick"]),t(T,{onClick:y=>S(!0,c.id)},{default:l(()=>[m("编辑")]),_:2},1032,["onClick"]),t(T,{type:"danger",onClick:y=>r(c.id)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]),_:1},8,["data-api","columns"]),e(_).editVisible?(f(),h(ot,{key:0,"edit-id":e(_).editId,onClose:a[1]||(a[1]=c=>S(!1)),onRefresh:x},null,8,["edit-id"])):k("",!0)],64)}}}),ht=Object.freeze(Object.defineProperty({__proto__:null,default:st},Symbol.toStringTag,{value:"Module"})),ut=E({__name:"IndexForm",props:["editId"],emits:["close","refresh"],setup(d,{emit:_}){const g=d;K.extend(z);const{configState:x}=de(le()),S=q(!0),R=q(!1),n=()=>{R.value=!0,oe(g.editId).then(a=>{r.value={...r.value,...a},r.value.show_page=a.show_page.split("|"),a.online_at&&a.offline_at?(r.value.is_forever=0,r.value.times=[a.online_at,a.offline_at]):(r.value.is_forever=1,r.value.times=void 0)}).finally(()=>R.value=!1)};g.editId&&n();const r=q({id:0,group_name:"",group_title:void 0,style_type:1,show_page:[],is_forever:0,times:void 0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:""}),U=q(!1),w=q(),p=()=>{w.value.validate().then(()=>{U.value=!0;const{id:a,...o}=r.value;o.show_page=typeof o.show_page!="string"?o.show_page.join("|"):o.show_page,console.log("data.times",o.times),o.is_forever===0&&o.times?(o.online_at=o.times[0],o.offline_at=o.times[1]):(o.online_at=0,o.offline_at=0),g.editId?Te(a,o).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{U.value=!1}):Se(o).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{U.value=!1})}).catch(()=>{})};return(a,o)=>{const s=i("a-input"),b=i("a-form-item"),L=i("a-image"),A=i("a-radio"),M=i("a-radio-group"),P=i("SelectLang"),D=i("a-checkbox"),T=i("a-checkbox-group"),j=i("SelectDateTime"),C=i("a-button"),$=i("a-form"),c=i("a-spin"),v=i("a-drawer");return f(),h(v,{open:e(S),"onUpdate:open":o[8]||(o[8]=u=>Q(S)?S.value=u:null),title:g.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:o[9]||(o[9]=u=>!u&&_("close"))},{default:l(()=>[t(c,{spinning:e(R)},{default:l(()=>[t($,{model:e(r),name:"basic",ref_key:"formRef",ref:w,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(b,{label:"组名称",name:"group_name",rules:[{required:!0,message:"请输入组名称"}]},{default:l(()=>[t(s,{value:e(r).group_name,"onUpdate:value":o[0]||(o[0]=u=>e(r).group_name=u),placeholder:"请输入banner组名称，仅后台展示用"},null,8,["value"])]),_:1}),t(b,{label:"样式"},{default:l(()=>[(f(!0),Y(V,null,te(e(ee),(u,y)=>(f(),Y("div",{key:y,style:{"line-height":"32px","margin-bottom":"5px"}},[t(M,{value:e(r).style_type,"onUpdate:value":o[1]||(o[1]=W=>e(r).style_type=W),onChange:o[2]||(o[2]=()=>e(r).group_title=void 0)},{default:l(()=>[t(A,{value:u.value},{default:l(()=>[m(N(u.label)+" ",1),t(L,{src:u.img,preview:!1,style:{height:"100px",border:"1px solid #ccc","border-radius":"5px"}},null,8,["src"])]),_:2},1032,["value"])]),_:2},1032,["value"])]))),128))]),_:1}),e(r).style_type===3?(f(),h(b,{key:0,label:"标题",name:"group_title",rules:[{required:!0,message:"请选择标题"}]},{default:l(()=>[t(P,{value:e(r).group_title,"onUpdate:value":o[3]||(o[3]=u=>e(r).group_title=u)},null,8,["value"])]),_:1})):k("",!0),t(b,{label:"展示页面",name:"show_page",rules:[{required:!0,message:"请选择展示页面"}]},{default:l(()=>[t(T,{value:e(r).show_page,"onUpdate:value":o[4]||(o[4]=u=>e(r).show_page=u)},{default:l(()=>[(f(!0),Y(V,null,te(e(x).show_pages,(u,y)=>(f(),h(D,{key:y,value:u.value,name:"show_page"},{default:l(()=>[m(N(u.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1}),t(b,{label:"上线时间",onChange:o[6]||(o[6]=u=>e(r).times=void 0)},{default:l(()=>[t(M,{value:e(r).is_forever,"onUpdate:value":o[5]||(o[5]=u=>e(r).is_forever=u),options:[{label:"永久",value:1},{label:"定时",value:0}]},null,8,["value"])]),_:1}),e(r).is_forever===0?(f(),h(b,{key:1,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:l(()=>[t(j,{value:e(r).times,"onUpdate:value":o[7]||(o[7]=u=>e(r).times=u)},null,8,["value"])]),_:1})):k("",!0),t(b,{"wrapper-col":{offset:6,span:16}},{default:l(()=>[t(C,{type:"primary",onClick:p,loading:e(U)},{default:l(()=>[m("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),dt=["src"],rt=E({__name:"Index",setup(d){K.extend(z);const _=G({editVisible:!1,editId:0}),g=re(),{getConfItem:x}=le(),S=q(),R=()=>S.value.requestTableData(!0),n=(a,o)=>{_.editVisible=a,_.editId=o||0},r=a=>B("确定要复制此条数据并生成一份新数据吗？",qe,{id:a}).then(()=>R()),U=(a,o)=>B(`确定要删除${o?"选中的":"此条"}数据吗？`,Le,{id:a}).then(()=>R()),w=q([]),p=(a,o,s)=>{w.value[s]=!0,o.status=1-a;const b=Math.floor(K.utc().valueOf()/1e3);if(o.status===0&&o.offline_at&&b>o.offline_at){ie.warning("已过期，请修改上线时间后操作"),w.value[s]=!1;return}X.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{w.value[s]=!1,Oe(o.id,a).finally(()=>R())},onCancel:()=>{w.value[s]=!1}})};return(a,o)=>{const s=i("PlusOutlined"),b=i("a-button"),L=i("a-image"),A=i("a-typography-link"),M=i("a-popover"),P=i("a-switch"),D=i("a-tag"),T=i("a-divider"),j=i("a-space"),C=i("CustomTable");return f(),Y(V,null,[t(C,{ref_key:"RefCustomTable",ref:S,"data-api":e($e),params:{},columns:e(at)},{leftTool:l(()=>[t(b,{type:"primary",onClick:o[0]||(o[0]=$=>n(!0))},{icon:l(()=>[t(s)]),default:l(()=>[m(" 新增 ")]),_:1})]),bodyCell:l(({record:$,column:c,index:v})=>[c.key==="img_id"?(f(),h(L,{key:0,src:$.img_id,height:60},null,8,["src"])):k("",!0),c.key==="times"?(f(),Y(V,{key:1},[m(N($.offline_at?`${e(K).utc($.online_at*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${e(K).utc($.offline_at*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):k("",!0),c.key==="style_type"?(f(),Y(V,{key:2},[e(ee).filter(u=>u.value===$.style_type)[0]?(f(),h(M,{key:0,placement:"right"},{content:l(()=>[_e("img",{src:e(ee).filter(u=>u.value===$.style_type)[0].img,alt:""},null,8,dt)]),default:l(()=>[t(A,null,{default:l(()=>[m(N(e(ee).filter(u=>u.value===$.style_type)[0].label),1)]),_:2},1024)]),_:2},1024)):(f(),Y(V,{key:1},[m(N($.style_type||"-"),1)],64))],64)):k("",!0),c.key==="status"?(f(),h(P,{key:3,checked:$.status,"onUpdate:checked":u=>$.status=u,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(w)[v],onClick:u=>p(u,$,v)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),c.key==="show_page"?(f(!0),Y(V,{key:4},te($.show_page.split("|"),(u,y)=>(f(),h(D,{key:y},{default:l(()=>{var W;return[m(N(((W=e(x)("show_pages",u))==null?void 0:W.label)||u),1)]}),_:2},1024))),128)):k("",!0),c.key==="action"?(f(),h(j,{key:5},{split:l(()=>[t(T,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(A,{onClick:u=>n(!0,$.id)},{default:l(()=>[m("编辑")]),_:2},1032,["onClick"]),t(A,{type:"success",onClick:u=>r($.id)},{default:l(()=>[m("复制")]),_:2},1032,["onClick"]),t(A,{onClick:u=>e(g).push(`/operation/banner/detail/${$.id}`)},{default:l(()=>[m("详情")]),_:2},1032,["onClick"]),t(A,{type:"danger",onClick:u=>U($.id,!1)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]),_:1},8,["data-api","columns"]),e(_).editVisible?(f(),h(ut,{key:0,"edit-id":e(_).editId,onClose:o[1]||(o[1]=$=>n(!1)),onRefresh:R},null,8,["edit-id"])):k("",!0)],64)}}}),wt=Object.freeze(Object.defineProperty({__proto__:null,default:rt},Symbol.toStringTag,{value:"Module"})),_t=E({__name:"DetailForm",props:["editId","styleConfig"],emits:["close","refresh"],setup(d,{emit:_}){const g=d;K.extend(z);const x=q(!0),S=q(!1),R=()=>{S.value=!0,Ye(g.editId).then(a=>{r.value={...r.value,...a},a.online_at&&a.offline_at?(r.value.is_forever=0,r.value.times=[a.online_at,a.offline_at]):(r.value.is_forever=1,r.value.times=void 0)}).finally(()=>S.value=!1)};g.editId&&R();const n=ne(),r=q({id:0,activity_banner_groups_id:n.params.id&&Number(n.params.id)||0,title:void 0,type:1,link_url:"",img_id:"",is_forever:0,times:void 0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:""}),U=q(!1),w=q(),p=()=>{w.value.validate().then(()=>{U.value=!0;const{id:a,...o}=r.value;o.is_forever===0&&o.times?(o.online_at=o.times[0],o.offline_at=o.times[1]):(o.online_at=0,o.offline_at=""),g.editId?Ve(a,o).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{U.value=!1}):Ae(o).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{U.value=!1})}).catch(()=>{})};return(a,o)=>{const s=i("SelectLang"),b=i("a-form-item"),L=i("a-select"),A=i("a-input"),M=i("SelectImg"),P=i("a-radio-group"),D=i("SelectDateTime"),T=i("a-radio"),j=i("SelectWithAllComp"),C=i("a-textarea"),$=i("a-button"),c=i("a-form"),v=i("a-spin"),u=i("a-drawer");return f(),h(u,{open:e(x),"onUpdate:open":o[14]||(o[14]=y=>Q(x)?x.value=y:null),title:g.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:o[15]||(o[15]=y=>!y&&_("close"))},{default:l(()=>[t(v,{spinning:e(S)},{default:l(()=>[t(c,{model:e(r),name:"basic",ref_key:"formRef",ref:w,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(b,{label:"banner名称",name:"title",rules:[{required:!0,message:"请选择banner名称"}]},{default:l(()=>[t(s,{value:e(r).title,"onUpdate:value":o[0]||(o[0]=y=>e(r).title=y)},null,8,["value"])]),_:1}),t(b,{label:"跳转类型",name:"type",rules:[{required:!0,message:"请选择跳转类型"}]},{default:l(()=>[t(L,{value:e(r).type,"onUpdate:value":o[1]||(o[1]=y=>e(r).type=y),options:Object.values(e(ue)),placeholder:"请选择跳转类型"},null,8,["value","options"])]),_:1}),t(b,{label:"跳转URL",name:"link_url",rules:[{required:!0,message:"请输入跳转URL"}]},{default:l(()=>[t(A,{value:e(r).link_url,"onUpdate:value":o[2]||(o[2]=y=>e(r).link_url=y),placeholder:"请输入跳转URL"},null,8,["value"])]),_:1}),t(b,{label:"banner图",name:"img_id",rules:[{required:!0,message:"请上传banner图"}]},{default:l(()=>[t(M,{value:e(r).img_id,"onUpdate:value":o[3]||(o[3]=y=>e(r).img_id=y),"width-height":[d.styleConfig.width,d.styleConfig.height]},null,8,["value","width-height"])]),_:1}),t(b,{label:"上线时间",onChange:o[5]||(o[5]=y=>e(r).times=void 0)},{default:l(()=>[t(P,{value:e(r).is_forever,"onUpdate:value":o[4]||(o[4]=y=>e(r).is_forever=y),options:[{label:"永久",value:1},{label:"定时",value:0}]},null,8,["value"])]),_:1}),e(r).is_forever===0?(f(),h(b,{key:0,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:l(()=>[t(D,{value:e(r).times,"onUpdate:value":o[6]||(o[6]=y=>e(r).times=y)},null,8,["value"])]),_:1})):k("",!0),t(b,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:l(()=>[t(P,{value:e(r).is_filter,"onUpdate:value":o[7]||(o[7]=y=>e(r).is_filter=y),onChange:o[8]||(o[8]=y=>e(ae)(e(r),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:l(()=>[t(T,{value:1},{default:l(()=>[m("开启")]),_:1}),t(T,{value:0},{default:l(()=>[m("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(r).is_filter===1?(f(),Y(V,{key:1},[t(b,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:l(()=>[t(j,{value:e(r).f_os,"onUpdate:value":o[9]||(o[9]=y=>e(r).f_os=y),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(b,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:l(()=>[t(j,{value:e(r).lang,"onUpdate:value":o[10]||(o[10]=y=>e(r).lang=y),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(b,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:l(()=>[t(j,{value:e(r).f_channel,"onUpdate:value":o[11]||(o[11]=y=>e(r).f_channel=y),placeholder:"请选择渠道",type:"channels"},null,8,["value"])]),_:1}),t(b,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:l(()=>[t(C,{value:e(r).f_s_ids,"onUpdate:value":o[12]||(o[12]=y=>e(r).f_s_ids=y),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(b,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:l(()=>[t(C,{value:e(r).f_lv_ids,"onUpdate:value":o[13]||(o[13]=y=>e(r).f_lv_ids=y),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1})],64)):k("",!0),t(b,{"wrapper-col":{offset:6,span:16}},{default:l(()=>[t($,{type:"primary",onClick:p,loading:e(U)},{default:l(()=>[m("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),pt=E({__name:"Detail",setup(d){K.extend(z);const _=ne(),g=G({editVisible:!1,editId:0,style_type:0,group_id:_.params.id&&Number(_.params.id)||0,bannerInfo:{}}),{getConfItem:x}=le();(()=>{oe(g.group_id).then(o=>{g.bannerInfo=o,g.style_type=o.style_type})})();const R=q(),n=()=>R.value.requestTableData(!0),r=(o,s)=>{g.editVisible=o,g.editId=s||0},U=(o,s)=>B(`确定要${s==="up"?"上移":"下移"}此条数据吗？`,Pe,{id:o,params:{type:s,group_id:g.group_id}}).then(()=>n()),w=(o,s)=>B(`确定要删除${s?"选中的":"此条"}数据吗？`,je,{id:o}).then(()=>n()),p=q([]),a=(o,s,b)=>{p.value[b]=!0,s.status=1-o,X.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{p.value[b]=!1,Me(s.id,o).finally(()=>n())},onCancel:()=>{p.value[b]=!1}})};return(o,s)=>{const b=i("a-typography-text"),L=i("a-tag"),A=i("a-space"),M=i("a-card"),P=i("PlusOutlined"),D=i("a-button"),T=i("a-image"),j=i("a-switch"),C=i("LangKey"),$=i("FilterCell"),c=i("a-divider"),v=i("ArrowUpOutlined"),u=i("a-typography-link"),y=i("ArrowDownOutlined"),W=i("CustomTable");return f(),Y(V,null,[t(M,null,{default:l(()=>[t(A,{size:"large"},{default:l(()=>[t(b,{strong:""},{default:l(()=>[m(N(e(g).bannerInfo.group_name)+"(ID: "+N(e(g).bannerInfo.id)+")",1)]),_:1}),t(A,null,{default:l(()=>[t(b,{strong:""},{default:l(()=>[m("展示页面：")]),_:1}),(f(!0),Y(V,null,te((e(g).bannerInfo.show_page||"").split("|"),(H,I)=>(f(),h(L,{key:I},{default:l(()=>{var F;return[m(N(((F=e(x)("show_pages",H))==null?void 0:F.label)||H),1)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}),t(W,{ref_key:"RefCustomTable",ref:R,"data-api":e(Re),params:{group_id:e(g).group_id},columns:e(lt),pagination:!1},{leftTool:l(()=>[t(D,{type:"primary",onClick:s[0]||(s[0]=H=>r(!0))},{icon:l(()=>[t(P)]),default:l(()=>[m(" 新增 ")]),_:1})]),bodyCell:l(({record:H,column:I,index:F})=>[I.key==="img_id"?(f(),h(T,{key:0,src:H.img_id,height:60},null,8,["src"])):k("",!0),I.key==="times"?(f(),Y(V,{key:1},[m(N(H.offline_at?`${e(K).utc(H.online_at*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${e(K).utc(H.offline_at*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):k("",!0),I.key==="status"?(f(),h(j,{key:2,checked:H.status,"onUpdate:checked":J=>H.status=J,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(p)[F],onClick:J=>a(J,H,F)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),I.key==="title"?(f(),h(C,{"lang-key":H.title,key:H.title,i18n_name:H.i18n_name},null,8,["lang-key","i18n_name"])):k("",!0),I.key==="type"?(f(),Y(V,{key:4},[m(N(H.type?e(ue)[H.type].label:"-"),1)],64)):k("",!0),I.key==="filter"?(f(),h($,{key:5,record:H},null,8,["record"])):k("",!0),I.key==="action"?(f(),h(A,{key:6},{split:l(()=>[t(c,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(u,{onClick:J=>U(H.id,"up"),disabled:H.first},{default:l(()=>[t(v),m("上移")]),_:2},1032,["onClick","disabled"]),t(u,{onClick:J=>U(H.id,"down"),disabled:H.last},{default:l(()=>[t(y),m("下移")]),_:2},1032,["onClick","disabled"]),t(u,{onClick:J=>r(!0,H.id)},{default:l(()=>[m("编辑")]),_:2},1032,["onClick"]),t(u,{type:"danger",onClick:J=>w(H.id,!1)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]),_:1},8,["data-api","params","columns"]),e(g).editVisible?(f(),h(_t,{key:0,"edit-id":e(g).editId,onClose:s[1]||(s[1]=H=>r(!1)),onRefresh:n,"style-config":e(ee).filter(H=>H.value===e(g).style_type)[0]},null,8,["edit-id","style-config"])):k("",!0)],64)}}}),Ct=Object.freeze(Object.defineProperty({__proto__:null,default:pt},Symbol.toStringTag,{value:"Module"})),ft=E({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(d,{emit:_}){const g=d,x=q(!0),S=q(!1),R=()=>{S.value=!0,Ne(g.editId).then(p=>{n.value=p,p.validity_type===2?n.value.times=[p.start_time,p.end_time]:n.value.times=void 0}).finally(()=>S.value=!1)};g.editId&&R();const n=q({id:0,name:void 0,target_url:"",validity_type:1,start_time:void 0,end_time:void 0,weight:0,img_url:"",img_id:"",is_forever:0,times:void 0,is_filter:0,f_os:"",f_channel:"",f_lang:"",f_s_ids:"",f_lv_ids:""}),r=q(!1),U=q(),w=()=>{U.value.validate().then(()=>{r.value=!0;const{id:p,...a}=n.value;a.validity_type===2&&a.times?(a.start_time=a.times[0],a.end_time=a.times[1]):(a.start_time=K.utc().unix(),a.end_time=0),g.editId?Be(p,a).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{r.value=!1}):Ke(a).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{r.value=!1})}).catch(()=>{})};return(p,a)=>{const o=i("a-input"),s=i("a-form-item"),b=i("a-radio-group"),L=i("SelectDateTime"),A=i("a-input-number"),M=i("SelectImg"),P=i("a-radio"),D=i("SelectWithAllComp"),T=i("a-textarea"),j=i("a-button"),C=i("a-form"),$=i("a-spin"),c=i("a-drawer");return f(),h(c,{open:e(x),"onUpdate:open":a[14]||(a[14]=v=>Q(x)?x.value=v:null),title:g.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:a[15]||(a[15]=v=>!v&&_("close"))},{default:l(()=>[t($,{spinning:e(S)},{default:l(()=>[t(C,{model:e(n),name:"name",ref_key:"formRef",ref:U,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(s,{label:"后台名称",name:"name",rules:[{required:!0,message:"请输入后台名称"}]},{default:l(()=>[t(o,{allowClear:"",value:e(n).name,"onUpdate:value":a[0]||(a[0]=v=>e(n).name=v),placeholder:"请输入后台名称"},null,8,["value"])]),_:1}),t(s,{label:"跳转URL",name:"target_url",rules:[{required:!0,message:"请输入跳转URL"}]},{default:l(()=>[t(o,{allowClear:"",value:e(n).target_url,"onUpdate:value":a[1]||(a[1]=v=>e(n).target_url=v),placeholder:"请输入跳转URL"},null,8,["value"])]),_:1}),t(s,{label:"上线时间",onChange:a[3]||(a[3]=v=>e(n).times=void 0)},{default:l(()=>[t(b,{value:e(n).validity_type,"onUpdate:value":a[2]||(a[2]=v=>e(n).validity_type=v),options:[{label:"永久",value:1},{label:"定时",value:2}]},null,8,["value"])]),_:1}),e(n).validity_type===2?(f(),h(s,{key:0,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:l(()=>[t(L,{value:e(n).times,"onUpdate:value":a[4]||(a[4]=v=>e(n).times=v)},null,8,["value"])]),_:1})):k("",!0),t(s,{label:"权重",name:"weight",rules:[{required:!0,message:"请输入权重"}]},{default:l(()=>[t(A,{style:{width:"100%"},value:e(n).weight,"onUpdate:value":a[5]||(a[5]=v=>e(n).weight=v),min:0,max:15e5,placeholder:"请输入权重"},null,8,["value"])]),_:1}),t(s,{label:"图片配置",name:"img_url",rules:[{required:!0,message:"请上传图片配置"}]},{default:l(()=>[t(M,{value:e(n).img_url,"onUpdate:value":a[6]||(a[6]=v=>e(n).img_url=v),"width-height":[40,40],tips:"png格式，建议尺寸比例: "},null,8,["value"])]),_:1}),t(s,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:l(()=>[t(b,{value:e(n).is_filter,"onUpdate:value":a[7]||(a[7]=v=>e(n).is_filter=v),onChange:a[8]||(a[8]=v=>e(ae)(e(n),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:l(()=>[t(P,{value:1},{default:l(()=>[m("开启")]),_:1}),t(P,{value:0},{default:l(()=>[m("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(n).is_filter===1?(f(),Y(V,{key:1},[t(s,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:l(()=>[t(D,{value:e(n).f_os,"onUpdate:value":a[9]||(a[9]=v=>e(n).f_os=v),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(s,{label:"语种",name:"f_lang",rules:[{required:!0,message:"请选择语种"}]},{default:l(()=>[t(D,{value:e(n).f_lang,"onUpdate:value":a[10]||(a[10]=v=>e(n).f_lang=v),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(s,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:l(()=>[t(D,{value:e(n).f_channel,"onUpdate:value":a[11]||(a[11]=v=>e(n).f_channel=v),placeholder:"请选择渠道",type:"channels"},null,8,["value"])]),_:1}),t(s,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:l(()=>[t(T,{value:e(n).f_s_ids,"onUpdate:value":a[12]||(a[12]=v=>e(n).f_s_ids=v),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(s,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:l(()=>[t(T,{value:e(n).f_lv_ids,"onUpdate:value":a[13]||(a[13]=v=>e(n).f_lv_ids=v),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1})],64)):k("",!0),t(s,{"wrapper-col":{offset:6,span:16}},{default:l(()=>[t(j,{type:"primary",onClick:w,loading:e(r)},{default:l(()=>[m("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),mt=E({__name:"Index",setup(d){K.extend(z);const _=G({editVisible:!1,editId:0}),g=q(),x=()=>g.value.requestTableData(!0),S=(U,w)=>{_.editVisible=U,_.editId=w||0},R=U=>B("删除后游戏内将不展示该项，即刻生效，是否确认删除？",Fe,{id:U}).then(()=>x()),n=q([]),r=(U,w,p)=>{n.value[p]=!0,w.is_online=1-U,X.confirm({title:"提示",content:"切换状态后即刻生效，是否确认操作？",okText:"确定",cancelText:"取消",onOk:()=>{n.value[p]=!1,Ee(w.id,U).finally(()=>x())},onCancel:()=>{n.value[p]=!1}})};return(U,w)=>{const p=i("PlusOutlined"),a=i("a-button"),o=i("a-image"),s=i("a-switch"),b=i("FilterCell"),L=i("a-divider"),A=i("a-typography-link"),M=i("a-space"),P=i("CustomTable");return f(),Y(V,null,[t(P,{ref_key:"RefCustomTable",ref:g,"data-api":e(He),params:{},columns:e(et),pagination:!1},{leftTool:l(()=>[t(a,{type:"primary",onClick:w[0]||(w[0]=D=>S(!0))},{icon:l(()=>[t(p)]),default:l(()=>[m(" 新增 ")]),_:1})]),bodyCell:l(({record:D,column:T,index:j})=>[T.key==="img_url"?(f(),h(o,{key:0,src:D.img_url,height:60},null,8,["src"])):k("",!0),T.key==="validity_type"?(f(),Y(V,{key:1},[m(N(D.validity_type===2?`${e(K).utc(D.start_time*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${e(K).utc(D.end_time*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):k("",!0),T.key==="is_online"?(f(),h(s,{key:2,checked:D.is_online,"onUpdate:checked":C=>D.is_online=C,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(n)[j],onClick:C=>r(C,D,j)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),T.key==="link_type"?(f(),Y(V,{key:3},[m(N(D.link_type?e(Z)[D.link_type].label:"-"),1)],64)):k("",!0),T.key==="filter"?(f(),h(b,{key:4,record:D},null,8,["record"])):k("",!0),T.key==="action"?(f(),h(M,{key:5},{split:l(()=>[t(L,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(A,{onClick:C=>S(!0,D.id)},{default:l(()=>[m("编辑")]),_:2},1032,["onClick"]),t(A,{type:"danger",onClick:C=>R(D.id)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]),_:1},8,["data-api","columns"]),e(_).editVisible?(f(),h(ft,{key:0,"edit-id":e(_).editId,onClose:w[1]||(w[1]=D=>S(!1)),onRefresh:x},null,8,["edit-id"])):k("",!0)],64)}}}),It=Object.freeze(Object.defineProperty({__proto__:null,default:mt},Symbol.toStringTag,{value:"Module"})),ct=E({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(d,{emit:_}){const g=d,x=q(!0),S=q(!1),R=()=>{S.value=!0,Je(g.editId).then(o=>{if(n.value=o,o.validity_type===2?n.value.times=[o.start_time,o.end_time]:n.value.times=void 0,o.img_lang_type===2){let s=JSON.parse(o.lang_img_url);r.langImgs.forEach(b=>{b.img=s[b.lang]}),n.value.lang_img_url_en=s.en}}).finally(()=>S.value=!1)};g.editId&&R();const n=q({id:0,name:void 0,target_url:"",validity_type:1,start_time:void 0,end_time:void 0,img_lang_type:1,uni_img_url:"",lang_img_url:"{}",lang_img_url_en:"",times:void 0,is_filter:0,f_os:"",f_channel:"",f_lang:"",f_s_ids:"",f_lv_ids:""}),r=G({langImgs:[{lang:"en",img:""},{lang:"zh_cn",img:""},{lang:"de",img:""},{lang:"fr",img:""},{lang:"zh_tw",img:""},{lang:"ko",img:""},{lang:"ja",img:""},{lang:"ru",img:""}]}),U=(o,s)=>{console.log("handleChangeImg",o,s),o==="en"&&(n.value.lang_img_url_en=s)},w=q(!1),p=q(),a=()=>{p.value.validate().then(()=>{w.value=!0;const{id:o,...s}=n.value;if(s.validity_type===2&&s.times?(s.start_time=s.times[0],s.end_time=s.times[1]):(s.start_time=K.utc().unix(),s.end_time=0),s.img_lang_type===2){let b={};r.langImgs.forEach(L=>{b[L.lang]=L.img}),s.lang_img_url=JSON.stringify(b)}g.editId?Ge(o,s).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{w.value=!1}):We(s).then(()=>{_("close"),_("refresh")}).catch(()=>{}).finally(()=>{w.value=!1})}).catch(()=>{})};return(o,s)=>{const b=i("a-input"),L=i("a-form-item"),A=i("a-radio-group"),M=i("SelectDateTime"),P=i("ExclamationCircleFilled"),D=i("a-typography-text"),T=i("SelectImg"),j=i("a-col"),C=i("a-row"),$=i("a-radio"),c=i("SelectWithAllComp"),v=i("a-textarea"),u=i("a-button"),y=i("a-form"),W=i("a-spin"),H=i("a-drawer");return f(),h(H,{open:e(x),"onUpdate:open":s[14]||(s[14]=I=>Q(x)?x.value=I:null),title:g.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:s[15]||(s[15]=I=>!I&&_("close"))},{default:l(()=>[t(W,{spinning:e(S)},{default:l(()=>[t(y,{model:e(n),name:"name",ref_key:"formRef",ref:p,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(L,{label:"广告位名称",name:"name",rules:[{required:!0,message:"请输入广告位名称"}]},{default:l(()=>[t(b,{value:e(n).name,"onUpdate:value":s[0]||(s[0]=I=>e(n).name=I),placeholder:"请输入广告位名称"},null,8,["value"])]),_:1}),t(L,{label:"跳转URL",name:"target_url",rules:[{required:!0,message:"请输入跳转URL"}]},{default:l(()=>[t(b,{value:e(n).target_url,"onUpdate:value":s[1]||(s[1]=I=>e(n).target_url=I),placeholder:"请输入跳转URL"},null,8,["value"])]),_:1}),t(L,{label:"上线时间",onChange:s[3]||(s[3]=I=>e(n).times=void 0),rules:[{required:!0,message:"请选择上线时间"}]},{default:l(()=>[t(A,{value:e(n).validity_type,"onUpdate:value":s[2]||(s[2]=I=>e(n).validity_type=I),options:[{label:"永久",value:1},{label:"定时",value:2}]},null,8,["value"])]),_:1}),e(n).validity_type===2?(f(),h(L,{key:0,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:l(()=>[t(M,{value:e(n).times,"onUpdate:value":s[4]||(s[4]=I=>e(n).times=I)},null,8,["value"])]),_:1})):k("",!0),t(L,{label:"图片配置",name:"img_lang_type",rules:[{required:!0,message:"请上传图片配置"}]},{default:l(()=>[t(A,{value:e(n).img_lang_type,"onUpdate:value":s[5]||(s[5]=I=>e(n).img_lang_type=I),options:[{label:"全语种统一",value:1},{label:"区分语种",value:2}]},null,8,["value"]),t(D,{type:"warning",style:{"font-size":"12px",display:"block"}},{default:l(()=>[t(P),m(" "+N("png格式，建议尺寸比例: 488 * 120"))]),_:1})]),_:1}),e(n).img_lang_type===1?(f(),h(L,{key:1,"wrapper-col":{offset:6,span:16},name:"uni_img_url",rules:[{required:!0,message:"请上传图片配置"}]},{default:l(()=>[t(T,{value:e(n).uni_img_url,"onUpdate:value":s[6]||(s[6]=I=>e(n).uni_img_url=I)},null,8,["value"])]),_:1})):(f(),h(L,{key:2,"wrapper-col":{offset:2,span:24}},{default:l(()=>[t(C,{style:{display:"flex",width:"100%"}},{default:l(()=>[(f(!0),Y(V,null,te(e(r).langImgs,I=>(f(),h(j,{span:8,key:I.lang},{default:l(()=>[t(L,{label:I.lang,name:`lang_img_url_${I.lang}`,rules:I.lang==="en"?[{required:!0,message:"请上传图片配置"}]:[]},{default:l(()=>[t(T,{value:I.img,"onUpdate:value":F=>I.img=F,onChange:F=>U(I.lang,F)},null,8,["value","onUpdate:value","onChange"])]),_:2},1032,["label","name","rules"])]),_:2},1024))),128))]),_:1})]),_:1})),t(L,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:l(()=>[t(A,{value:e(n).is_filter,"onUpdate:value":s[7]||(s[7]=I=>e(n).is_filter=I),onChange:s[8]||(s[8]=I=>e(ae)(e(n),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:l(()=>[t($,{value:1},{default:l(()=>[m("开启")]),_:1}),t($,{value:0},{default:l(()=>[m("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(n).is_filter===1?(f(),Y(V,{key:3},[t(L,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:l(()=>[t(c,{value:e(n).f_os,"onUpdate:value":s[9]||(s[9]=I=>e(n).f_os=I),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(L,{label:"语种",name:"f_lang",rules:[{required:!0,message:"请选择语种"}]},{default:l(()=>[t(c,{value:e(n).f_lang,"onUpdate:value":s[10]||(s[10]=I=>e(n).f_lang=I),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(L,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:l(()=>[t(c,{value:e(n).f_channel,"onUpdate:value":s[11]||(s[11]=I=>e(n).f_channel=I),placeholder:"请选择渠道",type:"channels"},null,8,["value"])]),_:1}),t(L,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:l(()=>[t(v,{value:e(n).f_s_ids,"onUpdate:value":s[12]||(s[12]=I=>e(n).f_s_ids=I),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(L,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:l(()=>[t(v,{value:e(n).f_lv_ids,"onUpdate:value":s[13]||(s[13]=I=>e(n).f_lv_ids=I),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1})],64)):k("",!0),t(L,{"wrapper-col":{offset:6,span:16}},{default:l(()=>[t(u,{type:"primary",onClick:a,loading:e(w)},{default:l(()=>[m("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),vt=E({__name:"Index",setup(d){K.extend(z);const _=G({editVisible:!1,editId:0}),g=q(),x=()=>g.value.requestTableData(!0),S=(w,p)=>{_.editVisible=w,_.editId=p||0},R=(w,p)=>B(`确定要${p==="up"?"上移":"下移"}此条数据吗？`,Ze,{id:w,type:p}).then(()=>x()),n=w=>B("删除后游戏内将不展示该项，即刻生效，是否确认删除？",Xe,{id:w}).then(()=>x()),r=q([]),U=(w,p,a)=>{r.value[a]=!0,p.is_online=1-w,X.confirm({title:"提示",content:w===0?"当前入口已上线，请确认后下线？":"开启后游戏内将展示该项，即刻生效，是否确认开启？",okText:"确定",cancelText:"取消",onOk:()=>{r.value[a]=!1,Qe(p.id,w).finally(()=>x())},onCancel:()=>{r.value[a]=!1}})};return(w,p)=>{const a=i("PlusOutlined"),o=i("a-button"),s=i("a-image"),b=i("a-switch"),L=i("FilterCell"),A=i("a-divider"),M=i("ArrowUpOutlined"),P=i("a-typography-link"),D=i("ArrowDownOutlined"),T=i("a-space"),j=i("CustomTable");return f(),Y(V,null,[t(j,{ref_key:"RefCustomTable",ref:g,"data-api":e(ze),params:{},columns:e(tt),pagination:!1},{leftTool:l(()=>[t(o,{type:"primary",onClick:p[0]||(p[0]=C=>S(!0))},{icon:l(()=>[t(a)]),default:l(()=>[m(" 新增 ")]),_:1})]),bodyCell:l(({record:C,column:$,index:c})=>[$.key==="img_lang_type"?(f(),h(s,{key:0,src:C.img_lang_type===1?C.uni_img_url:JSON.parse(C.lang_img_url).en?JSON.parse(C.lang_img_url).en:"",height:60},null,8,["src"])):k("",!0),$.key==="validity_type"?(f(),Y(V,{key:1},[m(N(C.validity_type===2?`${e(K).utc(C.start_time*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${e(K).utc(C.end_time*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):k("",!0),$.key==="is_online"?(f(),h(b,{key:2,checked:C.is_online,"onUpdate:checked":v=>C.is_online=v,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(r)[c],onClick:v=>U(v,C,c)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),$.key==="link_type"?(f(),Y(V,{key:3},[m(N(C.link_type?e(Z)[C.link_type].label:"-"),1)],64)):k("",!0),$.key==="filter"?(f(),h(L,{key:4,record:C},null,8,["record"])):k("",!0),$.key==="action"?(f(),h(T,{key:5},{split:l(()=>[t(A,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(P,{onClick:v=>R(C.id,"up"),disabled:C.first},{default:l(()=>[t(M),m("上移")]),_:2},1032,["onClick","disabled"]),t(P,{onClick:v=>R(C.id,"down"),disabled:C.last},{default:l(()=>[t(D),m("下移")]),_:2},1032,["onClick","disabled"]),t(P,{onClick:v=>S(!0,C.id)},{default:l(()=>[m("编辑")]),_:2},1032,["onClick"]),t(P,{type:"danger",onClick:v=>n(C.id)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]),_:1},8,["data-api","columns"]),e(_).editVisible?(f(),h(ct,{key:0,"edit-id":e(_).editId,onClose:p[1]||(p[1]=C=>S(!1)),onRefresh:x},null,8,["edit-id"])):k("",!0)],64)}}}),xt=Object.freeze(Object.defineProperty({__proto__:null,default:vt},Symbol.toStringTag,{value:"Module"}));export{Ct as D,bt as I,ht as a,wt as b,It as c,xt as d};
