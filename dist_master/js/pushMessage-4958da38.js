import{y as $,d as te,m as X}from"./common-83df99a7.js";import{d as ae,f as C,g as le,r as q,h as se,a as i,o as u,c as k,w as s,b as l,u as e,l as c,k as g,F as D,s as ne,v as x,e as v,i as ce,z as ve,A as fe,_ as oe,t as w}from"./vendor-8e323468.js";import{M as ye,m as ee}from"./antd-bb254174.js";const ge=_=>$.get("/api/system-message",_),ke=_=>$.post("/api/system-message",_),be=(_,Y)=>$.put(`/api/system-message/${_}`,Y),he=_=>$.get(`/api/system-message/${_}`),xe=({petId:_})=>$.delete(`/api/system-message/${_}`),we=_=>$.post("/api/system-message/upload-templates",_),De=_=>$.downfile(`/api/system-message/export-templates?key=${_}`,{key:_}),Ce=_=>$.put(`/api/system-message/publish/${_}`),S=_=>(ve("data-v-35b440a3"),_=_(),fe(),_),Ye={class:"link-tips"},Me=S(()=>x("p",{style:{color:"gray"}},"如需跳转社区文章详情，则参考以下链接配置规则：",-1)),Ie=S(()=>x("p",null,"/communityDetail/?community-app=article/文章id",-1)),$e=S(()=>x("p",null,"例如：",-1)),Le=S(()=>x("p",null,"/communityDetail/articleDetail?community-app=article/1385",-1)),Te={class:"btns-wrap"},Oe={class:"ant-upload-drag-icon"},Pe=S(()=>x("p",{class:"ant-upload-text"},"点击或拖拽文件至此区域即可上传",-1)),Ue={style:{"margin-top":"10px",display:"flex","align-items":"center",gap:"30px"}},j="funplus_zone",He=ae({__name:"Form",props:["editId"],emits:["close","refresh"],setup(_,{emit:Y}){const p=_;C.extend(le);const O=localStorage.getItem("crtGame")||"",P=q(!0),M=q(!1),z=()=>{M.value=!0,he(p.editId).then(r=>{console.log("pushMessage getPropsDetail res",r),a.value=r,a.value.push_time=C.utc(r.push_at*1e3).format("YYYY-MM-DD HH:mm:ss"),a.value.validity_type=r.validity_type||1,a.value.expire_time=r.expire_time||0,a.value.expire_time_str=r.expire_time?C.utc(r.expire_time*1e3).format("YYYY-MM-DD HH:mm:ss"):"",O!==j&&a.value.game_project===j&&ye.confirm({title:"提示",content:"当前空间下不支持修改平台消息",okText:"确定",cancelText:"",cancelButtonProps:{ghost:!0},onOk:()=>{}})}).finally(()=>M.value=!1)};p.editId&&z();const a=q({id:0,category:1,category_sub:1,push_type:2,push_at:0,push_time:"",link_target:1,link_url:"",message_templates:"",is_show_red_dot:1,system_notice_key:"",msg2:"",push_range_type:1,push_user_range:"",validity_type:1,expire_time:0,expire_time_str:"",game_project:""}),d=se({uploadLoading:!1,fileList:[],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[{label:"角色激活提醒",value:1},{label:"每日签到提醒",value:2},{label:"每日补签提醒",value:3},{label:"权益变更提醒",value:4}],optionsType:[{label:"自动推送",value:1},{label:"定时推送",value:2},{label:"系统推送",value:3}],jumpType:[{label:"无跳转",value:1},{label:"跳转",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"每天",value:1},{label:"每周",value:2},{label:"每月",value:3},{label:"每年",value:4}],trigger2:[{label:"发布动态",value:1},{label:"浏览动态",value:2}],trigger3:[{label:"大于",value:1},{label:"等于",value:2},{label:"小于",value:3}]}),L=q(!1),N=q(),b=r=>r&&r<C().startOf("day"),f=r=>{const t=d.domains.indexOf(r);t!==-1&&d.domains.splice(t,1)},A=()=>{d.domains.push({value:"",key:Date.now()})},J=r=>(d.fileList=[r],!1);function Q(r){d.fileList=[r.file],console.log("state.fileList",d.fileList),console.log("state.fileList.length",d.fileList.length),d.fileList.length>0&&U()}function U(){if(d.fileList.length===0)return ee.error("未选择文件！");d.uploadLoading=!0;const r=new FormData;r.append("file",d.fileList[0]),we(r).then(t=>{console.log("pushMessage uploadConfigs res",t),a.value.message_templates=JSON.stringify(t),ee.success("上传成功"),N.value.clearValidate(["message_templates"])}).catch(()=>{d.uploadLoading=!1})}const H=()=>{N.value.validate().then(()=>{L.value=!0;const{id:r,...t}=a.value;t.push_at=C.utc(t.push_time).unix(),t.expire_time=t.expire_time_str?C.utc(t.expire_time_str).unix():0,p.editId?be(r,t).then(()=>{Y("close"),Y("refresh",!0)}).catch(()=>{}).finally(()=>{L.value=!1}):ke(t).then(()=>{Y("close"),Y("refresh")}).catch(()=>{}).finally(()=>{L.value=!1}),setTimeout(()=>{L.value=!1},1e3)}).catch(()=>{})};return(r,t)=>{const T=i("a-select"),y=i("a-form-item"),F=i("a-date-picker"),o=i("a-input-number"),m=i("PlusCircleOutlined"),R=i("MinusCircleOutlined"),V=i("a-space"),G=i("QuestionCircleOutlined"),B=i("a-popover"),h=i("a-input"),ie=i("inbox-outlined"),pe=i("a-upload-dragger"),Z=i("a-button"),K=i("a-radio-group"),ue=i("a-textarea"),W=i("a-radio"),re=i("a-form"),de=i("a-spin"),_e=i("a-drawer");return u(),k(_e,{open:e(P),"onUpdate:open":t[16]||(t[16]=n=>ce(P)?P.value=n:null),title:p.editId?"编辑消息":"新增消息",maskClosable:!1,width:800,onAfterOpenChange:t[17]||(t[17]=n=>!n&&Y("close"))},{default:s(()=>[l(de,{spinning:e(M)},{default:s(()=>[l(re,{model:e(a),name:"basic",ref_key:"formRef",ref:N,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:s(()=>[l(y,{label:"推送类型",name:"push_type",rules:[{required:!0,message:"请选择推送类型"}]},{default:s(()=>[l(T,{disabled:"",value:e(a).push_type,"onUpdate:value":t[0]||(t[0]=n=>e(a).push_type=n),options:e(d).optionsType},null,8,["value","options"])]),_:1}),e(a).push_type===2?(u(),k(y,{key:0,label:"推送时间",name:"push_time",rules:[{required:!0,message:"请选择推送时间"}]},{default:s(()=>[l(F,{style:{width:"100%"},value:e(a).push_time,"onUpdate:value":t[1]||(t[1]=n=>e(a).push_time=n),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","show-now":!1,"show-time":""},null,8,["value"])]),_:1})):c("",!0),e(a).push_type===4?(u(),k(y,{key:1,label:"触发条件"},{default:s(()=>[(u(!0),g(D,null,ne(e(d).domains,(n,me)=>(u(),k(y,{key:n.key,name:["触发条件",me,"value"],rules:{required:!0,message:"触发条件不能为空",trigger:"change"}},{default:s(()=>[l(V,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:s(()=>[l(T,{value:e(a).msg2,"onUpdate:value":t[2]||(t[2]=I=>e(a).msg2=I),options:e(d).trigger1},null,8,["value","options"]),l(T,{value:e(a).msg2,"onUpdate:value":t[3]||(t[3]=I=>e(a).msg2=I),options:e(d).trigger2},null,8,["value","options"]),l(T,{value:e(a).msg2,"onUpdate:value":t[4]||(t[4]=I=>e(a).msg2=I),options:e(d).trigger3},null,8,["value","options"]),l(o,{value:n.value,"onUpdate:value":I=>n.value=I,min:0,max:100,placeholder:"请填写触发条件"},null,8,["value","onUpdate:value"]),l(m,{class:"dynamic-button dynamic-add-button",onClick:A}),e(d).domains.length>1?(u(),k(R,{key:0,class:"dynamic-button dynamic-delete-button",onClick:I=>f(n)},null,8,["onClick"])):c("",!0)]),_:2},1024)]),_:2},1032,["name"]))),128))]),_:1})):c("",!0),l(y,{label:"跳转类型",name:"link_target",rules:[{required:!0,message:"请选择跳转类型"}]},{default:s(()=>[l(T,{value:e(a).link_target,"onUpdate:value":t[5]||(t[5]=n=>e(a).link_target=n),options:e(d).jumpType},null,8,["value","options"])]),_:1}),e(a).link_target===2?(u(),k(y,{key:2,class:"link-target",label:"跳转链接",name:"link_url",rules:[{required:!0,message:"请输入跳转链接"}]},{default:s(()=>[x("div",Ye,[l(B,{title:"Tips"},{content:s(()=>[Me,Ie,$e,Le]),default:s(()=>[l(G)]),_:1})]),l(h,{style:{width:"100%"},value:e(a).link_url,"onUpdate:value":t[6]||(t[6]=n=>e(a).link_url=n),placeholder:"请输入跳转链接"},null,8,["value"])]),_:1})):c("",!0),l(y,{label:"消息文案",name:"message_templates",rules:[{required:!0,message:"请上传消息文案"}]},{default:s(()=>[x("div",Te,[l(pe,{fileList:e(d).fileList,"onUpdate:fileList":t[7]||(t[7]=n=>e(d).fileList=n),name:"file",multiple:!1,action:"/",accept:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel",onChange:Q,onRemove:t[8]||(t[8]=n=>e(d).fileList=[]),"before-upload":J},{default:s(()=>[x("p",Oe,[l(ie)]),Pe]),_:1},8,["fileList"])]),x("div",Ue,[l(Z,{type:"primary",onClick:t[9]||(t[9]=n=>e(te)("message-template"))},{default:s(()=>[v("下载模版")]),_:1}),e(a).message_templates&&e(a).message_templates.length>0?(u(),k(Z,{key:0,type:"primary",onClick:t[10]||(t[10]=n=>e(De)(e(a).system_notice_key))},{default:s(()=>[v("导出消息配置")]),_:1})):c("",!0)])]),_:1}),l(y,{label:"推送用户群",rules:[{required:!0,message:"请选择推送用户群"}]},{default:s(()=>[l(K,{value:e(a).push_range_type,"onUpdate:value":t[11]||(t[11]=n=>e(a).push_range_type=n),options:[{label:"全体",value:1},{label:"指定用户",value:2}]},null,8,["value"])]),_:1}),e(a).push_range_type===2?(u(),k(y,{key:3,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"push_user_range",rules:[{required:!0,message:"请填写funplus_id"}]},{default:s(()=>[l(ue,{width:"100%",value:e(a).push_user_range,"onUpdate:value":t[12]||(t[12]=n=>e(a).push_user_range=n),placeholder:"请输入funplus_id，多个id用英文逗号分隔","allow-clear":""},null,8,["value"])]),_:1})):c("",!0),e(a).push_type===2?(u(),k(y,{key:4,label:"推送有效期",rules:[{required:!0,message:"请选择推送有效期"}]},{default:s(()=>[l(K,{value:e(a).validity_type,"onUpdate:value":t[13]||(t[13]=n=>e(a).validity_type=n),options:[{label:"永久推送",value:1},{label:"定时结束",value:2}]},null,8,["value"])]),_:1})):c("",!0),e(a).validity_type===2?(u(),k(y,{key:5,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"expire_time_str",rules:[{required:!0,message:"请选择结束时间"}]},{default:s(()=>[l(F,{style:{width:"100%"},value:e(a).expire_time_str,"onUpdate:value":t[14]||(t[14]=n=>e(a).expire_time_str=n),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":b,"show-now":!1,"show-time":""},null,8,["value"])]),_:1})):c("",!0),l(y,{label:"是否需要红点",name:"is_show_red_dot"},{default:s(()=>[l(K,{value:e(a).is_show_red_dot,"onUpdate:value":t[15]||(t[15]=n=>e(a).is_show_red_dot=n)},{default:s(()=>[l(W,{value:1},{default:s(()=>[v("需要")]),_:1}),l(W,{value:0},{default:s(()=>[v("不需要")]),_:1})]),_:1},8,["value"])]),_:1}),e(O)===j&&e(a).game_project===j||e(O)!==j&&e(a).game_project!==j?(u(),k(y,{key:6,"wrapper-col":{offset:10,span:12}},{default:s(()=>[l(Z,{type:"primary",onClick:H,loading:e(L)},{default:s(()=>[v("保存")]),_:1},8,["loading"])]),_:1})):c("",!0)]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const je=oe(He,[["__scopeId","data-v-35b440a3"]]),qe={key:0},Ne={class:"preview-header"},Se={class:"preview-title"},Fe={class:"preview-date"},Re=["innerHTML"],Ve={key:0,class:"preview-btn"},E="funplus_zone",Ge=ae({__name:"Index",setup(_){C.extend(le);const Y=[{dataIndex:"id",key:"id",title:"ID",width:"60px",fixed:"left",align:"center"},{dataIndex:"push_type",key:"push_type",title:"推送类型",width:"100px"},{dataIndex:"link_target",key:"link_target",title:"消息类型",width:"100px"},{dataIndex:"preview",key:"preview",title:"内容预览",width:"130px"},{dataIndex:"updated_by",key:"updated_by",title:"更新人",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"game_project",key:"game_project",title:"类型",width:"130px"},{dataIndex:"push_at",key:"push_at",title:"推送时间",width:"200px"},{dataIndex:"is_expire",key:"is_expire",title:"过期状态",width:"130px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],p=se({editVisible:!1,editId:0,searchParams:{push_type:null},previewOpen:!1,previewData:{},optionsGift:[{label:"自动推送",value:1},{label:"定时推送",value:2},{label:"系统推送",value:3}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[{label:"角色激活提醒",value:1},{label:"每日签到提醒",value:2},{label:"每日补签提醒",value:3},{label:"权益变更提醒",value:4}],optionsPushType:[{label:"自动推送",value:1},{label:"定时推送",value:2},{label:"系统推送",value:3}],optionsJumpType:[{label:"无跳转",value:1},{label:"有跳转",value:2}]}),O=localStorage.getItem("crtGame")||"",P=q(),M=b=>P.value.requestTableData(!b),z=b=>X("确定发布并推送当前信息吗？",Ce,b).then(()=>M(!0)),a=(b,f)=>{p.editVisible=b,p.editId=f||0},d=(b,f)=>X(`确定要删除${f?"选中的":"此条"}数据吗？`,xe,{petId:b}).then(()=>M()),L=b=>{p.previewOpen=!0,console.log("record",b),p.previewData=b},N=()=>{te("message-template")};return(b,f)=>{const A=i("a-col"),J=i("a-row"),Q=i("a-select"),U=i("a-button"),H=i("a-space"),r=i("PlusOutlined"),t=i("a-typography-link"),T=i("a-divider"),y=i("CustomTable"),F=i("a-modal");return u(),g(D,null,[l(J,{justify:"space-between"},{default:s(()=>[l(A,{span:12})]),_:1}),l(y,{ref_key:"RefCustomTable",ref:P,"data-api":e(ge),params:e(p).searchParams,columns:Y},{top:s(()=>[l(H,{direction:"vertical"},{default:s(()=>[l(H,{wrap:"",style:{padding:"0 30px",gap:"20px"}},{default:s(()=>[l(Q,{style:{width:"215px"},allowClear:"",value:e(p).searchParams.push_type,"onUpdate:value":f[0]||(f[0]=o=>e(p).searchParams.push_type=o),options:e(p).optionsGift,placeholder:"请选择推送类型"},null,8,["value","options"]),l(U,{type:"primary",onClick:M},{default:s(()=>[v("搜索")]),_:1}),l(U,{onClick:f[1]||(f[1]=()=>{e(p).searchParams.push_type="",M()})},{default:s(()=>[v("重置")]),_:1})]),_:1}),l(H,{wrap:"",style:{padding:"20px 30px",gap:"20px"}},{default:s(()=>[l(U,{type:"primary",onClick:f[2]||(f[2]=o=>N())},{icon:s(()=>[l(r)]),default:s(()=>[v(" 下载模版 ")]),_:1}),l(U,{type:"primary",onClick:f[3]||(f[3]=o=>a(!0))},{icon:s(()=>[l(r)]),default:s(()=>[v(" 新增消息 ")]),_:1})]),_:1})]),_:1})]),bodyCell:s(({column:o,record:m})=>{var R,V,G,B;return[o.key==="push_type"?(u(),g(D,{key:0},[v(w((R=e(p).optionsPushType.find(h=>h.value===m[o.key]))==null?void 0:R.label),1)],64)):c("",!0),o.key==="link_target"?(u(),g(D,{key:1},[v(w((V=e(p).optionsJumpType.find(h=>h.value===m[o.key]))==null?void 0:V.label),1)],64)):c("",!0),o.key==="category"?(u(),g(D,{key:2},[v(w((G=e(p).optionsOne.find(h=>h.value===m[o.key]))==null?void 0:G.label),1)],64)):c("",!0),o.key==="category_sub"?(u(),g(D,{key:3},[v(w((B=e(p).optionsTwo.find(h=>h.value===m[o.key]))==null?void 0:B.label),1)],64)):c("",!0),o.key==="preview"?(u(),k(t,{key:4,onClick:h=>L(m)},{default:s(()=>[v("预览")]),_:2},1032,["onClick"])):c("",!0),o.key==="status"?(u(),g(D,{key:5},[v(w(m[o.key]===1?"已发布":"未发布"),1)],64)):c("",!0),o.key==="game_project"?(u(),g(D,{key:6},[v(w(m[o.key]==="funplus_zone"?"平台":"游戏"),1)],64)):c("",!0),o.key==="push_at"?(u(),g(D,{key:7},[v(w(m.expire_time?`${e(C).utc(m.push_at*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${e(C).utc(m.expire_time*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):c("",!0),o.key==="is_expire"?(u(),g(D,{key:8},[v(w(m.push_type===2?m[o.key]?"已过期":"生效中":"-"),1)],64)):c("",!0),o.key==="action"?(u(),k(H,{key:9},{split:s(()=>[l(T,{type:"vertical",style:{margin:"0"}})]),default:s(()=>[l(t,{disabled:m.status===1||e(O)===E&&m.game_project!==E||e(O)!==E&&m.game_project===E,onClick:h=>z(m.id)},{default:s(()=>[v("发布")]),_:2},1032,["disabled","onClick"]),l(t,{onClick:h=>a(!0,m.id)},{default:s(()=>[v("编辑")]),_:2},1032,["onClick"]),l(t,{disabled:m.push_type===3,type:"danger",danger:"",onClick:h=>d(m.id,!1)},{default:s(()=>[v("删除")]),_:2},1032,["disabled","onClick"])]),_:2},1024)):c("",!0)]}),_:1},8,["data-api","params"]),e(p).editVisible?(u(),k(je,{key:0,"edit-id":e(p).editId,onClose:f[4]||(f[4]=o=>a(!1)),onRefresh:M},null,8,["edit-id"])):c("",!0),l(F,{open:e(p).previewOpen,"onUpdate:open":f[5]||(f[5]=o=>e(p).previewOpen=o),title:"预览",footer:null,class:"preview-modal"},{default:s(()=>[e(p).previewData.templates&&e(p).previewData.templates.length>0?(u(),g("div",qe,[(u(!0),g(D,null,ne(e(p).previewData.templates,(o,m)=>(u(),g("div",{key:m},[x("div",Ne,[x("p",Se,w(o.title),1),x("p",Fe,w(e(p).previewData.created_at?e(C).utc(e(p).previewData.created_at).format("YYYY-MM-DD HH:mm:ss"):e(C).utc(new Date().getTime()).format("YYYY-MM-DD HH:mm:ss")),1)]),x("p",{class:"preview-desc",innerHTML:o.content},null,8,Re),e(p).previewData.link_target==2?(u(),g("div",Ve,w(o.button_text),1)):c("",!0)]))),128))])):c("",!0)]),_:1},8,["open"])],64)}}});const Ae=oe(Ge,[["__scopeId","data-v-8ae65f90"]]);export{Ae as default};
