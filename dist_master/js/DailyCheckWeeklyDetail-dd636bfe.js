import{w as p,P as d,x as c}from"./common-83df99a7.js";import{a as m}from"./shared/DailyCheckWeekly/DailyCheckWeeklyDetail-1f853882.js";import{d as u,m as _,h as f,r as C,a as s,n as D,o as w,c as h,w as T,p as b,b as k,u as a}from"./vendor-8e323468.js";import"./antd-bb254174.js";const x=u({__name:"Detail",setup(v){const o=_(),e=f({id:o.params.id&&Number(o.params.id)||0}),t=C(),n=()=>t.value.requestTableData(!0);return(L,g)=>{const i=s("UploadBtn"),r=s("CustomTable"),l=D("has");return w(),h(r,{ref_key:"RefCustomTable",ref:t,"data-api":a(c),params:{id:a(e).id},columns:a(m),pagination:!1},{rightTool:T(()=>[b(k(i,{ref:"uploadBtn",onUploadSuccess:n,downloadApi:a(p),uploadData:{id:a(e).id},downloadData:{id:a(e).id},fileType:"checkin-weekly",page:a(d).DAILY_CHECK_WEEKLY},null,8,["downloadApi","uploadData","downloadData","page"]),[[l,"Operation"]])]),_:1},8,["data-api","params","columns"])}}});export{x as default};
