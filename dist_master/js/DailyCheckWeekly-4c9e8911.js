import{M as E,D as j}from"./shared/DailyCheckWeekly/DailyCheckWeeklyDetail-1f853882.js";import{n as q,o as A,p as B,d as z,q as H,m as U,r as G,t as J,v as Q}from"./common-83df99a7.js";import{d as S,r as g,a,o as r,c as D,w as e,b as t,u as s,e as d,i as X,f as Y,g as Z,h as ee,j as te,k as O,F as P,l as w,t as N}from"./vendor-8e323468.js";import{M as ae}from"./antd-bb254174.js";const ne=S({__name:"Form",props:["editId"],emits:["close","refresh"],setup(W,{emit:f}){const x=W,b=g(!0),m=g(!1),L=()=>{m.value=!0,q(x.editId).then(l=>{i.value=l}).finally(()=>m.value=!1)};x.editId&&L();const i=g({id:0,month:void 0,bg_img:"",f_s_ids:""}),p=g([]),y=g(!1),$=g(),u=()=>{$.value.validate().then(()=>{y.value=!0;const l=new FormData;l.append("month",`${i.value.month}`),l.append("bg_img",i.value.bg_img),l.append("f_s_ids",`${i.value.f_s_ids}`),p.value.length&&l.append("file",p.value[0]),i.value.id&&l.append("id",`${i.value.id}`),(i.value.id?A:B)(l).then(()=>{f("close"),f("refresh")}).catch(()=>{}).finally(()=>{y.value=!1})}).catch(()=>{})},o=l=>(p.value=[...p.value||[],l],!1),C=async l=>p.value.length||i.value.id||i.value.file?Promise.resolve():Promise.reject("请上传奖品配置文件");return(l,c)=>{const T=a("upload-outlined"),I=a("a-button"),R=a("a-upload"),V=a("ExclamationCircleFilled"),F=a("a-typography-text"),k=a("a-form-item"),M=a("a-textarea"),n=a("a-form"),h=a("a-spin"),K=a("a-drawer");return r(),D(K,{open:s(b),"onUpdate:open":c[2]||(c[2]=v=>X(b)?b.value=v:null),title:x.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:c[3]||(c[3]=v=>!v&&f("close"))},{default:e(()=>[t(h,{spinning:s(m)},{default:e(()=>[t(n,{model:s(i),name:"basic",ref_key:"formRef",ref:$,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:e(()=>[t(k,{label:"奖品配置",rules:[{validator:C}]},{default:e(()=>[t(R,{"file-list":s(p),"before-upload":o,onRemove:c[0]||(c[0]=()=>p.value=[]),"max-count":1,accept:"'.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'"},{default:e(()=>[t(I,null,{default:e(()=>[t(T),d(" 点击上传 ")]),_:1})]),_:1},8,["file-list"]),t(F,{type:"warning",style:{"font-size":"12px"}},{default:e(()=>[t(V),d(" 新增时必须上传奖励配置 ")]),_:1})]),_:1},8,["rules"]),t(k,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:e(()=>[t(M,{value:s(i).f_s_ids,"onUpdate:value":c[1]||(c[1]=v=>s(i).f_s_ids=v),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(k,{"wrapper-col":{offset:6,span:16}},{default:e(()=>[t(I,{type:"primary",onClick:u,loading:s(y)},{default:e(()=>[d("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),de=S({__name:"Index",setup(W){Y.extend(Z);const f=ee({editVisible:!1,editId:0}),x=te(),b=g(),m=()=>b.value.requestTableData(!0),L=(u,o)=>{f.editVisible=u,f.editId=o||0},i=(u,o)=>U(`确定要删除${o?"选中的":"此条"}数据吗？`,J,{id:u}).then(()=>m()),p=u=>U("确定要复制此条数据并生成一份新数据吗？",Q,{id:u}).then(()=>m()),y=g([]),$=(u,o,C)=>{y.value[C]=!0,o.status=1-u,ae.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{y.value[C]=!1,G({id:o.id,status:u}).finally(()=>{m()})},onCancel:()=>{y.value[C]=!1}})};return(u,o)=>{const C=a("PlusOutlined"),l=a("a-button"),c=a("CloudDownloadOutlined"),T=a("a-space"),I=a("a-image"),R=a("a-typography-text"),V=a("a-switch"),F=a("a-divider"),k=a("a-typography-link"),M=a("CustomTable");return r(),O(P,null,[t(M,{ref_key:"RefCustomTable",ref:b,"data-api":s(H),params:{},columns:s(j)},{leftTool:e(()=>[t(T,null,{default:e(()=>[t(l,{type:"primary",onClick:o[0]||(o[0]=n=>L(!0))},{icon:e(()=>[t(C)]),default:e(()=>[d(" 新增 ")]),_:1}),t(l,{type:"primary",onClick:o[1]||(o[1]=n=>s(z)("checkin-weekly"))},{icon:e(()=>[t(c)]),default:e(()=>[d(" 下载奖品模板 ")]),_:1})]),_:1})]),bodyCell:e(({record:n,column:h,index:K})=>{var v;return[h.key==="bg_img"?(r(),O(P,{key:0},[n.bg_img?(r(),D(I,{key:0,src:n.bg_img,height:60},null,8,["src"])):(r(),O(P,{key:1},[d("-")],64))],64)):w("",!0),h.key==="filter"?(r(),D(R,{key:1,code:!0},{default:e(()=>[d(N(n.filter.f_s_ids),1)]),_:2},1024)):w("",!0),h.key==="month"?(r(),O(P,{key:2},[d(N((v=s(E).filter(_=>n.month===_.value)[0])==null?void 0:v.label),1)],64)):w("",!0),h.key==="status"?(r(),D(V,{key:3,checked:n.status,"onUpdate:checked":_=>n.status=_,checkedValue:10,unCheckedValue:1,"checked-children":"开启","un-checked-children":"关闭",loading:s(y)[K],onClick:_=>$(_,n,K)},null,8,["checked","onUpdate:checked","loading","onClick"])):w("",!0),h.key==="action"?(r(),D(T,{key:4},{split:e(()=>[t(F,{type:"vertical",style:{margin:"0"}})]),default:e(()=>[t(k,{type:"success",onClick:_=>p(n.id)},{default:e(()=>[d("复制")]),_:2},1032,["onClick"]),t(k,{onClick:_=>L(!0,n.id)},{default:e(()=>[d("编辑")]),_:2},1032,["onClick"]),t(k,{onClick:_=>s(x).push(`/dailyCheck/weekly/detail/${n.id}`)},{default:e(()=>[d("详情")]),_:2},1032,["onClick"]),t(k,{type:"danger",danger:"",onClick:_=>i(n.id,!1)},{default:e(()=>[d("删除")]),_:2},1032,["onClick"])]),_:2},1024)):w("",!0)]}),_:1},8,["data-api","columns"]),s(f).editVisible?(r(),D(ne,{key:0,"edit-id":s(f).editId,onClose:o[2]||(o[2]=n=>L(!1)),onRefresh:m},null,8,["edit-id"])):w("",!0)],64)}}});export{de as default};
