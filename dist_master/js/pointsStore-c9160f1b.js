import{y as O,z as de,A as te,L as ae,B as _e,P as re,m as me}from"./common-83df99a7.js";import{d as le,f as T,g as ne,r as G,h as W,x as ie,y as ve,a as m,o as c,c as Y,w as o,b as l,u as t,e as v,l as k,i as ce,z as se,A as oe,v as j,_ as ue,n as fe,k as I,p as ge,F as C,t as L}from"./vendor-8e323468.js";import{M as P}from"./antd-bb254174.js";const ye=_=>O.get("/api/points-mall-product",_),ke=_=>O.post("/api/points-mall-product",_),ee=(_,b)=>O.put(`/api/points-mall-product/${_}`,b),be=_=>O.get(`/api/points-mall-product/${_}`),we=({petId:_})=>O.delete(`/api/points-mall-product/${_}`),he=(_,b)=>O.put(`/api/points-mall-product/set-status/${_}`,{status:b}),xe=()=>O.downfile("/api/points-mall-product/export"),Ie=_=>O.put(`/api/points-mall-product/adjust-stock/${_.id}`,_),Ce=_=>(se("data-v-5cc6996b"),_=_(),oe(),_),Le=Ce(()=>j("span",{style:{margin:"0 10px"}},"-",-1)),De=le({__name:"Form",props:["editId"],emits:["close","refresh"],setup(_,{emit:b}){const d=_;T.extend(ne);const{configState:$}=de(),M=G(!0),D=G(!1),A=()=>{D.value=!0,be(d.editId).then(i=>{console.log("store getPropsDetail res",i),a.value=i,a.value.gift_id=Number(i.gift_id),a.value.item_limit_type=i.item_limit_type||1,a.value.stocks_num=i.stocks_num||1,a.value.online_timing_str=i.online_timing?T.utc(i.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss"):"",i.is_time_limit===1&&i.start_time&&i.end_time?a.value.times=[i.start_time,i.end_time]:a.value.times=void 0}).finally(()=>D.value=!1)};d.editId&&A();const a=G({id:0,cycle_type:1,cycle_shape:1,cycle_times:1,cost_coin:0,vip:1,vip_max:1,game_growth:0,role_growth:0,gift_id:null,status:0,is_time_limit:0,times:void 0,use_stocks:0,item_limit_type:1,stocks_num:1,use_desc_key:null,online_type:1,online_timing:0,online_timing_str:"",product_type:1}),f=W({uploadLoading:!1,optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"R1",value:1},{label:"R2",value:2},{label:"R3",value:3},{label:"R4",value:4},{label:"R5",value:5},{label:"R6",value:6},{label:"R7",value:7}],jumpType:[{label:"跳转到特定页面",value:0},{label:"打开游戏",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:1},{label:"按角色",value:2}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5,disabled:!0},{label:"终身领取",value:6}],progressList:[{label:"每次需完成1个行为",value:1},{label:"每次需完成2个行为",value:2},{label:"每次需完成3个行为",value:3},{label:"每次需完成4个行为",value:4},{label:"每次需完成5个行为",value:5},{label:"每次需完成6个行为",value:6},{label:"每次需完成7个行为",value:7},{label:"每次需完成8个行为",value:8},{label:"每次需完成9个行为",value:9},{label:"每次需完成10个行为",value:10}],rewardType:[{label:"平台道具奖励",value:0},{label:"游戏道具奖励",value:1},{label:"积分",value:2}],optionsGifts:[],optionsLang:[],optionsLangKey:[],optionsOriginTaskEvent:[],optionsTaskEvent:[],stocksTypeList:[{label:"每日兑换上限",value:1},{label:"每周兑换上限",value:2},{label:"每月兑换上限",value:3},{label:"每年兑换上限",value:4},{label:"总兑换上限",value:5}],productTypeList:[{label:"游戏",value:1},{label:"平台",value:2}],giftsList:[]}),N=W({filterData:{task_where:[{required:!0,message:"请选择任务条件",trigger:"change"}]}}),w=G(!1),V=G(),B=i=>i&&i<T().startOf("day");ie(()=>{r(),u({page:1,page_size:1e3})}),ve(()=>$,i=>{i&&i.task_events&&i.task_events.length>0&&(f.optionsTaskEvent=i.task_events.map(e=>({originLabel:e.label,label:e.label,value:e.value})))},{immediate:!0});const E=i=>{a.value.gift_id=null,f.optionsGifts=f.giftsList.filter(e=>i===1?e.game_project!=="funplus_zone":e.game_project==="funplus_zone")},r=()=>{te().then(i=>{i&&i.length>0&&(f.giftsList=i,f.optionsGifts=i.filter(e=>a.value.product_type===1?e.game_project!=="funplus_zone":e.game_project==="funplus_zone"))})},u=i=>{ae(i).then(e=>{e.data&&e.data.length>0&&(f.optionsLang=e.data.map(g=>({label:g.zh_cn,value:g.key})),f.optionsLangKey=[...f.optionsLang])})},h=i=>{const e=f.optionsTwo.find(g=>g.value===i);console.log("handleGiftPackage item",e)},S=(i,e)=>e.label.toLowerCase().indexOf(i.toLowerCase())>=0,R=()=>{V.value.validate().then(()=>{w.value=!0;const{id:i,...e}=a.value;if(e.game_growth=e.game_growth||0,e.vip_max&&e.vip_max<e.vip)return w.value=!1,P.error({title:"提示",content:"等级上限不能小于等级下限"});const g=f.optionsGifts.find(y=>y.id===e.gift_id);if(g&&g.item_list_length>1)return w.value=!1,P.error({title:"提示",content:"所选礼包内包含多种道具/积分，请重新选择"});if(e.cost_coin===0)return w.value=!1,P.error({title:"提示",content:"兑换所需积分不能为0"});if(e.is_time_limit===1&&e.times?(e.start_time=e.times[0],e.end_time=e.times[1]):(e.start_time=0,e.end_time=0),e.online_type===2?e.online_timing=e.online_timing_str?T.utc(e.online_timing_str).unix():0:e.online_timing=0,e.product_type===2&&!e.use_desc_key)return w.value=!1,P.error({title:"提示",content:"请选择使用说明"});d.editId?a.value.status?P.confirm({title:"提示",content:"当前商品已上线，保存后立即生效，请确认后操作",okText:"确定",cancelText:"取消",onOk:()=>{ee(i,e).then(()=>{b("close"),b("refresh",!0)}).catch(()=>{}).finally(()=>{w.value=!1})},onCancel:()=>{w.value=!1}}):ee(i,e).then(()=>{b("close"),b("refresh",!0)}).catch(()=>{}).finally(()=>{w.value=!1}):ke(e).then(()=>{b("close"),b("refresh")}).catch(()=>{}).finally(()=>{w.value=!1}),setTimeout(()=>{w.value=!1},1e3)}).catch(()=>{})};return(i,e)=>{const g=m("a-select"),y=m("a-form-item"),U=m("a-input-number"),H=m("a-space"),q=m("a-radio-group"),K=m("a-date-picker"),J=m("SelectDateTime"),Q=m("SelectLang"),s=m("a-button"),p=m("a-form"),z=m("a-spin"),F=m("a-drawer");return c(),Y(F,{open:t(M),"onUpdate:open":e[19]||(e[19]=n=>ce(M)?M.value=n:null),title:d.editId?"编辑商品":"新增商品",maskClosable:!1,width:800,onAfterOpenChange:e[20]||(e[20]=n=>!n&&b("close"))},{default:o(()=>[l(z,{spinning:t(D)},{default:o(()=>[l(p,{model:t(a),rules:t(N),name:"basic",ref_key:"formRef",ref:V,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:o(()=>[l(y,{label:"商品类型",name:"product_type",rules:[{required:!0,message:"请选择商品类型"}]},{default:o(()=>[l(g,{style:{width:"100%"},value:t(a).product_type,"onUpdate:value":e[0]||(e[0]=n=>t(a).product_type=n),onChange:E,options:t(f).productTypeList},null,8,["value","options"])]),_:1}),l(y,{label:"商品名称",name:"gift_id",rules:[{required:!0,message:"请选择商品名称"}]},{default:o(()=>[l(g,{style:{width:"100%"},allowClear:"","show-search":"","filter-option":S,value:t(a).gift_id,"onUpdate:value":e[1]||(e[1]=n=>t(a).gift_id=n),options:t(f).optionsGifts,placeholder:"请选择商品名称",onChange:h},null,8,["value","options"])]),_:1}),l(y,{label:"兑换所需积分",name:"cost_coin",rules:[{required:!0,message:"请填写兑换所需积分"}]},{default:o(()=>[l(U,{style:{width:"100%"},value:t(a).cost_coin,"onUpdate:value":e[2]||(e[2]=n=>t(a).cost_coin=n),min:1,max:15e5,placeholder:"请填写兑换所需积分"},null,8,["value"])]),_:1}),l(y,{label:"等级限制",name:"vip",rules:[{required:!0,message:"请选择等级限制"}]},{default:o(()=>[l(H,{style:{width:"100%"}},{default:o(()=>[l(g,{style:{width:"100px"},value:t(a).vip,"onUpdate:value":e[3]||(e[3]=n=>t(a).vip=n),options:t(f).optionsVip},null,8,["value","options"]),Le,l(g,{style:{width:"100px"},value:t(a).vip_max,"onUpdate:value":e[4]||(e[4]=n=>t(a).vip_max=n),options:t(f).optionsVip},null,8,["value","options"])]),_:1})]),_:1}),l(y,{label:"游戏成长值",name:"game_growth"},{default:o(()=>[l(U,{style:{width:"100%"},value:t(a).game_growth,"onUpdate:value":e[5]||(e[5]=n=>t(a).game_growth=n),placeholder:"请输入游戏成长值"},null,8,["value"])]),_:1}),l(y,{label:"领取维度及周期",name:"cycle_times",rules:[{required:!0,message:"请填写领取维度及周期"}]},{default:o(()=>[l(H,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:o(()=>[l(g,{style:{width:"100%"},value:t(a).cycle_shape,"onUpdate:value":e[6]||(e[6]=n=>t(a).cycle_shape=n),options:t(f).trigger1},null,8,["value","options"]),l(g,{style:{width:"100%"},value:t(a).cycle_type,"onUpdate:value":e[7]||(e[7]=n=>t(a).cycle_type=n),options:t(f).trigger2},null,8,["value","options"]),l(U,{style:{width:"100%"},value:t(a).cycle_times,"onUpdate:value":e[8]||(e[8]=n=>t(a).cycle_times=n),min:1,max:9999,placeholder:"请填写次数"},null,8,["value"]),v("次 ")]),_:1})]),_:1}),l(y,{label:"商品上线时间",onChange:e[10]||(e[10]=n=>t(a).times=void 0),rules:[{required:!0,message:"请选择起止时间"}]},{default:o(()=>[l(q,{value:t(a).online_type,"onUpdate:value":e[9]||(e[9]=n=>t(a).online_type=n),options:[{label:"立即上线",value:1},{label:"定时上线",value:2}]},null,8,["value"])]),_:1}),t(a).online_type===2?(c(),Y(y,{key:0,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"online_timing_str",rules:[{required:!0,message:"请选择结束时间"}]},{default:o(()=>[l(K,{style:{width:"100%"},value:t(a).online_timing_str,"onUpdate:value":e[11]||(e[11]=n=>t(a).online_timing_str=n),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":B,"show-now":!1,"show-time":""},null,8,["value"])]),_:1})):k("",!0),l(y,{label:"商品有效期",onChange:e[13]||(e[13]=n=>t(a).times=void 0),rules:[{required:!0,message:"请选择起止时间"}]},{default:o(()=>[l(q,{value:t(a).is_time_limit,"onUpdate:value":e[12]||(e[12]=n=>t(a).is_time_limit=n),options:[{label:"永久",value:0},{label:"定时",value:1}]},null,8,["value"])]),_:1}),t(a).is_time_limit===1?(c(),Y(y,{key:1,class:"time-limit-item","wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:o(()=>[l(J,{value:t(a).times,"onUpdate:value":e[14]||(e[14]=n=>t(a).times=n)},null,8,["value"])]),_:1})):k("",!0),l(y,{label:"是否限量兑换",rules:[{required:!0,message:"请选择是否限量兑换"}]},{default:o(()=>[l(q,{value:t(a).use_stocks,"onUpdate:value":e[15]||(e[15]=n=>t(a).use_stocks=n),options:[{label:"否",value:0},{label:"是",value:1}]},null,8,["value"])]),_:1}),t(a).use_stocks===1?(c(),Y(y,{key:2,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"item_limit_type",rules:[{required:!0,message:"请选择兑换上限类型"}]},{default:o(()=>[l(g,{style:{width:"50%"},value:t(a).item_limit_type,"onUpdate:value":e[16]||(e[16]=n=>t(a).item_limit_type=n),options:t(f).stocksTypeList},null,8,["value","options"]),l(U,{value:t(a).stocks_num,"onUpdate:value":e[17]||(e[17]=n=>t(a).stocks_num=n),min:1,max:9999,placeholder:"请填写兑换上限"},null,8,["value"])]),_:1})):k("",!0),l(y,{label:"使用说明",name:"use_desc_key"},{default:o(()=>[l(Q,{value:t(a).use_desc_key,"onUpdate:value":e[18]||(e[18]=n=>t(a).use_desc_key=n)},null,8,["value"])]),_:1}),l(y,{"wrapper-col":{offset:10,span:12}},{default:o(()=>[l(s,{type:"primary",onClick:R,loading:t(w)},{default:o(()=>[v("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const Te=ue(De,[["__scopeId","data-v-5cc6996b"]]),pe=_=>(se("data-v-5ce9a7a3"),_=_(),oe(),_),Ye=pe(()=>j("div",{class:"preview-desc"},"库存调整后立即生效，请确认修改后保存",-1)),Ue=pe(()=>j("strong",null,"→",-1)),Oe=le({__name:"Index",setup(_){T.extend(ne);const b=[{dataIndex:"id",key:"id",title:"商品ID",width:"100px"},{dataIndex:"item_list",key:"item_list",title:"道具详情",width:"130px"},{dataIndex:"cost_coin",key:"cost_coin",title:"兑换积分",width:"130px"},{dataIndex:"product_type",key:"product_type",title:"商品类型",width:"130px"},{dataIndex:"vip",key:"vip",title:"等级限制",width:"130px"},{dataIndex:"game_growth",key:"game_growth",title:"游戏成长值",width:"130px"},{dataIndex:"cycle_shape",key:"cycle_shape",title:"领取维度",width:"130px"},{dataIndex:"cycle_type",key:"cycle_type",title:"周期",width:"130px"},{dataIndex:"cycle_times",key:"cycle_times",title:"周期内次数",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"online_type",key:"online_type",title:"上线时间",width:"130px"},{dataIndex:"is_time_limit",key:"is_time_limit",title:"有效期",width:"130px"},{dataIndex:"stocks_num",key:"stocks_num",title:"兑换上限",width:"100px"},{dataIndex:"now_stocks",key:"now_stocks",title:"实时库存",width:"100px"},{dataIndex:"action",key:"action",title:"操作",width:"200px",fixed:"right",align:"center"}],d=W({editVisible:!1,editId:0,searchParams:{task_category:null,vip_restriction:null},previewOpen:!1,previewData:{},optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4}],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],trigger1:[{label:"账号",value:1},{label:"角色",value:2}],optionsGifts:[],optionsLang:[]}),$=G([]),M=G(),D=r=>M.value.requestTableData(!r);ie(()=>{A(),a({page:1,page_size:1e3}),f("task_desc")});const A=()=>{te().then(r=>{r&&r.length>0&&(d.optionsGifts=r)})},a=r=>{ae(r).then(u=>{console.log("LangGetData res",u),u.data&&u.data.length>0&&(d.optionsLang=u.data.map(h=>({label:h.zh_cn,value:h.key})))})},f=async r=>{const u=await _e({key:r});console.log("res",u);const h=(u==null?void 0:u.zh_cn)||"-";return console.log("item",h),h},N=r=>{d.previewData=r,d.previewOpen=!0},w=()=>{const{id:r,change_stocks:u}=d.previewData;if(u<0)return P.error({title:"提示",content:"所选礼包内包含多种道具/积分，请重新选择"});Ie({id:r,now_stocks:u}).then(()=>{d.previewOpen=!1,D(!0)})},V=(r,u)=>{d.editVisible=r,d.editId=u||0},B=r=>me("删除后，用户将看不到该商品，请确认后删除",we,{petId:r}).then(()=>D()),E=(r,u,h)=>{$.value[h]=!0,u.status=1-r;const S=r===1?"当前商品未上线，是否确认现在上线":"当前商品已上线，是否确认现在下线？";P.confirm({title:"提示",content:S,okText:"确定",cancelText:"取消",onOk:()=>{$.value[h]=!1,he(u.id,r).finally(()=>D(!0))},onCancel:()=>{$.value[h]=!1}})};return(r,u)=>{const h=m("UploadBtn"),S=m("PlusOutlined"),R=m("a-button"),i=m("a-space"),e=m("a-switch"),g=m("a-tooltip"),y=m("a-divider"),U=m("a-typography-link"),H=m("CustomTable"),q=m("a-input-number"),K=m("a-form-item"),J=m("a-modal"),Q=fe("has");return c(),I(C,null,[l(H,{ref_key:"RefCustomTable",ref:M,"data-api":t(ye),params:t(d).searchParams,columns:b},{top:o(()=>[l(i,{direction:"vertical"},{default:o(()=>[l(i,{wrap:"",style:{padding:"20px 0",gap:"20px"}},{default:o(()=>[ge(l(h,{ref:"uploadBtn",onUploadSuccess:D,downloadApi:t(xe),fileType:"points-products",page:t(re).PRODUCTCONFIG},null,8,["downloadApi","page"]),[[Q,"Operation"]]),l(R,{type:"primary",onClick:u[0]||(u[0]=s=>V(!0))},{icon:o(()=>[l(S)]),default:o(()=>[v(" 新增商品 ")]),_:1})]),_:1})]),_:1})]),bodyCell:o(({column:s,record:p,index:z})=>{var F,n,X,Z;return[s.key==="package"?(c(),I(C,{key:0},[v(L((F=p[s.key])==null?void 0:F.name),1)],64)):k("",!0),s.key==="item_list"?(c(),I(C,{key:1},[v(L(p[s.key]&&p[s.key].length>0?p[s.key][0].name+"*"+p[s.key][0].num:""),1)],64)):k("",!0),s.key==="vip"?(c(),I(C,{key:2},[v(" R"+L(p.vip)+" - R"+L(p.vip_max),1)],64)):k("",!0),s.dataIndex==="product_type"?(c(),I(C,{key:3},[v(L(p[s.key]!==1?"平台":"游戏"),1)],64)):k("",!0),s.dataIndex==="cycle_shape"?(c(),I(C,{key:4},[v(L((n=t(d).trigger1.find(x=>x.value===p[s.key]))==null?void 0:n.label),1)],64)):k("",!0),s.dataIndex==="cycle_type"?(c(),I(C,{key:5},[v(L((X=t(d).optionsGift1.find(x=>x.value===p[s.key]))==null?void 0:X.label),1)],64)):k("",!0),s.dataIndex==="progress"?(c(),I(C,{key:6},[v(L(p[s.key]?p[s.key]:"无限制"),1)],64)):k("",!0),s.dataIndex==="pkg_id"?(c(),I(C,{key:7},[v(L((Z=t(d).optionsGifts.find(x=>x.value==p[s.key]))==null?void 0:Z.label),1)],64)):k("",!0),s.key==="status"?(c(),Y(e,{key:8,checked:p.status,"onUpdate:checked":x=>p.status=x,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:t($)[z],onClick:x=>E(x,p,z)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),s.dataIndex==="online_type"&&p[s.key]===2?(c(),Y(g,{key:9,title:`${t(T).utc(p.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss")}`,placement:"topLeft"},{default:o(()=>[v(L(t(T).utc(p.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss")),1)]),_:2},1032,["title"])):k("",!0),s.dataIndex==="online_type"&&p[s.key]===1?(c(),I(C,{key:10},[v("立即上线")],64)):k("",!0),s.dataIndex==="is_time_limit"&&p[s.key]===1?(c(),Y(g,{key:11,title:`${t(T).utc(p.start_time*1e3).format("YYYY-MM-DD HH:mm:ss")} -
        ${t(T).utc(p.end_time*1e3).format("YYYY-MM-DD HH:mm:ss")}`,placement:"topLeft"},{default:o(()=>[v(" 定时 ")]),_:2},1032,["title"])):k("",!0),s.dataIndex==="is_time_limit"&&p[s.key]===0?(c(),I(C,{key:12},[v(" 永久 ")],64)):k("",!0),s.key==="action"?(c(),Y(i,{key:13},{split:o(()=>[l(y,{type:"vertical",style:{margin:"0"}})]),default:o(()=>[l(U,{disabled:!p.stocks_num,onClick:x=>N(p)},{default:o(()=>[v("库存调整")]),_:2},1032,["disabled","onClick"]),l(U,{onClick:x=>V(!0,p.id)},{default:o(()=>[v("编辑")]),_:2},1032,["onClick"]),l(U,{type:"danger",danger:"",onClick:x=>B(p.id)},{default:o(()=>[v("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]}),_:1},8,["data-api","params"]),t(d).editVisible?(c(),Y(Te,{key:0,"edit-id":t(d).editId,onClose:u[1]||(u[1]=s=>V(!1)),onRefresh:D},null,8,["edit-id"])):k("",!0),l(J,{class:"preview-modal",visible:t(d).previewOpen,"onUpdate:visible":u[4]||(u[4]=s=>t(d).previewOpen=s),title:"库存调整",onCancel:u[5]||(u[5]=s=>t(d).previewOpen=!1),onOk:u[6]||(u[6]=s=>t(d).previewOpen=!1)},{footer:o(()=>[l(R,{key:"back",onClick:u[3]||(u[3]=s=>t(d).previewOpen=!1)},{default:o(()=>[v(" 取消 ")]),_:1}),l(R,{key:"submit",type:"primary",onClick:w},{default:o(()=>[v(" 确认 ")]),_:1})]),default:o(()=>[Ye,l(K,{label:"实时库存",class:"preview-item"},{default:o(()=>[j("strong",null,L(t(d).previewData.now_stocks),1),Ue,l(q,{value:t(d).previewData.change_stocks,"onUpdate:value":u[2]||(u[2]=s=>t(d).previewData.change_stocks=s),min:0,max:t(d).previewData.stocks_num},null,8,["value","max"])]),_:1})]),_:1},8,["visible"])],64)}}});const Me=ue(Oe,[["__scopeId","data-v-5ce9a7a3"]]);export{Me as default};
