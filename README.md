# 私域管理平台

## 技术架构

### 技术栈

- 前端框架：Vue 3 + TypeScript
- 构建工具：Vite 4.x
- 状态管理：Pinia (Vuex 替代方案)
- UI 框架：Ant Design Vue (3.x 版本)
  - 组件库：@ant-design/icons-vue、ant-design-vue
  - 样式方案：Sass/SCSS 预处理（项目自定义样式）+ Less（Ant Design 基础样式）
- 路由管理：Vue Router 4.x
- 代码规范：ESLint + Prettier
- 单元测试：Vitest (待接入)
- 包管理：Yarn

### 项目结构

```text
src/
├─ api/               # API接口管理
│   ├─ resource.ts    # 资源管理接口
│   └─ points.ts      # 积分系统接口
├─ assets/
│   ├─ images/        # 图片资源
│   └─ styles/
│       ├─ mixins/    # Sass混入
│       ├─ antd.less  # Ant Design样式覆盖
│       └─ main.scss  # 全局样式入口
├─ components/
│   ├─ common/        # 通用业务组件
│   │   ├─ FPTable/   # 定制表格组件
│   │   └─ FPSearch/  # 搜索组件
│   └─ layout/        # 布局组件
├─ enum/              # 类型定义
│   ├─ system.ts      # 系统枚举
│   └─ business.ts    # 业务枚举
├─ router/
│   ├─ index.ts       # 路由配置入口
│   └─ guard/         # 路由守卫
├─ store/             # Pinia全局状态
│   ├─ user.ts        # 用户信息
│   └─ system.ts      # 系统配置
├─ utils/             # 工具函数
│   ├─ auth.ts        # 权限管理
│   └─ request.ts     # 请求封装
├─ views/
│   ├─ resourceManage/
│   │   ├─ components/ # 模块组件
│   │   ├─ props/      # Props类型定义
│   │   └─ store/      # 模块状态管理
│   ├─ pointsSystem/
│   │   ├─ Form.vue    # 表单组件
│   │   └─ store/      # 积分模块状态
├─ App.vue            # 根组件
└─ main.ts            # 应用入口
```

## 开发构建发布

### 开发

代码仓库： https://gitlab-ee.funplus.io/priv-platform/priv-admin-web.git
node 版本: 18.16.0
分支: dev

```bash
npm install

# ENV - release | master
npm run build:{ENV}

# 更新yapi版本
npm install yapi-download@latest --registry=http://nexus.kingsgroupgames.com/repository/npm-group/ --legacy-peer-deps

# 更新yapi数据
npx yapi-download --pid 480 --token 2fad3c084268e490f56849b66c1df7ee20a7f30ba7ec34384940b56a11121332 --output ./api.json

```

### 测试

服务器地址: release: 47.100.233.173, master: 35.166.237.230
分支: dev

```bash
ssh root@47.100.233.173
cd /data/programs/priv/priv-admin-web
git pull
```

### 发布

分支: master

```bash
git push origin master
创建tag: master_global_v0.0.1
更新发布任务: kingsgroup.global_ali.prod.priv-admin-web
```

## 开发指南

## 前端开发规范

### 页面开发流程

1. **文件位置**

   - Views 层：`/views/${模块}/功能名称/Index.vue`
   - API 层：`/api/${模块}.ts`
   - 路由配置：`/router/permissionModules/${模块}.ts`

2. **组件要求**

   - 列表页必须使用`<CustomTable>`组件
   - 搜索区域用`<a-card>`包裹，置于表格上方
   - 参考模板：`/views/rightAndInterests/exclusiveGift/Index.vue`
   - 参考模板：`/views/rightAndInterests/exclusiveGift/Form.vue`

3. **路由规范**

   ```ts
   // 示例：资源管理模块路由
   {
     path: '/resource/items',
     component: () => import('@/views/resource/items/Index.vue'),
     meta: { title: '资源项管理' }
   }
   ```

4. **API 接口**
   - 接口路径遵循`/api/${模块}-${资源}`格式
   - 使用统一的`Request`模块发起请求
   - 接口实现参考`/api/resource.ts`

### 组件库使用

- 表格组件：`<custom-table>`（文档见`src/components/CustomTable/README.md`）
- 表单组件：`<custom-form>`（文档见`src/components/CustomForm/README.md`）
