import{y as R,L as Y,d as ae,A as Z,P as le,m as W}from"./common-b8130258.js";import{d as K,r as V,h as H,x as J,a as r,o as v,c as G,w as l,b as t,u as e,v as E,e as f,l as w,i as ee,z as ie,A as oe,_ as Q,k as O,F as S,t as N,n as ne,p as se}from"./vendor-cc06403f.js";import{m as X,M as te}from"./antd-914dcf46.js";const pe=d=>R.get("/api/vip-right-config",d),ue=d=>R.post("/api/vip-right-config",d),de=(d,y)=>R.put(`/api/vip-right-config/${d}`,y),re=d=>R.get(`/api/vip-right-config/${d}`),_e=(d,y)=>R.put(`/api/vip-right-config/set-status/${d}`,{status:y}),ce=d=>R.post("/api/vip-right-config/upload-ladder-configs",d),fe=({id:d})=>R.downfile(`/api/vip-right-config/export-ladders/${d}`),ge=d=>(ie("data-v-2583038c"),d=d(),oe(),d),ve={class:"btns-wrap"},ye={class:"ant-upload-drag-icon"},me=ge(()=>E("p",{class:"ant-upload-text"},"点击或拖拽文件至此区域即可上传",-1)),he={style:{"margin-top":"10px",display:"flex","align-items":"center",gap:"30px"}},be=K({__name:"Form",props:["editId"],emits:["close","refresh"],setup(d,{emit:y}){const p=d,C=V(!0),$=V(!1),I=()=>{$.value=!0,re(p.editId).then(s=>{i.value=s}).finally(()=>$.value=!1)};p.editId&&I();const i=V({id:0,rights_key:"",rights_type:1,rights_name:"",rights_img:"",rights_desc:"",dimension:"",ladder_configs:"{}",collection_method:"",button:"",collection_url:"",status:1,name_key:"en",quantity:0,is_filter:!1}),n=H({uploadLoading:!1,fileList:[],optionsRights:[{label:"平台特权",value:1},{label:"游戏特权",value:2}],optionsDimension:[{label:"账户",value:0},{label:"角色",value:1}],optionsCollection:[{label:"当前页面领取",value:1},{label:"跳转页面",value:2},{label:"无引导",value:3}],optionsKeys:[{label:"rights_gift_day",value:"rights_gift_day"},{label:"rights_gift_week",value:"rights_gift_week"},{label:"rights_gift_month",value:"rights_gift_month"},{label:"rights_gift_level",value:"rights_gift_level"},{label:"rights_mall",value:"rights_mall"},{label:"rights_signin",value:"rights_signin"},{label:"rights_activity",value:"rights_activity"}],optionsBtnText:[{label:"立即领取",value:1},{label:"立即前往",value:2},{label:"联系客服",value:3},{label:"我知道了",value:4},{label:"提交工单",value:5}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],jumpType:[{label:"无跳转",value:1},{label:"跳转",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:1},{label:"按角色",value:2}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5},{label:"终身领取",value:6}],optionsRightsName:[],optionsRightsDesc:[]}),T=V(!1),L=V();J(()=>{m({page:1,page_size:1e3})});const m=s=>{Y(s).then(a=>{a.data&&a.data.length>0&&(n.optionsTwo=a.data.map(h=>({label:h.zh_cn,value:h.key})),n.optionsRightsName=[...n.optionsTwo],n.optionsRightsDesc=[...n.optionsTwo])})},U=s=>{const a=n.optionsTwo.filter(h=>{var b;return((b=h.label)==null?void 0:b.indexOf(s))>-1});n.optionsRightsName=a},c=s=>{s===void 0&&(n.optionsRightsName=n.optionsTwo)},_=s=>{const a=n.optionsTwo.filter(h=>{var b;return((b=h.label)==null?void 0:b.indexOf(s))>-1});n.optionsRightsDesc=a},u=s=>{s===void 0&&(n.optionsRightsDesc=n.optionsTwo)},P=s=>(n.fileList=[s],!1);function x(s){n.fileList=[s.file],n.fileList,n.fileList.length,n.fileList.length>0&&F()}function F(){if(n.fileList.length===0)return X.error("未选择文件！");n.uploadLoading=!0;const s=new FormData;s.append("file",n.fileList[0]),ce(s).then(a=>{X.success("上传成功"),i.value.ladder_configs=JSON.stringify(a)}).catch(()=>{n.uploadLoading=!1})}const A=()=>{L.value.validate().then(()=>{T.value=!0;const{id:s,...a}=i.value;p.editId?de(s,a).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}):ue(a).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}),setTimeout(()=>{T.value=!1},1e3)}).catch(()=>{})};return(s,a)=>{const h=r("a-select"),b=r("a-form-item"),q=r("inbox-outlined"),k=r("a-upload-dragger"),o=r("a-button"),M=r("a-input"),B=r("a-space"),z=r("a-form"),j=r("a-spin"),D=r("a-drawer");return v(),G(D,{open:e(C),"onUpdate:open":a[12]||(a[12]=g=>ee(C)?C.value=g:null),title:p.editId?"编辑权益":"新增权益",maskClosable:!1,width:800,onAfterOpenChange:a[13]||(a[13]=g=>!g&&y("close"))},{default:l(()=>[t(j,{spinning:e($)},{default:l(()=>[t(z,{model:e(i),name:"basic",ref_key:"formRef",ref:L,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(b,{label:"权益key",name:"rights_key",rules:[{required:!0,message:"请选择权益key"}]},{default:l(()=>[t(h,{disabled:"",style:{width:"100%"},value:e(i).rights_key,"onUpdate:value":a[0]||(a[0]=g=>e(i).rights_key=g),options:e(n).optionsKeys},null,8,["value","options"])]),_:1}),t(b,{label:"权益类型",name:"rights_type",rules:[{required:!0,message:"请选择权益类型"}]},{default:l(()=>[t(h,{style:{width:"100%"},value:e(i).rights_type,"onUpdate:value":a[1]||(a[1]=g=>e(i).rights_type=g),options:e(n).optionsRights},null,8,["value","options"])]),_:1}),t(b,{label:"权益名称",name:"rights_name",rules:[{required:!0,message:"请选择权益名称"}]},{default:l(()=>[t(h,{style:{width:"100%"},value:e(i).rights_name,"onUpdate:value":a[2]||(a[2]=g=>e(i).rights_name=g),options:e(n).optionsRightsName,allowClear:"","filter-option":!1,showSearch:"",onSearch:U,onChange:c},null,8,["value","options"])]),_:1}),t(b,{label:"权益简介",name:"rights_desc",rules:[{required:!0,message:"请选择权益简介"}]},{default:l(()=>[t(h,{style:{width:"100%"},value:e(i).rights_desc,"onUpdate:value":a[3]||(a[3]=g=>e(i).rights_desc=g),options:e(n).optionsRightsDesc,allowClear:"","filter-option":!1,showSearch:"",onSearch:_,onChange:u},null,8,["value","options"])]),_:1}),t(b,{label:"梯度配置",name:"gift_value"},{default:l(()=>[E("div",ve,[t(k,{fileList:e(n).fileList,"onUpdate:fileList":a[4]||(a[4]=g=>e(n).fileList=g),name:"file",multiple:!1,action:"/",accept:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel",onChange:x,onRemove:a[5]||(a[5]=g=>e(n).fileList=[]),"before-upload":P},{default:l(()=>[E("p",ye,[t(q)]),me]),_:1},8,["fileList"])]),E("div",he,[t(o,{type:"primary",onClick:a[6]||(a[6]=g=>e(ae)("vip-right-ladder-configs"))},{default:l(()=>[f("下载模版")]),_:1}),e(i).ladder_configs&&e(i).ladder_configs.length>0?(v(),G(o,{key:0,type:"primary",onClick:a[7]||(a[7]=g=>e(fe)({id:p.editId}))},{default:l(()=>[f("导出梯度配置")]),_:1})):w("",!0)])]),_:1}),t(b,{label:"领取维度",name:"dimension",rules:[{required:!0,message:"请选择权益维度"}]},{default:l(()=>[t(h,{style:{width:"100%"},value:e(i).dimension,"onUpdate:value":a[8]||(a[8]=g=>e(i).dimension=g),options:e(n).optionsDimension},null,8,["value","options"])]),_:1}),t(b,{label:"领取文案及跳转"},{default:l(()=>[t(B,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:l(()=>[t(h,{style:{width:"100%"},value:e(i).collection_method,"onUpdate:value":a[9]||(a[9]=g=>e(i).collection_method=g),options:e(n).optionsCollection},null,8,["value","options"]),t(h,{style:{width:"100%"},disbaled:"",value:e(i).button,"onUpdate:value":a[10]||(a[10]=g=>e(i).button=g),options:e(n).optionsBtnText},null,8,["value","options"]),e(i).collection_method===2?(v(),G(M,{key:0,style:{width:"100%"},value:e(i).collection_url,"onUpdate:value":a[11]||(a[11]=g=>e(i).collection_url=g),placeholder:"请输入页面url"},null,8,["value"])):w("",!0)]),_:1})]),_:1}),t(b,{"wrapper-col":{offset:10,span:12}},{default:l(()=>[t(o,{type:"primary",onClick:A,loading:e(T)},{default:l(()=>[f("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const ke=Q(be,[["__scopeId","data-v-2583038c"]]),we=K({__name:"Index",setup(d){const y=[{dataIndex:"id",key:"id",title:"ID",width:"100px"},{dataIndex:"rights_key",key:"rights_key",title:"权益key",width:"150px"},{dataIndex:"rights_type",key:"rights_type",title:"权益分类",width:"100px"},{dataIndex:"rights_name",key:"rights_name",title:"权益名称",width:"130px"},{dataIndex:"rights_desc",key:"rights_desc",title:"简介",width:"130px"},{dataIndex:"dimension",key:"dimension",title:"统计维度",width:"130px"},{dataIndex:"ladder_configs",key:"ladder_configs",title:"梯度配置",width:"130px"},{dataIndex:"collection_method",key:"collection_method",title:"领取方式",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],p=H({editVisible:!1,editId:0,searchParams:{rights_type:null},previewOpen:!1,previewData:{},optionsRights:[{label:"平台特权",value:1},{label:"游戏特权",value:2}],optionsDimension:[{label:"账户",value:0},{label:"角色",value:1}],optionsCollection:[{label:"当前页面领取",value:1},{label:"跳转页面",value:2},{label:"无引导",value:3}],optionsKeys:[{label:"日礼包",value:"rights_gift_day"},{label:"周礼包",value:"rights_gift_week"},{label:"月礼包",value:"rights_gift_month"},{label:"等级礼包",value:"rights_gift_level"},{label:"特惠商城",value:"rights_mall"},{label:"签到福利",value:"rights_signin"},{label:"特惠活动",value:"rights_activity"}],optionsBtnText:[{label:"立即领取",value:1},{label:"立即前往",value:2},{label:"联系客服",value:3},{label:"我知道了",value:4},{label:"提交工单",value:5}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],optionsTwo:[]}),C=V([]),$=V(),I=()=>$.value.requestTableData(!0);J(()=>{i({page:1,page_size:1e3})});const i=L=>{Y(L).then(m=>{m.data&&m.data.length>0&&(p.optionsTwo=m.data.map(U=>({label:U.zh_cn,value:U.key})))})},n=(L,m)=>{p.editVisible=L,p.editId=m||0},T=(L,m,U)=>{C.value[U]=!0,m.status=1-L;const c=L!==1?"关闭后前端将不展示该权益，即刻生效，请谨慎操作，是否确认关闭":"关闭后前端将展示该权益，即刻生效，是否确认开启";te.confirm({title:"提示",content:c,okText:"确定",cancelText:"取消",onOk:()=>{C.value[U]=!1,_e(m.id,L).finally(()=>I())},onCancel:()=>{C.value[U]=!1}})};return(L,m)=>{const U=r("a-select"),c=r("a-button"),_=r("a-space"),u=r("a-image"),P=r("a-switch"),x=r("a-divider"),F=r("a-typography-link"),A=r("CustomTable");return v(),O(S,null,[t(A,{ref_key:"RefCustomTable",ref:$,"data-api":e(pe),params:e(p).searchParams,columns:y},{top:l(()=>[t(_,{direction:"vertical"},{default:l(()=>[t(_,{wrap:"",style:{gap:"20px"}},{default:l(()=>[t(U,{style:{width:"215px"},allowClear:"",value:e(p).searchParams.rights_type,"onUpdate:value":m[0]||(m[0]=s=>e(p).searchParams.rights_type=s),options:e(p).optionsRights,placeholder:"请选择权益类型"},null,8,["value","options"]),t(c,{type:"primary",onClick:I},{default:l(()=>[f("搜索")]),_:1}),t(c,{onClick:m[1]||(m[1]=()=>{e(p).searchParams.rights_type="",I()})},{default:l(()=>[f("重置")]),_:1})]),_:1}),t(_,{wrap:"",style:{padding:"20px 0",gap:"20px"}})]),_:1})]),bodyCell:l(({column:s,record:a,index:h})=>{var b,q,k;return[s.key==="rights_type"?(v(),O(S,{key:0},[f(N((b=e(p).optionsRights.find(o=>o.value===a[s.key]))==null?void 0:b.label),1)],64)):w("",!0),s.key==="rights_name"?(v(),O(S,{key:1},[f(N((q=e(p).optionsTwo.find(o=>o.value===a[s.key]))==null?void 0:q.label),1)],64)):w("",!0),s.key==="rights_img"?(v(),G(u,{key:2,src:a.rights_img,height:60},null,8,["src"])):w("",!0),s.key==="rights_desc"?(v(),O(S,{key:3},[f(N((k=e(p).optionsTwo.find(o=>o.value===a[s.key]))==null?void 0:k.label),1)],64)):w("",!0),s.key==="dimension"?(v(),O(S,{key:4},[f(N(a[s.key]===0?"账户":"角色"),1)],64)):w("",!0),s.key==="ladder_configs"?(v(),O(S,{key:5},[f(N(a[s.key]&&a[s.key].length>0?"已上传":"未上传"),1)],64)):w("",!0),s.key==="collection_method"?(v(),O(S,{key:6},[f(N(a[s.key]===1?"当前页面领取":a[s.key]===2?"跳转页面":"无引导"),1)],64)):w("",!0),s.key==="status"?(v(),G(P,{key:7,checked:a.status,"onUpdate:checked":o=>a.status=o,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:e(C)[h],onClick:o=>T(o,a,h)},null,8,["checked","onUpdate:checked","loading","onClick"])):w("",!0),s.key==="action"?(v(),G(_,{key:8},{split:l(()=>[t(x,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(F,{onClick:o=>n(!0,a.id)},{default:l(()=>[f("编辑")]),_:2},1032,["onClick"])]),_:2},1024)):w("",!0)]}),_:1},8,["data-api","params"]),e(p).editVisible?(v(),G(ke,{key:0,"edit-id":e(p).editId,onClose:m[2]||(m[2]=s=>n(!1)),onRefresh:I},null,8,["edit-id"])):w("",!0)],64)}}});const xe=Q(we,[["__scopeId","data-v-17225d75"]]),qe=Object.freeze(Object.defineProperty({__proto__:null,default:xe},Symbol.toStringTag,{value:"Module"})),Ce=d=>R.get("/api/member-gift",d),Ie=d=>R.post("/api/member-gift",d),Le=(d,y)=>R.put(`/api/member-gift/${d}`,y),Te=d=>R.get(`/api/member-gift/${d}`),Pe=({petId:d})=>R.delete(`/api/member-gift/${d}`),De=(d,y)=>R.put(`/api/member-gift/set-status/${d}`,{status:y}),Re=({id:d})=>R.put(`/api/member-gift/copy/${d}`),Ue=d=>R.downfile("/api/member-gift/export",d),Ve=K({__name:"Form",props:["editId"],emits:["close","refresh"],setup(d,{emit:y}){const p=d,C=V(!0),$=V(!1),I=()=>{$.value=!0,Te(p.editId).then(_=>{i.value=_}).finally(()=>$.value=!1)};p.editId&&I();const i=V({id:0,gift_packages_id:null,vip:1,game_growth:0,role_growth:0,cycle_shape:1,cycle_type:1,cycle_times:0,gift_value:0,f_s_ids:"",name_key:"en",quantity:0,is_filter:!1}),n=H({uploadLoading:!1,optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],jumpType:[{label:"无跳转",value:1},{label:"跳转",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:1},{label:"按角色",value:2}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5},{label:"终身领取",value:6}]}),T=V(!1),L=V();J(()=>{m()});const m=()=>{Z().then(_=>{_&&_.length>0&&(n.optionsTwo=_)})},U=_=>{const u=n.optionsTwo.find(P=>P.value===_);u&&(i.value.gift_value=u.gift_value)},c=()=>{L.value.validate().then(()=>{T.value=!0;const{id:_,...u}=i.value;p.editId?Le(_,u).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}):Ie(u).then(()=>{y("close"),y("refresh")}).catch(()=>{}).finally(()=>{T.value=!1}),setTimeout(()=>{T.value=!1},1e3)}).catch(()=>{})};return(_,u)=>{const P=r("a-select"),x=r("a-form-item"),F=r("a-input-number"),A=r("a-space"),s=r("a-input"),a=r("a-textarea"),h=r("a-button"),b=r("a-form"),q=r("a-spin"),k=r("a-drawer");return v(),G(k,{open:e(C),"onUpdate:open":u[10]||(u[10]=o=>ee(C)?C.value=o:null),title:p.editId?"编辑礼包":"新增礼包",maskClosable:!1,width:800,onAfterOpenChange:u[11]||(u[11]=o=>!o&&y("close"))},{default:l(()=>[t(q,{spinning:e($)},{default:l(()=>[t(b,{model:e(i),name:"basic",ref_key:"formRef",ref:L,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:l(()=>[t(x,{label:"礼包类型",name:"cycle_type",rules:[{required:!0,message:"请选择礼包类型"}]},{default:l(()=>[t(P,{style:{width:"100%"},value:e(i).cycle_type,"onUpdate:value":u[0]||(u[0]=o=>e(i).cycle_type=o),options:e(n).optionsGift},null,8,["value","options"])]),_:1}),t(x,{label:"礼包名称（后台）",name:"gift_packages_id",rules:[{required:!0,message:"请选择礼包名称"}]},{default:l(()=>[t(P,{style:{width:"100%"},value:e(i).gift_packages_id,"onUpdate:value":u[1]||(u[1]=o=>e(i).gift_packages_id=o),options:e(n).optionsTwo,onChange:U},null,8,["value","options"])]),_:1}),t(x,{label:"VIP等级",name:"vip",rules:[{required:!0,message:"请选择VIP等级"}]},{default:l(()=>[t(P,{style:{width:"100%"},value:e(i).vip,"onUpdate:value":u[2]||(u[2]=o=>e(i).vip=o),options:e(n).optionsVip},null,8,["value","options"])]),_:1}),t(x,{label:"游戏成长值",name:"game_growth"},{default:l(()=>[t(F,{style:{width:"100%"},value:e(i).game_growth,"onUpdate:value":u[3]||(u[3]=o=>e(i).game_growth=o),min:0,max:15e5,placeholder:"请输入游戏成长值"},null,8,["value"])]),_:1}),t(x,{label:"角色成长值",name:"role_growth"},{default:l(()=>[t(F,{style:{width:"100%"},value:e(i).role_growth,"onUpdate:value":u[4]||(u[4]=o=>e(i).role_growth=o),min:0,max:15e5,placeholder:"请输入角色成长值"},null,8,["value"])]),_:1}),t(x,{label:"领取维度及周期",name:"cycle_times",rules:[{required:!0,message:"请填写领取维度及周期"}]},{default:l(()=>[t(A,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:l(()=>[t(P,{style:{width:"100%"},value:e(i).cycle_shape,"onUpdate:value":u[5]||(u[5]=o=>e(i).cycle_shape=o),options:e(n).trigger1},null,8,["value","options"]),t(P,{style:{width:"100%"},disabled:"",value:e(i).cycle_type,"onUpdate:value":u[6]||(u[6]=o=>e(i).cycle_type=o),options:e(n).trigger2},null,8,["value","options"]),t(F,{style:{width:"100%"},value:e(i).cycle_times,"onUpdate:value":u[7]||(u[7]=o=>e(i).cycle_times=o),min:0,max:1e5,placeholder:"请填写次数"},null,8,["value"]),f("次 ")]),_:1})]),_:1}),t(x,{label:"礼包价值",name:"gift_value"},{default:l(()=>[t(s,{style:{width:"100px","margin-right":"10px"},readonly:"",value:e(i).gift_value,"onUpdate:value":u[8]||(u[8]=o=>e(i).gift_value=o)},null,8,["value"]),f("积分（发生退款时扣除对应积分） ")]),_:1}),t(x,{label:"服务器",name:"f_s_ids"},{default:l(()=>[t(a,{value:e(i).f_s_ids,"onUpdate:value":u[9]||(u[9]=o=>e(i).f_s_ids=o),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(x,{"wrapper-col":{offset:10,span:12}},{default:l(()=>[t(h,{type:"primary",onClick:c,loading:e(T)},{default:l(()=>[f("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const $e=Q(Ve,[["__scopeId","data-v-435269d3"]]),Oe=K({__name:"Index",setup(d){const y=[{dataIndex:"id",key:"id",title:"ID",width:"100px"},{dataIndex:"cycle_type",key:"cycle_type",title:"礼包类型",width:"100px"},{dataIndex:"gift_packages_id",key:"gift_packages_id",title:"礼包名称（后台）",width:"130px"},{dataIndex:"vip",key:"vip",title:"VIP等级",width:"80px"},{dataIndex:"game_growth",key:"game_growth",title:"游戏成长值",width:"130px"},{dataIndex:"role_growth",key:"role_growth",title:"角色成长值",width:"130px"},{dataIndex:"gift_value",key:"gift_value",title:"礼包积分价值",width:"130px"},{dataIndex:"cycle_shape",key:"cycle_shape",title:"领取维度",width:"130px"},{dataIndex:"cycle_type",key:"cycle_type_1",title:"周期",width:"130px"},{dataIndex:"cycle_times",key:"cycle_times",title:"周期内次数",width:"130px"},{dataIndex:"f_s_ids",key:"f_s_ids",title:"服务器ID",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],p=H({editVisible:!1,editId:0,searchParams:{cycle_type:null},previewOpen:!1,previewData:{},optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],optionsTwo:[]}),C=V([]),$=V(),I=c=>$.value.requestTableData(!c);J(()=>{i()});const i=()=>{Z().then(c=>{c&&c.length>0&&(p.optionsTwo=c)})},n=(c,_)=>{p.editVisible=c,p.editId=_||0},T=c=>W("确定要复制此条数据并生成一份新数据吗？",Re,{id:c}).then(()=>I(!0)),L=(c,_)=>W(`确定要删除${_?"选中的":"此条"}数据吗？`,Pe,{petId:c}).then(()=>I()),m=c=>{p.previewOpen=!0,p.previewData=c},U=(c,_,u)=>{C.value[u]=!0,_.status=1-c,te.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{C.value[u]=!1,De(_.id,c).finally(()=>I(!0))},onCancel:()=>{C.value[u]=!1}})};return(c,_)=>{const u=r("a-select"),P=r("a-button"),x=r("a-space"),F=r("UploadBtn"),A=r("PlusOutlined"),s=r("a-switch"),a=r("a-typography-link"),h=r("a-divider"),b=r("CustomTable"),q=ne("has");return v(),O(S,null,[t(b,{ref_key:"RefCustomTable",ref:$,"data-api":e(Ce),params:e(p).searchParams,columns:y},{top:l(()=>[t(x,{direction:"vertical"},{default:l(()=>[t(x,{wrap:"",style:{gap:"20px"}},{default:l(()=>[t(u,{style:{width:"215px"},allowClear:"",value:e(p).searchParams.cycle_type,"onUpdate:value":_[0]||(_[0]=k=>e(p).searchParams.cycle_type=k),options:e(p).optionsGift,placeholder:"请选择礼包类型"},null,8,["value","options"]),t(P,{type:"primary",onClick:I},{default:l(()=>[f("搜索")]),_:1}),t(P,{onClick:_[1]||(_[1]=()=>{e(p).searchParams.cycle_type="",I()})},{default:l(()=>[f("重置")]),_:1})]),_:1}),t(x,{wrap:"",style:{padding:"20px 0",gap:"20px"}},{default:l(()=>[se(t(F,{ref:"uploadBtn",onUploadSuccess:I,downloadApi:e(Ue),fileType:"member-gifts",page:e(le).RIGHTGIFT},null,8,["downloadApi","page"]),[[q,"Operation"]]),t(P,{type:"primary",onClick:_[2]||(_[2]=k=>n(!0))},{icon:l(()=>[t(A)]),default:l(()=>[f(" 新增礼包 ")]),_:1})]),_:1})]),_:1})]),bodyCell:l(({column:k,record:o,index:M})=>{var B,z,j;return[k.key==="gift_packages_id"?(v(),O(S,{key:0},[f(N((B=e(p).optionsTwo.find(D=>D.value===o[k.key]))==null?void 0:B.label),1)],64)):w("",!0),k.key==="cycle_type"?(v(),O(S,{key:1},[f(N((z=e(p).optionsGift.find(D=>D.value===o[k.key]))==null?void 0:z.label),1)],64)):w("",!0),k.key==="cycle_type_1"?(v(),O(S,{key:2},[f(N((j=e(p).optionsGift1.find(D=>D.value===o.cycle_type))==null?void 0:j.label),1)],64)):w("",!0),k.key==="cycle_shape"?(v(),O(S,{key:3},[f(N(o[k.key]===1?"账户":"角色"),1)],64)):w("",!0),k.key==="status"?(v(),G(s,{key:4,checked:o.status,"onUpdate:checked":D=>o.status=D,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:e(C)[M],onClick:D=>U(D,o,M)},null,8,["checked","onUpdate:checked","loading","onClick"])):w("",!0),k.key==="preview"?(v(),G(a,{key:5,onClick:D=>m(o)},{default:l(()=>[f("预览")]),_:2},1032,["onClick"])):w("",!0),k.key==="action"?(v(),G(x,{key:6},{split:l(()=>[t(h,{type:"vertical",style:{margin:"0"}})]),default:l(()=>[t(a,{type:"success",onClick:D=>T(o.id)},{default:l(()=>[f("复制")]),_:2},1032,["onClick"]),t(a,{onClick:D=>n(!0,o.id)},{default:l(()=>[f("编辑")]),_:2},1032,["onClick"]),t(a,{type:"danger",danger:"",onClick:D=>L(o.id,!1)},{default:l(()=>[f("删除")]),_:2},1032,["onClick"])]),_:2},1024)):w("",!0)]}),_:1},8,["data-api","params"]),e(p).editVisible?(v(),G($e,{key:0,"edit-id":e(p).editId,onClose:_[3]||(_[3]=k=>n(!1)),onRefresh:I},null,8,["edit-id"])):w("",!0)],64)}}});const Se=Q(Oe,[["__scopeId","data-v-843a2840"]]),Ae=Object.freeze(Object.defineProperty({__proto__:null,default:Se},Symbol.toStringTag,{value:"Module"}));export{qe as I,Ae as a};
