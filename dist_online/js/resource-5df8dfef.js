import{u as V,a as Z,P as q,L as Q,b as Y,c as ee,e as te,f as ae,h as ne,i as oe,m as E,j as le,k as ie,n as se,o as pe,p as de,q as re,t as _e}from"./common-9638d99e.js";import{d as M,a as N,r as L,bz as z,H as ue,b as t,c as H,e as u,l as T,h as e,g as a,i as J,u as n,f as k,n as h,k as x,t as G,F as O,j as D,q as ce,s as me,_ as j,w as fe,a9 as ye,p as W,m as A,v as ge,x as ve,bD as ke}from"./vendor-6ece677a.js";import{F as he,m as K}from"./antd-79d05377.js";const be=[{dataIndex:"key",key:"key",title:"多语言key",width:"200px",fixed:"left"},{dataIndex:"category",key:"category",title:"模块",width:"120px"}],Ie=[{dataIndex:"id",key:"id",title:"id",width:"60px"},{dataIndex:"gift_id",key:"gift_id",title:"gift_id",width:"140px"},{dataIndex:"name",key:"name",title:"礼包名称(后台)",width:"130px"},{dataIndex:"desc_key",key:"desc_key",title:"礼包名称(前台)",width:"130px"},{dataIndex:"icon_url",key:"icon_url",title:"icon",width:"70px"},{dataIndex:"item_list",key:"item_list",title:"道具详情",width:"230px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],xe=[{dataIndex:"item_id",key:"item_id",title:"道具",ellipsis:!0},{dataIndex:"num",key:"num",title:"数量",width:"80px"},{dataIndex:"action",key:"action",title:"操作",width:"80px",align:"center"}],we=[{dataIndex:"id",key:"id",title:"道具ID",width:"60px",fixed:"left",align:"center"},{dataIndex:"internal_id",key:"internal_id",title:"internal_id",width:"100px"},{dataIndex:"item_id",key:"item_id",title:"item_id",width:"230px"},{dataIndex:"image",key:"image",title:"道具icon",width:"80px"},{dataIndex:"name",key:"name",title:"中文名",width:"130px"}],Ce=[{dataIndex:"id",key:"id",title:"ID",width:"60px",fixed:"left",align:"center"},{dataIndex:"type",key:"type",title:"道具类型",width:"100px"},{dataIndex:"item_id",key:"item_id",title:"三方券id",width:"230px"},{dataIndex:"name",key:"name",title:"道具名称",width:"130px"},{dataIndex:"image",key:"image",title:"道具icon",width:"80px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],Pe=M({__name:"Index",setup(p){const o=N({searchParams:{key:""}}),y=Q,s=L(),{configState:i}=z(V()),g=ue(()=>{const r=[...i.value.langs.map(c=>({ellipsis:!0,dataIndex:c.value,title:`${c.label}(${c.value})`,key:c.value,type:"lang",width:"200px"})).sort((c,b)=>c.dataIndex<b.dataIndex?1:-1)];return[...be,...r]}),d=()=>s.value.requestTableData(!0);return(r,c)=>{const b=t("a-input-search"),f=t("a-col"),l=t("UploadBtn"),_=t("a-row"),v=t("a-card"),w=t("PropIcon"),C=t("a-tooltip"),I=t("CustomTable"),P=H("has");return u(),T(O,null,[e(v,{class:"filter",border:!1},{default:a(()=>[e(_,{justify:"space-between"},{default:a(()=>[e(f,{span:12},{default:a(()=>[e(b,{value:o.searchParams.key,"onUpdate:value":c[0]||(c[0]=m=>o.searchParams.key=m),placeholder:"请输入搜索内容",style:{width:"300px"},"enter-button":"查询",allowClear:"",onSearch:d},null,8,["value"])]),_:1}),J(e(l,{ref:"uploadBtn",onUploadSuccess:d,downloadApi:n(Z),fileType:"lang",page:n(q).MULTILINGUAL},null,8,["downloadApi","page"]),[[P,"Operation"]])]),_:1})]),_:1}),e(I,{ref_key:"RefCustomTable",ref:s,"data-api":n(y),params:o.searchParams,columns:n(g)},{bodyCell:a(({column:m,record:$})=>[m.key==="image"?(u(),k(w,{key:0,"prop-detail":$,style:{width:"50px",height:"50px"}},null,8,["prop-detail"])):h("",!0),m.type==="lang"?(u(),k(C,{key:1,title:$[m.dataIndex],placement:"topLeft"},{default:a(()=>[x(G($[m.dataIndex]),1)]),_:2},1032,["title"])):h("",!0)]),_:1},8,["data-api","params","columns"])],64)}}}),Qe=Object.freeze(Object.defineProperty({__proto__:null,default:Pe},Symbol.toStringTag,{value:"Module"})),X=p=>(ce("data-v-b51eee60"),p=p(),me(),p),Te={style:{"flex-shrink":"0"}},Se={key:1,xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 83 83",fill:"none",style:{"flex-shrink":"0"}},Le=X(()=>D("circle",{cx:"41.5",cy:"41.5",r:"36.5",fill:"#F1B545",stroke:"#F5F070",strokeWidth:"10"},null,-1)),Ue=X(()=>D("path",{d:"M56.5652 53.8262L62.0435 29.1741L49.7174 37.3915L41.5 23.6958L33.2826 37.3915L20.9565 29.1741L27.8044 53.8262H56.5652Z",fill:"#F7F473"},null,-1)),Oe=[Le,Ue],$e={style:{margin:"0 5px",overflow:"hidden"}},Fe={class:"t-name"},Me={class:"t-name"},De={key:0,class:"num",style:{"flex-shrink":"0"}},Re=M({__name:"Item",props:{item:{type:Object,default:()=>({})},showNum:{type:Boolean,default:!0}},setup(p){const o=p;return(y,s)=>{const i=t("a-image"),g=t("a-flex");return u(),T("div",null,[e(g,{align:"center"},{default:a(()=>[D("div",Te,[o.item.gift_type===1||o.item.gift_type===2?(u(),k(i,{key:0,src:o.item.image,width:"50px",preview:!1},null,8,["src"])):h("",!0),o.item.gift_type===0?(u(),T("svg",Se,Oe)):h("",!0)]),D("div",$e,[D("div",Fe,G(o.item.name),1),D("div",Me,G(o.item.item_id),1)]),o.showNum?(u(),T("div",De,"X"+G(o.item.num),1)):h("",!0)]),_:1})])}}});const B=j(Re,[["__scopeId","data-v-b51eee60"]]),Ge=M({name:"FormPropItem",components:{Item:B},props:{value:{type:Array,default:()=>[]}},setup(p,{emit:o}){const y=he.useInjectFormItemContext(),s=f=>{o("update:value",f),y.onFieldChange()},i=N({type:void 0,itemKey:void 0,loading:!1,crtItem:{},options:[]}),{configState:g}=z(V());fe(()=>p.value,f=>{f&&f.length===0&&y.onFieldChange()},{deep:!0});const d=f=>{f&&(i.loading=!0,Y({search_key:f,type:i.type}).then(l=>{i.options=l.map(_=>({..._,value:_.item_id,label:_.name})),i.options}).finally(()=>{i.loading=!1}))},r=(f,l)=>{i.crtItem=l},c=f=>{i.options=[],i.itemKey=void 0,i.crtItem={}},b=()=>{if(i.type===0){if(p.value.find(f=>f.item_id==="coin"))return K.info("已添加此道具，直接修改数量");s([...p.value,{num:1,game_project:"funplus_zone",item_id:"coin",internal_id:"coin",name:"积分",gift_type:0}])}if(i.type===1||i.type===2){if(!i.crtItem.item_id)return;if(p.value.find(f=>f.item_id===i.crtItem.item_id))return K.info("已添加此道具，直接修改数量");s([...p.value,{...i.crtItem,num:1,gift_type:i.type}])}i.crtItem,i.crtItem={},i.itemKey=void 0};return{configState:g,...ye(i),remoteMethod:d,select:r,change:c,addItem:b,GIFT_ITEM_COLUMNS_MAP:xe}}});const Ne={style:{color:"#999"}};function Ae(p,o,y,s,i,g){const d=t("a-select"),r=t("a-input-group"),c=t("PlusOutlined"),b=t("a-button"),f=t("a-flex"),l=t("Item"),_=t("a-input-number"),v=t("a-typography-link"),w=t("a-table");return u(),T(O,null,[e(f,null,{default:a(()=>[e(r,{compact:""},{default:a(()=>[e(d,{value:p.type,"onUpdate:value":o[0]||(o[0]=C=>p.type=C),onChange:p.change,placeholder:"道具类型",options:p.configState.prize_type,style:{width:"100px"}},null,8,["value","onChange","options"]),p.type===1||p.type===2?(u(),k(d,{key:0,value:p.itemKey,"onUpdate:value":o[1]||(o[1]=C=>p.itemKey=C),showSearch:"",placement:"bottomRight",placeholder:"请输入多语言key","filter-option":!1,"not-found-content":p.loading?void 0:null,"option-label-prop":"name",onSearch:p.remoteMethod,onSelect:p.select,style:{width:"230px"},options:p.options,"label-in-value":"",dropdownStyle:{width:"300px !important",color:"red"}},{option:a(C=>[D("div",null,G(C.name),1),D("div",Ne,G(C.item_id),1)]),_:1},8,["value","not-found-content","onSearch","onSelect","options"])):h("",!0)]),_:1}),e(b,{type:"primary",onClick:p.addItem},{icon:a(()=>[e(c)]),_:1},8,["onClick"])]),_:1}),e(w,{style:{"margin-top":"5px"},size:"small",columns:p.GIFT_ITEM_COLUMNS_MAP,"data-source":p.value,pagination:!1},{bodyCell:a(({record:C,index:I,column:P})=>[P.key==="item_id"?(u(),k(l,{key:0,item:C,"show-num":!1},null,8,["item"])):h("",!0),P.key==="num"?(u(),k(_,{key:1,value:p.value[I].num,"onUpdate:value":m=>p.value[I].num=m,style:{width:"60px"}},null,8,["value","onUpdate:value"])):h("",!0),P.key==="action"?(u(),k(v,{key:2,type:"danger",onClick:()=>p.value.splice(I,1)},{default:a(()=>[x("删除")]),_:2},1032,["onClick"])):h("",!0)]),_:1},8,["columns","data-source"])],64)}const Be=j(Ge,[["render",Ae],["__scopeId","data-v-87fb91ce"]]),je=M({__name:"Form",props:["editId"],emits:["close","refresh"],setup(p,{emit:o}){const y=p,s=L({id:0,gift_id:"",name:"",desc_key:void 0,icon_url:"",item_list:[],gift_value:0}),i=L(!0),g=L(!1),d=()=>{g.value=!0,ee(y.editId).then(f=>{s.value={...s.value,...f},s.value.item_list=JSON.parse(f.item_list)}).finally(()=>g.value=!1)};y.editId&&d();const r=L(!1),c=L(),b=()=>{c.value.validate().then(()=>{r.value=!0;const{id:f,...l}=s.value;l.item_list=JSON.stringify(l.item_list.map(_=>({internal_id:_.internal_id,item_id:_.item_id,num:_.num,gift_type:_.gift_type,game_project:_.game_project}))),y.editId?te(f,l).then(()=>{o("close"),o("refresh")}).catch(()=>{}).finally(()=>{r.value=!1}):ae(l).then(()=>{o("close"),o("refresh")}).catch(()=>{}).finally(()=>{r.value=!1})}).catch(()=>{})};return(f,l)=>{const _=t("a-input"),v=t("a-form-item"),w=t("SelectLang"),C=t("SelectImg"),I=t("a-input-number"),P=t("ExclamationCircleFilled"),m=t("a-typography-text"),$=t("a-button"),F=t("a-form"),R=t("a-spin"),U=t("a-drawer");return u(),k(U,{open:n(i),"onUpdate:open":l[6]||(l[6]=S=>W(i)?i.value=S:null),title:"编辑规则",maskClosable:!1,width:600,onAfterOpenChange:l[7]||(l[7]=S=>!S&&o("close"))},{default:a(()=>[e(R,{spinning:n(g)},{default:a(()=>[e(F,{model:n(s),name:"basic",ref_key:"formRef",ref:c,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:a(()=>[e(v,{label:"gift_id",name:"gift_id"},{default:a(()=>[e(_,{value:n(s).gift_id,"onUpdate:value":l[0]||(l[0]=S=>n(s).gift_id=S),placeholder:"创建后自动生成，不支持修改",disabled:""},null,8,["value"])]),_:1}),e(v,{label:"礼包名称(后台)",name:"name"},{default:a(()=>[e(_,{value:n(s).name,"onUpdate:value":l[1]||(l[1]=S=>n(s).name=S),placeholder:"仅做后台展示用"},null,8,["value"])]),_:1}),e(v,{label:"礼包名称(前台)",name:"desc_key"},{default:a(()=>[e(w,{value:n(s).desc_key,"onUpdate:value":l[2]||(l[2]=S=>n(s).desc_key=S),placeholder:"选择多语言key,不填写则默认使用道具名称"},null,8,["value"])]),_:1}),e(v,{label:"礼包icon",name:"icon_url"},{default:a(()=>[e(C,{value:n(s).icon_url,"onUpdate:value":l[3]||(l[3]=S=>n(s).icon_url=S),"width-height":[72,72]},null,8,["value"])]),_:1}),e(v,{label:"礼包价值",name:"gift_value",rules:[{required:!0,message:"请输入礼包价值"}]},{default:a(()=>[e(I,{value:n(s).gift_value,"onUpdate:value":l[4]||(l[4]=S=>n(s).gift_value=S),placeholder:"请输入礼包价值","addon-after":"积分"},null,8,["value"]),e(m,{type:"warning",style:{"font-size":"12px",display:"block"}},{default:a(()=>[e(P),x(" 会员专享礼包需填写，发生退款时扣除对应积分 ")]),_:1})]),_:1}),e(v,{label:"添加道具",name:"item_list",rules:[{type:"array",required:!0,message:"请添加道具"}]},{default:a(()=>[e(Be,{value:n(s).item_list,"onUpdate:value":l[5]||(l[5]=S=>n(s).item_list=S)},null,8,["value"])]),_:1}),e(v,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[e($,{type:"primary",onClick:b,loading:n(r)},{default:a(()=>[x("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open"])}}}),Ke=M({__name:"Index",setup(p){const o=N({editVisible:!1,editId:0}),y=L(),s=()=>y.value.requestTableData(!0),i=(d,r)=>{o.editVisible=d,o.editId=r||0},g=d=>E("确定要删除此条数据吗？",le,{id:d}).then(()=>s());return(d,r)=>{const c=t("a-button"),b=t("UploadBtn"),f=t("PlusOutlined"),l=t("LangKey"),_=t("a-image"),v=t("a-divider"),w=t("a-typography-link"),C=t("a-space"),I=t("CustomTable"),P=H("has");return u(),T(O,null,[e(I,{ref_key:"RefCustomTable",ref:y,"data-api":n(oe),params:{},columns:n(Ie)},{leftTool:a(()=>[e(c,{type:"primary",onClick:r[0]||(r[0]=m=>i(!0))},{default:a(()=>[x("道具打包")]),_:1}),J(e(b,{style:{"margin-left":"30px"},ref:"uploadBtn",onUploadSuccess:s,fileType:"gift-package",page:n(q).GIFTCONFIG,downloadData:{},hideDownloadBtn:!0},null,8,["page"]),[[P,"Operation"]]),e(c,{style:{"margin-left":"30px"},type:"primary",onClick:r[1]||(r[1]=m=>n(ne)())},{icon:a(()=>[e(f)]),default:a(()=>[x(" 导出礼包 ")]),_:1})]),bodyCell:a(({record:m,column:$})=>[$.key==="desc_key"?(u(),k(l,{key:0,"lang-key":m.desc_key,i18n_name:m.i18n_name},null,8,["lang-key","i18n_name"])):h("",!0),$.key==="icon_url"?(u(),T(O,{key:1},[m.icon_url?(u(),k(_,{key:0,src:m.icon_url,height:60},null,8,["src"])):(u(),T(O,{key:1},[x("-")],64))],64)):h("",!0),$.key==="item_list"?(u(),T(O,{key:2},[m.item_list.length>=3?(u(),T(O,{key:0},[(u(!0),T(O,null,A(m.item_list.slice(0,m.show_all?m.item_list.length:2),(F,R)=>(u(),k(B,{style:{"margin-top":"5px"},item:F,key:R},null,8,["item"]))),128)),m.show_all?h("",!0):(u(),k(c,{key:0,type:"link",onClick:F=>m.show_all=!m.show_all},{default:a(()=>[x("查看全部")]),_:2},1032,["onClick"]))],64)):(u(!0),T(O,{key:1},A(m.item_list,(F,R)=>(u(),k(B,{style:{"margin-top":"5px"},item:F,key:R},null,8,["item"]))),128))],64)):h("",!0),$.key==="action"?(u(),k(C,{key:3},{split:a(()=>[e(v,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[e(w,{onClick:F=>i(!0,m.id)},{default:a(()=>[x("编辑")]),_:2},1032,["onClick"]),e(w,{type:"danger",onClick:F=>g(m.id)},{default:a(()=>[x("删除")]),_:2},1032,["onClick"])]),_:2},1024)):h("",!0)]),_:1},8,["data-api","columns"]),n(o).editVisible?(u(),k(je,{key:0,"edit-id":n(o).editId,onClose:r[2]||(r[2]=m=>i(!1)),onRefresh:s},null,8,["edit-id"])):h("",!0)],64)}}}),Ye=Object.freeze(Object.defineProperty({__proto__:null,default:Ke},Symbol.toStringTag,{value:"Module"})),Ve=M({__name:"GameProps",setup(p){const o=N({searchParams:{search_key:""}}),y=L(),s=()=>y.value.requestTableData(!0);return(i,g)=>{const d=t("a-col"),r=t("a-row"),c=t("a-input"),b=t("a-button"),f=t("a-space"),l=t("PropIcon"),_=t("CustomTable");return u(),T(O,null,[e(r,{justify:"space-between"},{default:a(()=>[e(d,{span:12})]),_:1}),e(_,{ref_key:"RefCustomTable",ref:y,"data-api":n(ie),params:n(o).searchParams,columns:n(we),"no-card":""},{top:a(()=>[e(f,{wrap:""},{default:a(()=>[e(c,{value:n(o).searchParams.search_key,"onUpdate:value":g[0]||(g[0]=v=>n(o).searchParams.search_key=v),placeholder:"请输入item_id或道具名称"},null,8,["value"]),e(b,{type:"primary",onClick:s},{default:a(()=>[x("搜索")]),_:1}),e(b,{onClick:g[1]||(g[1]=()=>{n(o).searchParams.search_key="",s()})},{default:a(()=>[x("重置")]),_:1})]),_:1})]),bodyCell:a(({column:v,record:w})=>[v.key==="image"?(u(),k(l,{key:0,"prop-detail":w,style:{width:"50px",height:"50px"}},null,8,["prop-detail"])):h("",!0)]),_:1},8,["data-api","params","columns"])],64)}}}),qe=M({__name:"TripartiteDetail",props:["editId"],emits:["close","refresh"],setup(p,{emit:o}){const y=p;ge.extend(ve);const s=L(!0),i=L(!1),g=()=>{i.value=!0,se(y.editId).then(l=>{d.value=l,l.online_at&&l.offline_at?(d.value.is_forever=0,d.value.times=[l.online_at,l.offline_at]):(d.value.is_forever=1,d.value.times=void 0)}).finally(()=>i.value=!1)};y.editId&&g();const d=L({id:0,name_key:void 0,item_id:"",image:"",type:2}),r=N({optionsVip:[{label:"三方券",value:2}]}),c=L(!1),b=L(),f=()=>{b.value.validate().then(()=>{c.value=!0;const{id:l,..._}=d.value;y.editId?pe(l,_).then(()=>{o("close"),o("refresh")}).catch(()=>{}).finally(()=>{c.value=!1}):de(_).then(()=>{o("close"),o("refresh")}).catch(()=>{}).finally(()=>{c.value=!1})}).catch(()=>{})};return(l,_)=>{const v=t("a-select"),w=t("a-form-item"),C=t("a-input"),I=t("SelectLang"),P=t("SelectImg"),m=t("a-button"),$=t("a-form"),F=t("a-spin"),R=t("a-drawer");return u(),k(R,{open:n(s),"onUpdate:open":_[4]||(_[4]=U=>W(s)?s.value=U:null),title:y.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:_[5]||(_[5]=U=>!U&&o("close"))},{default:a(()=>[e(F,{spinning:n(i)},{default:a(()=>[e($,{model:n(d),name:"basic",ref_key:"formRef",ref:b,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:a(()=>[e(w,{label:"道具类型",name:"type",rules:[{required:!0,message:"请选择道具类型"}]},{default:a(()=>[e(v,{style:{width:"100%"},value:n(d).type,"onUpdate:value":_[0]||(_[0]=U=>n(d).type=U),disabled:!0,options:n(r).optionsVip},null,8,["value","options"])]),_:1}),e(w,{label:"三方券id",name:"item_id",rules:[{required:!0,message:"请输入三方券id"}]},{default:a(()=>[e(C,{value:n(d).item_id,"onUpdate:value":_[1]||(_[1]=U=>n(d).item_id=U),placeholder:"请输入三方券id"},null,8,["value"])]),_:1}),e(w,{label:"道具名称",name:"name_key",rules:[{required:!0,message:"请选择道具名称多语言"}]},{default:a(()=>[e(I,{value:n(d).name_key,"onUpdate:value":_[2]||(_[2]=U=>n(d).name_key=U)},null,8,["value"])]),_:1}),e(w,{label:"道具icon",name:"image",rules:[{required:!0,message:"请上传道具icon"}]},{default:a(()=>[e(P,{value:n(d).image,"onUpdate:value":_[3]||(_[3]=U=>n(d).image=U),"width-height":[60,60]},null,8,["value"])]),_:1}),e(w,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[e(m,{type:"primary",onClick:f,loading:n(c)},{default:a(()=>[x("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),Ee=M({__name:"TripartiteProps",setup(p){const o=N({searchParams:{search_key:"",type:2},editVisible:!1,editId:0}),y=L(),s=()=>y.value.requestTableData(!0),i=(d,r)=>{o.editVisible=d,o.editId=r||0},g=(d,r)=>E(`确定要删除${r?"选中的":"此条"}数据吗？`,_e,{id:d}).then(()=>s());return(d,r)=>{const c=t("a-col"),b=t("a-row"),f=t("a-input"),l=t("a-button"),_=t("PlusOutlined"),v=t("a-space"),w=t("PropIcon"),C=t("CustomTable");return u(),T(O,null,[e(b,{justify:"space-between"},{default:a(()=>[e(c,{span:12})]),_:1}),e(C,{ref_key:"RefCustomTable",ref:y,"data-api":n(re),params:n(o).searchParams,columns:n(Ce),"no-card":""},{top:a(()=>[e(v,{wrap:""},{default:a(()=>[e(f,{value:n(o).searchParams.search_key,"onUpdate:value":r[0]||(r[0]=I=>n(o).searchParams.search_key=I),placeholder:"请输入道具名称"},null,8,["value"]),e(l,{type:"primary",onClick:s},{default:a(()=>[x("搜索")]),_:1}),e(l,{onClick:r[1]||(r[1]=()=>{n(o).searchParams.search_key="",s()})},{default:a(()=>[x("重置")]),_:1}),e(l,{type:"primary",onClick:r[2]||(r[2]=I=>i(!0))},{icon:a(()=>[e(_)]),default:a(()=>[x(" 新增道具 ")]),_:1})]),_:1})]),bodyCell:a(({column:I,record:P})=>[I.key==="type"?(u(),T(O,{key:0},[x(G(P.type===1?"游戏道具":"三方券"),1)],64)):h("",!0),I.key==="image"?(u(),k(w,{key:1,"prop-detail":P,style:{width:"50px",height:"50px"}},null,8,["prop-detail"])):h("",!0),I.key==="action"?(u(),k(v,{key:2,size:0},{default:a(()=>[e(l,{type:"link",onClick:m=>i(!0,P.id)},{default:a(()=>[x("编辑")]),_:2},1032,["onClick"]),e(l,{type:"link",danger:"",onClick:m=>g(P.id,!1)},{default:a(()=>[x("删除")]),_:2},1032,["onClick"])]),_:2},1024)):h("",!0)]),_:1},8,["data-api","params","columns"]),n(o).editVisible?(u(),k(qe,{key:0,"edit-id":n(o).editId,onClose:r[3]||(r[3]=I=>i(!1)),onRefresh:s},null,8,["edit-id"])):h("",!0)],64)}}}),ze={components:{GameProps:Ve,TripartiteProps:Ee}},He=M({...ze,__name:"Index",setup(p){const o=[{label:"游戏道具",key:"GameProps"},{label:"三方道具",key:"TripartiteProps"}],y=N({activeKey:o[0].key});return(s,i)=>{const g=t("a-tab-pane"),d=t("a-tabs"),r=t("a-card");return u(),k(r,{class:"mw-child-h-auto"},{default:a(()=>[e(d,{activeKey:n(y).activeKey,"onUpdate:activeKey":i[0]||(i[0]=c=>n(y).activeKey=c),class:"tabs-wrap"},{default:a(()=>[(u(),T(O,null,A(o,c=>e(g,{tab:c.label,key:c.key,class:"tab-pane"},{default:a(()=>[c.key===n(y).activeKey?(u(),k(ke(c.key),{key:0})):h("",!0)]),_:2},1032,["tab"])),64))]),_:1},8,["activeKey"])]),_:1})}}});const Je=j(He,[["__scopeId","data-v-bf9c8db7"]]),et=Object.freeze(Object.defineProperty({__proto__:null,default:Je},Symbol.toStringTag,{value:"Module"}));export{Qe as I,Ye as a,et as b};
