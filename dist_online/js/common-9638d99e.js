import{bs as M,a as I,bt as g,_ as R,b as f,e as v,f as O,J as Y,d as L,l as E,h as m,g as h,j as r,k,q as z,s as N,bu as ee,u as P,bv as ce,bw as ue,bx as pe,by as _e,bz as F,r as de,bA as me,bB as A,G as U,a7 as ge,a9 as x,F as j,n as $,m as he,bC as te,t as G,aa as K,I as q,H as Z}from"./vendor-6ece677a.js";import{m as y,M as fe}from"./antd-79d05377.js";const ve=M("system",()=>{const e=I({asideStatus:!1,env:""});return{systemState:e,setAsideStatus:()=>{e.asideStatus=!e.asideStatus},setCrtEnv:s=>{e.env=s}}}),be=[{index:4,path:"/dailyCheck",name:"DailyCheck",redirect:"/dailyCheck/list",meta:{title:"签到活动",icon:"TagsOutlined"},children:[{path:"/dailyCheck/list",name:"DailyCheckList",component:()=>g(()=>import("./dailycheck-15716145.js").then(e=>e.I),["js/dailycheck-15716145.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"签到活动",icon:"",breadcrumb:!1}},{path:"/dailyCheck/detail/:id",name:"DailyCheckDetail",component:()=>g(()=>import("./dailycheck-15716145.js").then(e=>e.D),["js/dailycheck-15716145.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"奖励详情",hidden:!0,icon:""}}]}],ye=Object.freeze(Object.defineProperty({__proto__:null,default:be},Symbol.toStringTag,{value:"Module"})),Ce=[{index:10,path:"/datamanage",name:"DataManage",redirect:"/datamanage/points",meta:{title:"数据明细",icon:"DatabaseOutlined"},children:[{path:"/datamanage/points",name:"DataManagePoints",component:()=>g(()=>import("./datamanage-6bc3f374.js").then(e=>e.I),["js/datamanage-6bc3f374.js","js/vendor-6ece677a.js"]),meta:{title:"积分明细",icon:""}},{path:"/datamanage/growth",name:"DataManageGrowth",component:()=>g(()=>import("./datamanage-6bc3f374.js").then(e=>e.a),["js/datamanage-6bc3f374.js","js/vendor-6ece677a.js"]),meta:{title:"成长值明细",icon:""}}]}],ke=Object.freeze(Object.defineProperty({__proto__:null,default:Ce},Symbol.toStringTag,{value:"Module"})),Pe=[],Ee=Object.freeze(Object.defineProperty({__proto__:null,default:Pe},Symbol.toStringTag,{value:"Module"})),Oe=[],Le=Object.freeze(Object.defineProperty({__proto__:null,default:Oe},Symbol.toStringTag,{value:"Module"})),Se=[{index:3,path:"/minigame",name:"Minigame",redirect:"/minigame/list",meta:{title:"小游戏",icon:"TrophyOutlined"},children:[{path:"/minigame/list",name:"MinigameList",component:()=>g(()=>import("./minigame-a43fed04.js"),["js/minigame-a43fed04.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"小游戏",icon:"",breadcrumb:!1}}]}],Ie=Object.freeze(Object.defineProperty({__proto__:null,default:Se},Symbol.toStringTag,{value:"Module"})),Re={};function Te(e,t){const o=f("router-view");return v(),O(o)}const De=R(Re,[["render",Te]]),$e=[{index:2,path:"/operation",name:"Operation",redirect:"/operation/sdkvajra",meta:{title:"运营位",icon:"FlagOutlined"},children:[{path:"/operation/sdkvajra",name:"OperationSDKVajra",component:()=>g(()=>import("./operation-686caee1.js").then(e=>e.I),["js/operation-686caee1.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"SDK金刚区",icon:""}},{path:"/operation/h5vajra",name:"OperationH5Vajra",component:()=>g(()=>import("./operation-686caee1.js").then(e=>e.a),["js/operation-686caee1.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"H5金刚区",icon:""}},{path:"/operation/banner",name:"OperationBanner",component:Y(De),redirect:"/operation/banner/group",meta:{title:"活动Banner",icon:""},children:[{path:"/operation/banner/group",name:"OperationBannerList",component:()=>g(()=>import("./operation-686caee1.js").then(e=>e.b),["js/operation-686caee1.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"活动Banner",icon:"",breadcrumb:!1}},{path:"/operation/banner/detail/:id",name:"OperationBannerDetail",component:()=>g(()=>import("./operation-686caee1.js").then(e=>e.D),["js/operation-686caee1.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"Banner详情",icon:"",hidden:!0,permission:"OperationBannerList"}}]},{path:"/operation/sdkiconspush",name:"OperationSDKIconsPush",component:()=>g(()=>import("./operation-686caee1.js").then(e=>e.c),["js/operation-686caee1.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"SDK特殊图标推送",icon:"",not_funplus_zone:!0}},{path:"/operation/sdkads",name:"OperationSDKAds",component:()=>g(()=>import("./operation-686caee1.js").then(e=>e.d),["js/operation-686caee1.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"SDK广告位",icon:"",not_funplus_zone:!0}}]}],Ae=Object.freeze(Object.defineProperty({__proto__:null,default:$e},Symbol.toStringTag,{value:"Module"})),je=[{index:1,path:"/platform",name:"Platform",redirect:"/platform/entrance",meta:{title:"平台配置",icon:"GoldOutlined",funplus_zone:!0},children:[{path:"/platform/game",name:"PlatformGame",component:()=>g(()=>import("./platform-bec26015.js").then(e=>e.I),["js/platform-bec26015.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"游戏接入",icon:""}},{path:"/platform/entrance",name:"PlatformEntrance",component:()=>g(()=>import("./platform-bec26015.js").then(e=>e.a),["js/platform-bec26015.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"入口管理",icon:""}},{path:"/platform/rule",name:"PlatformRule",component:()=>g(()=>import("./platform-bec26015.js").then(e=>e.b),["js/platform-bec26015.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"通用规则",icon:""}},{path:"/platform/combinerule",name:"CombineRule",component:()=>g(()=>import("./platform-bec26015.js").then(e=>e.c),["js/platform-bec26015.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css"]),meta:{title:"融合开关",icon:""}}]}],we=Object.freeze(Object.defineProperty({__proto__:null,default:je},Symbol.toStringTag,{value:"Module"})),Fe=[{index:7,path:"/points",name:"Points",redirect:"/points/task",meta:{title:"积分系统",icon:"ProjectOutlined"},children:[{path:"/points/task",name:"PointsTask",component:()=>g(()=>import("./PointsTask-5f9442fa.js"),["js/PointsTask-5f9442fa.js","js/antd-79d05377.js","js/vendor-6ece677a.js","css/antd-25a63267.css","css/Index-70edb989.css"]),meta:{title:"任务体系",icon:""}},{path:"/points/store",name:"PointsStore",component:()=>g(()=>import("./pointsStore-57120708.js"),["js/pointsStore-57120708.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css","css/Index-4b6ddb38.css"]),meta:{title:"积分商城",icon:""}}]}],Ge=Object.freeze(Object.defineProperty({__proto__:null,default:Fe},Symbol.toStringTag,{value:"Module"})),Me=[{index:5,path:"/pushmessage",name:"Pushmessage",redirect:"/pushmessage/list",meta:{title:"消息推送",icon:"MessageOutlined"},children:[{path:"/pushmessage/list",name:"PushmessageList",component:()=>g(()=>import("./pushMessage-01c3661d.js"),["js/pushMessage-01c3661d.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css","css/Index-95488f3c.css"]),meta:{title:"消息推送",icon:"",breadcrumb:!1}}]}],Ue=Object.freeze(Object.defineProperty({__proto__:null,default:Me},Symbol.toStringTag,{value:"Module"})),Be=[{index:8,path:"/resource",name:"Resource",redirect:"/resource/language",meta:{title:"资源管理",icon:"DropboxOutlined"},children:[{path:"/resource/language",name:"ResourceLanguage",component:()=>g(()=>import("./resource-5df8dfef.js").then(e=>e.I),["js/resource-5df8dfef.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css","css/resource-3c9f175e.css"]),meta:{title:"多语言管理",icon:""}},{path:"/resource/gift",name:"ResourceGift",component:()=>g(()=>import("./resource-5df8dfef.js").then(e=>e.a),["js/resource-5df8dfef.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css","css/resource-3c9f175e.css"]),meta:{title:"礼包列表",icon:""}},{path:"/resource/props",name:"ResourceProps",component:()=>g(()=>import("./resource-5df8dfef.js").then(e=>e.b),["js/resource-5df8dfef.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css","css/resource-3c9f175e.css"]),meta:{title:"道具表管理",icon:""}}]}],Ve=Object.freeze(Object.defineProperty({__proto__:null,default:Be},Symbol.toStringTag,{value:"Module"})),ze=[{index:6,path:"/rights",name:"Rights",redirect:"/rights/entrance",meta:{title:"权益配置",icon:"RightSquareOutlined"},children:[{path:"/rights/entrance",name:"RightsEntrance",component:()=>g(()=>import("./rights-2c6bf5bd.js").then(e=>e.I),["js/rights-2c6bf5bd.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css","css/rights-72343ce9.css"]),meta:{title:"权益入口",icon:""}},{path:"/rights/gift",name:"RightsGift",component:()=>g(()=>import("./rights-2c6bf5bd.js").then(e=>e.a),["js/rights-2c6bf5bd.js","js/vendor-6ece677a.js","js/antd-79d05377.js","css/antd-25a63267.css","css/rights-72343ce9.css"]),meta:{title:"专属礼包",icon:"",not_funplus_zone:!0}}]}],Ne=Object.freeze(Object.defineProperty({__proto__:null,default:ze},Symbol.toStringTag,{value:"Module"})),xe=[{index:9,path:"/user",name:"User",redirect:"/user/list",meta:{title:"用户管理",icon:"UsergroupAddOutlined"},children:[{path:"/user/list",name:"UserList",component:()=>g(()=>import("./user-f891b6ea.js"),["js/user-f891b6ea.js","js/vendor-6ece677a.js"]),meta:{title:"用户管理",icon:"",breadcrumb:!1}}]}],He=Object.freeze(Object.defineProperty({__proto__:null,default:xe},Symbol.toStringTag,{value:"Module"})),W=Object.assign({"./dailyCheck.ts":ye,"./dataManage.ts":ke,"./dev.ts":Ee,"./home.ts":Le,"./minigame.ts":Ie,"./operation.ts":Ae,"./platformConf.ts":we,"./points.ts":Ge,"./pushMessage.ts":Ue,"./resource.ts":Ve,"./rights.ts":Ne,"./users.ts":He});let B=[];for(const e in W)B=B.concat(W[e].default);const Ke=B,qe="/gif/kit-risk-management-forecasting-and-assessment-6a89fdde.gif",T=e=>(z("data-v-1e705d2e"),e=e(),N(),e),Ze={class:"wrapper darkAmber"},We=T(()=>r("div",{class:"view"},[r("img",{src:qe,alt:""})],-1)),Je=T(()=>r("div",{class:"panel-logo"},[r("svg",{width:"100px",height:"25px",viewBox:"0 0 300 75",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",class:"w-100"},[r("g",{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[r("g",{fill:"#FF5A00"},[r("path",{d:"M55.8479648,42.9125049 L55.8479648,62.9227814 C55.8479648,64.4627289 54.6047234,65.7133712 53.0738887,65.7133712 L11.702407,65.7133712 C10.0757079,65.7133712 8.7965173,64.3090355 8.9403139,62.6786801 L12.0798731,26.5908697 C12.2056951,25.1503709 13.4040002,24.0443812 14.8419662,24.0443812 L37.0914951,24.0443812 C36.5462663,22.6641543 36.2466901,21.1573563 36.2466901,19.5812457 C36.2466901,18.0051351 36.5462663,16.4983371 37.0914951,15.1181102 L14.3326866,15.1181102 C8.58082239,15.1181102 3.78161069,19.542069 3.27832257,25.3070778 L0.0428989523,62.5099188 C-0.523300181,69.0343538 4.58747083,74.6456693 11.097263,74.6456693 L53.6281047,74.6456693 C59.7544393,74.6456693 64.7244094,69.6491274 64.7244094,63.4833102 L64.7244094,42.9125049 C63.3523502,43.4609793 61.8544689,43.7623389 60.287685,43.7623389 C58.7209012,43.7623389 57.2200241,43.4609793 55.8479648,42.9125049 Z",id:"Path"}),r("path",{d:"M73.9397415,13.4673005 L70.0663235,13.4673005 C68.027997,13.4673005 66.3752191,11.8145226 66.3752191,9.7761962 L66.3752191,5.9027782 C66.3752191,2.64205363 63.7331655,0 60.4724409,0 C57.2117164,0 54.5696627,2.64205363 54.5696627,5.9027782 L54.5696627,9.7761962 C54.5696627,11.8145226 52.9168848,13.4673005 50.8785584,13.4673005 L47.0051404,13.4673005 C43.7444158,13.4673005 41.1023622,16.1093542 41.1023622,19.3700787 C41.1023622,22.6308033 43.7444158,25.2728569 47.0051404,25.2728569 L50.8785584,25.2728569 C52.9168848,25.2728569 54.5696627,26.9256348 54.5696627,28.9639613 L54.5696627,32.8373793 C54.5696627,36.0981038 57.2117164,38.7401575 60.4724409,38.7401575 C63.7331655,38.7401575 66.3752191,36.0981038 66.3752191,32.8373793 L66.3752191,28.9639613 C66.3752191,26.9256348 68.027997,25.2728569 70.0663235,25.2728569 L73.9397415,25.2728569 C77.2004661,25.2728569 79.8425197,22.6308033 79.8425197,19.3700787 C79.8425197,16.1093542 77.2004661,13.4673005 73.9397415,13.4673005 Z",id:"Path"}),r("path",{d:"M290.974807,40.287923 C287.781185,38.5244652 285.229872,37.0819087 285.229872,34.5986722 C285.229872,32.2743869 286.986513,30.0310767 290.81647,30.0310767 C293.290108,30.0310767 296.002745,31.1527318 296.961727,31.9534856 L298.718369,26.1862588 C298.079047,25.6254313 294.649414,23.6220472 290.338472,23.6220472 C283.075895,23.6220472 277.88962,27.2269388 277.88962,34.1967958 C277.88962,40.0449977 281.322241,43.1700368 285.23286,45.0924457 C289.463139,47.1738058 292.815098,48.8562884 292.815098,51.9033516 C292.815098,55.349292 289.941137,57.2717009 286.272504,57.2717009 C283.398544,57.2717009 280.207909,55.5892183 278.770929,54.4675631 L276.377953,61.5183952 C279.96891,63.6807304 282.442548,64.7244094 286.75349,64.7244094 C294.655389,64.7244094 300,59.3560601 300,52.227252 C299.994025,45.5752973 295.444085,42.7711594 290.974807,40.287923 Z",id:"Path"}),r("path",{d:"M199.113788,24.0944882 L195.96498,24.0944882 L190.743157,24.0944882 L188.503937,24.0944882 L188.503937,64.7244094 L195.96498,64.7244094 L195.96498,50.9325182 L199.11681,50.9325182 C208.070667,50.9325182 213.543307,46.0443676 213.543307,37.5150128 C213.540285,28.9826388 208.067645,24.0944882 199.113788,24.0944882 Z M199.113788,43.4780131 L195.96498,43.4780131 L195.96498,31.5489933 L199.11681,31.5489933 C202.930434,31.5489933 205.251244,33.6201924 205.251244,37.5119936 C205.248223,41.406814 202.927412,43.4780131 199.113788,43.4780131 Z",id:"Shape","fill-rule":"nonzero"}),r("polygon",{id:"Path",points:"226.600431 24.0944882 219.212598 24.0944882 219.212598 64.7244094 221.839782 64.7244094 226.600431 64.7244094 240.472441 64.7244094 240.472441 57.6412709 226.600431 57.6412709"}),r("polygon",{id:"Path",points:"172.128253 48.822069 159.796076 24.0944882 151.653543 24.0944882 151.653543 64.7244094 159.055846 64.7244094 159.055846 39.9998479 171.388023 64.7244094 179.527559 64.7244094 179.527559 24.0944882 172.128253 24.0944882"}),r("path",{d:"M135.249136,51.9500321 C135.249136,55.7694254 132.854357,58.0937472 128.976378,58.0937472 C125.098399,58.0937472 122.70362,55.7694254 122.70362,51.9500321 L122.70362,24.0944882 L115.275591,24.0944882 L115.275591,50.7485272 C115.275591,59.7159301 120.474308,65.1968504 128.976378,65.1968504 C137.478448,65.1968504 142.677165,59.7159301 142.677165,50.7485272 L142.677165,24.0944882 L135.249136,24.0944882 L135.249136,51.9500321 Z",id:"Path"}),r("path",{d:"M264.225514,51.9500321 C264.225514,55.7694254 261.830735,58.0937472 257.952756,58.0937472 C254.074777,58.0937472 251.679998,55.7694254 251.679998,51.9500321 L251.679998,24.0944882 L244.251969,24.0944882 L244.251969,50.7485272 C244.251969,59.7159301 249.450686,65.1968504 257.952756,65.1968504 C266.454826,65.1968504 271.653543,59.7159301 271.653543,50.7485272 L271.653543,24.0944882 L264.225514,24.0944882 L264.225514,51.9500321 Z",id:"Path"}),r("polygon",{id:"Path",points:"88.9916386 24.0944882 86.9291339 24.0944882 86.9291339 64.7244094 94.3523468 64.7244094 94.3523468 47.931393 107.055091 47.931393 107.055091 40.8482545 94.3523468 40.8482545 94.3523468 32.4819387 107.716535 32.4819387 107.716535 24.0944882 94.3523468 24.0944882"})])])]),k(" - "),r("span",{class:"sys-name"},"FunZone")],-1)),Qe=T(()=>r("div",{class:"panel-style-logo"},null,-1)),Xe=T(()=>r("div",{class:"panel-header"},[r("p",null,"私域管理平台")],-1)),Ye=T(()=>r("i",{class:"icon-feishu"},null,-1)),et=T(()=>r("span",null,"登录",-1)),tt=L({__name:"Index",setup(e){const t=()=>{location.href="https://admin-center.funplus.com/backend/admin/loginSso?system=1&referer="+encodeURIComponent(`${location.origin}/login`)};return(o,s)=>{const n=f("kinesis-element"),l=f("kinesis-container");return v(),E("div",Ze,[m(l,{class:"kinesis-wrap"},{default:h(()=>[m(n,{strength:-8,class:"kinesis-style kinesis-style-1"}),m(n,{strength:10,class:"kinesis-style kinesis-style-2"}),m(n,{strength:-5,class:"kinesis-style kinesis-style-3"}),m(n,{strength:-8,class:"kinesis-style kinesis-style-4"}),m(n,{strength:10,class:"kinesis-style kinesis-style-5"}),m(n,{strength:-5,class:"kinesis-style kinesis-style-6"}),m(n,{strength:-8,class:"kinesis-style kinesis-style-7"}),m(n,{strength:10,class:"kinesis-style kinesis-style-8"}),m(n,{strength:5,class:"kinesis-view"},{default:h(()=>[We]),_:1}),r("div",{class:"glass-container kinesis-panel"},[Je,Qe,Xe,r("div",{class:"panel-btn-main",onClick:t},[Ye,k(" - "),et])])]),_:1})])}}});const nt=R(tt,[["__scopeId","data-v-1e705d2e"]]),ot=L({__name:"Error",setup(e){const t=ee();return(o,s)=>{const n=f("a-button"),l=f("a-space"),a=f("a-result");return v(),O(a,{status:"403",title:"403"},{subTitle:h(()=>[k(" 抱歉，您没有当前页面的权限或页面不存在，请飞书联系管理员: 谷翼涵！ ")]),extra:h(()=>[m(l,null,{default:h(()=>[m(n,{type:"primary",onClick:s[0]||(s[0]=c=>P(t).replace("/"))},{default:h(()=>[k("回到首页")]),_:1})]),_:1})]),_:1})}}});const st=R(ot,[["__scopeId","data-v-11608d83"]]),at=L({__name:"Redirect",setup(e){const t=ce();return ee().replace(t.fullPath.replace("/redirect","")),(s,n)=>(v(),E("div"))}}),it=[{path:"/login",name:"Login",component:nt,meta:{hidden:!0,title:"登录"}},{path:"/redirect/:char(\\S+)+",name:"Redirect",component:Y(at),meta:{hidden:!0}},{path:"/error",redirect:"/403",meta:{hidden:!0},children:[{path:"/403",name:"Error",component:st,meta:{hidden:!0}}]}],rt=Object.freeze(Object.defineProperty({__proto__:null,default:it},Symbol.toStringTag,{value:"Module"})),J=Object.assign({"./userManager.ts":rt});let V=[];for(const e in J)V=V.concat(J[e].default);const lt=V,ne=[...lt],ct=[...Ke.sort((e,t)=>e.index-t.index)],S=ue({history:pe("/"),routes:ne}),C={MULTILINGUAL:"multilingual",PLATFORM_PROPS:"platformProps",DAILY_CHECK:"dailyCheck",LADDERCONFIGS:"ladderConfigs",RIGHTGIFT:"rightGift",TASKCONFIG:"taskConfig",TASKRULE:"taskRule",PRODUCTCONFIG:"productConfig",GIFTCONFIG:"giftConfig"};var w=(e=>(e[e.PAGE=1]="PAGE",e[e.BUTTON=3]="BUTTON",e))(w||{}),ut=(e=>(e[e.CLOSE=0]="CLOSE",e[e.OPEN=1]="OPEN",e))(ut||{});const sn=["/login","/403","/error"],an=[{value:1,img:new URL("/jpeg/banner_type_1-4ffe5097.jpeg",self.location).href,label:"样式一",width:686,height:292},{value:3,img:new URL("/jpeg/banner_type_2-40c10cde.jpeg",self.location).href,label:"样式二",width:686,height:292}],oe=(e=[],t=[])=>{const o=localStorage.getItem("crtGame"),s=[];return t.forEach((n,l)=>{e.forEach((a,c)=>{var p,d;if(!(o==="funplus_zone"&&((p=n.meta)!=null&&p.not_funplus_zone))&&!(o!=="funplus_zone"&&((d=n.meta)!=null&&d.funplus_zone))&&n.name&&a.component_name===n.name&&a.category===w.PAGE){n.meta.id=a.id,n.meta.sort=a.sort,n.key=a.path,a._child=a._child||[];const _=a._child.filter(b=>b.category===w.BUTTON);n.meta.rule={button:_.map(b=>b.component_name.trim())};const i=a._child.filter(b=>b.category===w.PAGE);n.children=oe(i,n.children),n.redirect=n.children[0]?n.children[0].path:"",s.push(n)}})}),s.sort((n,l)=>l.meta.sort-n.meta.sort)},se=e=>{let t={};return e.forEach(o=>{t[o.name]=o.meta.id,o.children&&o.children.length&&(t=Object.assign(t,se(o.children)))}),t},ae=M("permission",()=>{const e=I({routes:[],dynamicRoutes:[],commonBtnPromissions:[],permissionMap:{},subRoutes:[]}),t=a=>{e.permissionMap=a},o=a=>{e.routes=a.concat(ne),e.dynamicRoutes=a};return{permissionState:e,setPermissionMap:t,setRoutes:o,setBtnPromise:a=>{a.forEach(c=>{c.name==="通用配置管理"&&c._child&&c._child.forEach(p=>e.commonBtnPromissions.push(p.component_name))})},SET_ROUTES:a=>{const c=oe(a,ct);t(se(c)),o(c)},setSubRoutes:a=>{e.subRoutes=a.filter(c=>{var p;return!((p=c.meta)!=null&&p.hidden)})}}}),pt=e=>{const t=new Blob([e.data],{type:e.headers["content-type"]});if(e.headers["content-type"]==="application/json"){const o=new FileReader;o.readAsText(t,"utf-8"),o.onload=function(){e=JSON.parse(o.result)}}else{const o=(e.headers["content-disposition"]||e.headers["Content-Disposition"]||"").split("filename=")[1],s=document.createElement("a");s.href=window.URL.createObjectURL(t),s.download=o,s.click()}},H=_e.create({timeout:6e4});H.interceptors.request.use(e=>e,e=>Promise.reject(e));H.interceptors.response.use(async e=>{if(e.status===200){if(e.config.responseType==="arraybuffer")return pt(e),Promise.resolve("");const t=e.data;if(t.errCode===0||t.code===200){const{setCrtEnv:o}=ve();return t.env&&o(t.env),t.data}else{if(t.code===403)y.error({content:"登录已失效，请重新登录！",onClose:()=>{S.replace("/login")}});else{const o=typeof t.msg=="string"?t.msg:"",s=typeof t.errMsg=="string"?t.errMsg:"",n=o||s;n&&y.error(n)}return Promise.reject(e.data)}}else return Q(e),Promise.reject(e.data),e},e=>{const{response:t}=e;return t?(Q(t),Promise.reject(t.data)):(y.error("网络连接异常,请稍后再试!"),Promise.reject("网络连接异常,请稍后再试!"))});const Q=e=>{switch(e.status){case 401:break;case 403:break;case 404:y.error("网络连接异常,请稍后再试!");break;case 50:break;case 30:y.error({content:"登录已失效，请重新登录！",onClose:()=>{S.replace("/login")}});break;default:y.error(e.msg||e.errMsg)}},rn={isShow:(e,t="button")=>{const o=S.currentRoute;return(o.value.meta.rule&&o.value.meta.rule[t]||[]).indexOf(e)>-1},checkBtnPromission:e=>{const{permissionState:t}=F(ae());return t.value.commonBtnPromissions.indexOf(e)>-1}},X=e=>e?/^[a-zA-Z0-9_]*$/.test(e):!1,_t=e=>Object.prototype.toString.call(e)==="[object FormData]",ln=(e,t,o)=>new Promise((s,n)=>{fe.confirm({title:"提示",content:e,onOk:()=>new Promise((l,a)=>{t(o).then(()=>{y.success("操作成功"),l("操作成功"),s("操作成功")}).catch(()=>a())}),onCancel:()=>{n()}})}),cn=(e,t)=>{const o={f_os:"all",f_channel:"all",lang:"all",f_s_ids:"0-99999",f_lv_ids:"0-99999"};t.forEach(s=>{e.is_filter,e[s]=e.is_filter===1?o[s]:""})},u={get(e,t={}){return u.request("GET",e,{params:D(t)})},post(e,t={}){return u.request("POST",e,{data:D(t)})},put(e,t={}){return u.request("PUT",e,{data:D(t)})},delete(e,t={}){return u.request("DELETE",e,{data:D(t)})},downfile(e,t={}){return u.request("POST",e,{data:D(t),responseType:"arraybuffer"})},request(e="GET",t,o={}){const{userState:s}=F(ie());return t.startsWith("/api")&&s.value.crt_game&&(t=s.value.crt_game.api_server_url+t),t.startsWith("/backend")&&(t=t.replace("/backend","https://admin-center.funplus.com/backend")),new Promise((n,l)=>{H({method:e,url:t,...o}).then(a=>{n(a)}).catch(a=>{l(a)})})}},D=(e={})=>{var n,l;const{userState:t}=F(ie()),{permissionState:o}=F(ae()),s={admin_project:(n=t.value.crt_game)==null?void 0:n.game_project,system:"funplus_zone",ticket:t.value.ticket,permission_id:S.currentRoute.value.meta.permission&&o.value.permissionMap[S.currentRoute.value.meta.permission]||S.currentRoute.value.meta.id||"",user_id:t.value.userInfo.id,user_name:t.value.userInfo.username};return e.is_admin?(e.game_project=(l=t.value.crt_game)==null?void 0:l.game_project,delete e.is_admin):s.admin_project!=="funplus_zone"&&(e.game_project=s.admin_project),_t(e)?Object.keys(s).forEach(a=>{e.append(a,s[a])}):e={...s,...e},e},dt=e=>u.get("/backend/admin/publicSystemGame",{...e,is_admin:!0}),mt=e=>u.get("/backend/admin/publicChildPermission",{...e,is_admin:!0}),ie=M("user",()=>{const e=I({isLogin:!1,ticket:"",loading:!1,userInfo:{email:"",id:void 0,permission_list:[],route_path:[],username:""},commonBtnPromissions:[],game_infos:[]}),t=c=>{e.isLogin=!0,e.userInfo={...c}},o=()=>{const c=localStorage.getItem("ticket");localStorage.removeItem("ticket"),location.href=`https://admin-center.funplus.com/backend/admin/publicLogout?system=1&referer=&ticket=${c}`+encodeURIComponent(`${location.origin}/login`)},s=c=>{e.ticket=c},n=c=>{c.forEach(p=>{p.component==="CommonPromission"&&p._child&&p._child.forEach(d=>{e.commonBtnPromissions.push(d.component)})})};return{userState:e,login:t,logout:o,setTicket:s,saveBtnPromis:n,FETCH_PERMISSION:async()=>new Promise((c,p)=>{if(e.userInfo.permission_list.length)return c("success");e.ticket===""&&p(Error("未检测到ticket!"));const d={ticket:e.ticket};mt(d).then(_=>{n(_.permission_list),t(_),c("success")}).catch(()=>{p(Error("权限请求失败"))})}),FETCH_GAME_PERMISSION:async()=>new Promise((c,p)=>{if(e.game_infos.length)return c("success");if(e.ticket==="")return p(Error("未检测到ticket!"));const d={ticket:e.ticket};dt(d).then(_=>{if(e.game_infos=_.map(i=>({...i,gmUrl:{VITE_APP_SYSTEM:"funplus_zone",VITE_APP_ADMIN_CENTER_API:"https://admin-center.funplus.com/backend",BASE_URL:"/",MODE:"online",DEV:!1,PROD:!0,SSR:!1}[`VITE_APP_${i.game_project.toUpperCase()}_GM_URL`]})),e.game_infos.length){const i=localStorage.getItem("crtGame"),b=e.game_infos.filter(le=>le.game_project===i)[0]||e.game_infos[0];e.crt_game=b,localStorage.setItem("crtGame",b.game_project),c("success")}else localStorage.removeItem("ticket"),p("no game infos")}).catch(()=>{p(Error("权限请求失败"))})})}}),gt={[C.PLATFORM_PROPS]:"/api/items/import",[C.MULTILINGUAL]:"/api/lang/upload",[C.DAILY_CHECK]:"/api/active_checkin/update_reward",[C.LADDERCONFIGS]:"/api/vip-right-config/upload-ladder-configs",[C.RIGHTGIFT]:"/api/member-gift/import",[C.TASKCONFIG]:"/api/task-config/import",[C.PRODUCTCONFIG]:"/api/points-mall-product/import",[C.TASKRULE]:"/api/task-config/upload-rule-desc",[C.GIFTCONFIG]:"/api/gift-package/import"},un=(e,t)=>u.post(gt[e],t),pn=e=>u.get("/api/resource/log/list",e),ht=()=>u.get("/api/common/public-enums"),_n=e=>u.downfile("/api/common/downloadTemplate",{file_type:e}),dn=M("config",()=>{const e=de({langs:[],channels:[],platform:[],show_pages:[],prize_type:[],game_projects:[],game_channels:[],task_events:[]});return{configState:e,getConfItem:(n,l)=>e.value[n].find(a=>a.value===l),FETCH_GLOBAL_CONFIG:async(n=!1)=>{!n&&e.value.langs.length||ht().then(l=>{e.value=l})},getGameChannel:n=>{var l;return((l=e.value.game_channels.find(a=>a.game_project===n))==null?void 0:l.sdk_pkg_channels)||[]}}}),mn=me(),ft={$on:(...e)=>A.on(...e),$off:(...e)=>A.off(...e),$once:(...e)=>A.once(...e),$emit:(...e)=>A.emit(...e)},gn=e=>u.get("/api/lang",e),hn=()=>u.downfile("/api/lang/export"),fn=e=>u.get("/api/lang/keySearch",e),vn=e=>u.get("/api/lang/keyDetail",e),bn=e=>u.post("/api/common/upload-img",e),vt=e=>u.get("/api/resource/img/list",e),yn=e=>u.get("/api/resource/img/first",e),bt=e=>u.post("/api/resource/img/upload_check",e),yt=e=>u.post("/api/resource/img/upload_batch",e),Cn=e=>u.get("/api/gift-package",e),kn=()=>u.get("/api/gift-package/enum"),Pn=e=>u.post("/api/gift-package",e),En=(e,t)=>u.put(`/api/gift-package/${e}`,t),On=e=>u.get(`/api/gift-package/${e}`),Ln=({id:e})=>u.delete(`/api/gift-package/${e}`),Sn=()=>u.downfile("/api/gift-package/export"),In=e=>u.get("/api/item-i18n",e),Rn=({search_key:e,type:t})=>u.get("/api/item-i18n/search",{search_key:e,type:t}),Tn=e=>u.get("/api/item-i18n",e),Dn=e=>u.post("/api/item-i18n",e),$n=(e,t)=>u.put(`/api/item-i18n/${e}`,t),An=e=>u.get(`/api/item-i18n/${e}`),jn=({id:e})=>u.delete(`/api/item-i18n/${e}`),Ct=L({name:"List",emits:["submit","close"],setup(e,t){const o=U("setStep"),s=I({searchParams:{search:"",page:1,page_size:20},crtItem:{},loading:!1,list:[],total:0,imgTypes:[]}),n=()=>{s.searchParams.page=1,l()},l=()=>{const _={...s.searchParams};s.loading=!0,vt(_).then(i=>{s.list=i.data,s.searchParams.page=i.current_page,s.total=i.total}).finally(()=>{s.loading=!1})},a=_=>{s.searchParams.page=_,l()},c=_=>{s.crtItem=_};ge(()=>{l()});const p=()=>{if(!s.crtItem.id)return y.error("请选择一张图片！");t.emit("submit",s.crtItem)},d=()=>t.emit("close");return{...x(s),search:n,setCrt:c,handleCurrentChange:a,setStep:o,close:d,confirm:p}}});const kt=e=>(z("data-v-644e8c9d"),e=e(),N(),e),Pt={class:"img-list"},Et=kt(()=>r("div",{class:"left"}," asd ",-1)),Ot={class:"right"},Lt={class:"filter"},St={class:"result"},It={class:"list"},Rt=["onClick"],Tt={class:"img"},Dt=["src"],$t={class:"name"},At={class:"pre"},jt={class:"last"},wt={class:"pagination-wrap"},Ft={class:"footer"};function Gt(e,t,o,s,n,l){const a=f("a-divider"),c=f("a-input-search"),p=f("a-button"),d=f("a-pagination"),_=f("a-space");return v(),E(j,null,[r("div",Pt,[e.imgTypes.length>1?(v(),E(j,{key:0},[Et,m(a,{type:"vertical",style:{height:"100%"}})],64)):$("",!0),r("div",Ot,[r("div",Lt,[m(c,{value:e.searchParams.search,"onUpdate:value":t[0]||(t[0]=i=>e.searchParams.search=i),placeholder:"请输入图片名称",style:{width:"300px"},"enter-button":"查询",allowClear:"",onSearch:e.search},null,8,["value","onSearch"]),m(p,{onClick:t[1]||(t[1]=i=>e.setStep(2))},{default:h(()=>[k("上传图片")]),_:1})]),r("div",St,[r("div",It,[(v(!0),E(j,null,he(e.list,i=>(v(),E("div",{class:te(["img-item",{active:e.crtItem.id===i.id}]),key:i.id,onClick:b=>e.setCrt(i)},[r("div",Tt,[r("img",{src:i.preview_img},null,8,Dt)]),r("div",$t,[r("div",At,G(i.img_key.length>5?i.img_key.substring(0,i.img_key.length-2):i.img_key),1),r("div",jt,G(i.img_key.length>5&&i.img_key.substring(i.img_key.length-2)||""),1)])],10,Rt))),128))]),r("div",wt,[m(d,{current:e.searchParams.page,"onUpdate:current":t[2]||(t[2]=i=>e.searchParams.page=i),"default-page-size":e.searchParams.page_size,total:e.total,onCurrentChange:e.handleCurrentChange},null,8,["current","default-page-size","total","onCurrentChange"])])])])]),r("div",Ft,[m(_,{wrap:""},{default:h(()=>[m(p,{onClick:e.close},{default:h(()=>[k("取消")]),_:1},8,["onClick"]),m(p,{type:"primary",onClick:e.confirm},{default:h(()=>[k("确定")]),_:1},8,["onClick"])]),_:1})])],64)}const Mt=R(Ct,[["render",Gt],["__scopeId","data-v-644e8c9d"]]),Ut=L({name:"Upload",emits:["finish"],setup(e,t){const o=U("setStep"),s=U("isOnlyUpload"),n=I({list:[],loading:!1}),l=(d,_)=>!1,a=()=>{const d={};for(const _ in n.list){const i=n.list[_];if(i.size&&i.size>1024*1024*2)return`文件(${i.name})大小超过大小限制`;if(!X(i.name.split(".")[0]))return`文件(${i.name})不符合命名规则, 文件名只包含数字、字母、下划线`;if(d[i.name])return`文件(${i.name})重复选择`;d[i.name]=!0}return""},c=async()=>{if(a())return y.error(a());const d=new FormData;K(n.list).map(_=>{d.append("file[]",_.originFileObj)});try{await bt(d),p()}catch(_){_.errCode===50&&_.data.exist.length}},p=async()=>{const d=new FormData;K(n.list).map(_=>{d.append("file[]",_.originFileObj)}),n.loading=!0;try{await yt(d),n.list=[],y.success("操作成功"),t.emit("finish")}catch{}n.loading=!1};return{...x(n),isOnlyUpload:s,imgChange:l,setStep:o,preCheck:c,checkValueLNL:X}}});const re=e=>(z("data-v-e3f620ea"),e=e(),N(),e),Bt={key:0},Vt=re(()=>r("div",{style:{"margin-top":"8px"}},"选择图片",-1)),zt={class:"img-item"},Nt={class:"image"},xt=["src"],Ht=["title"],Kt={class:"pre"},qt={class:"last"},Zt=re(()=>r("div",{class:"tips"},"单次最多上传 10 张图片, 且每张图片不可大于 2M, 文件名必须是字母、数字和下划线!",-1)),Wt={class:"footer"};function Jt(e,t,o,s,n,l){const a=f("plus-outlined"),c=f("CloseCircleOutlined"),p=f("a-upload"),d=f("a-button"),_=f("a-space");return v(),E(j,null,[m(p,{class:"upload-wrap",ref:"update",action:"#","list-type":"picture-card","file-list":e.list,"onUpdate:fileList":t[0]||(t[0]=i=>e.list=i),"before-upload":e.imgChange,"max-count":10,accept:"image/gif,image/jpeg,image/jpg,image/png,image/svg",multiple:!0},{previewIcon:h(()=>[]),itemRender:h(({file:i,actions:{remove:b}})=>[r("div",zt,[r("div",Nt,[r("img",{src:i.thumbUrl},null,8,xt)]),r("div",{class:te(["title",{error:!e.checkValueLNL(i.name.split(".")[0])}]),title:i.name},[r("div",Kt,G(i.name.length>10?i.name.substring(0,i.name.length-6):i.name),1),r("div",qt,G(i.name.length>10&&i.name.substring(i.name.length-6)||""),1)],10,Ht),m(c,{class:"del-icon",onClick:b},null,8,["onClick"])])]),tip:h(()=>[Zt]),default:h(()=>[e.list.length<10?(v(),E("div",Bt,[m(a),Vt])):$("",!0)]),_:1},8,["file-list","before-upload"]),r("div",Wt,[m(_,{wrap:""},{default:h(()=>[e.isOnlyUpload?$("",!0):(v(),O(d,{key:0,onClick:t[1]||(t[1]=i=>e.setStep(1))},{default:h(()=>[k("返回")]),_:1})),m(d,{type:"primary",onClick:e.preCheck,loading:e.loading,disabled:e.list.length===0},{default:h(()=>[k("确定")]),_:1},8,["onClick","loading","disabled"])]),_:1})])],64)}const Qt=R(Ut,[["render",Jt],["__scopeId","data-v-e3f620ea"]]),Xt=L({__name:"Index",setup(e){let t;const o=I({visible:!1,step:1,isOnlyUpload:!1}),s=()=>{o.visible=!1},n=c=>{o.step=c};q("isOnlyUpload",o.isOnlyUpload),q("setStep",n);const l=c=>{t&&t(c),s()},a=()=>{o.step===2?o.step=1:(t&&t(),s())};return ft.$on("showPhotoGallery",c=>{t=c.callback,o.isOnlyUpload=c.isOnlyUpload||o.isOnlyUpload,o.visible=!0}),(c,p)=>{const d=f("a-modal");return v(),O(d,{open:P(o).visible,"onUpdate:open":p[0]||(p[0]=_=>P(o).visible=_),title:P(o).step===1?"图片库":"上传图片",width:"923px","destroy-on-close":"","wrap-class-name":"fp-mall-admin",footer:null},{default:h(()=>[!P(o).isOnlyUpload&&P(o).step===1?(v(),O(Mt,{key:0,onSubmit:l,onClose:s})):$("",!0),P(o).isOnlyUpload||P(o).step===2?(v(),O(Qt,{key:1,ref:"upload",onFinish:a},null,512)):$("",!0)]),_:1},8,["open","title"])}}}),wn=Object.freeze(Object.defineProperty({__proto__:null,default:Xt},Symbol.toStringTag,{value:"Module"})),Yt=L({name:"SelectWithAll",props:{value:{type:String,default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"请选择"}},emits:["update:value","change"],setup(e,t){const o=Z({get:()=>{if(e.value)return e.value==="all"?["all"]:e.value.split("|").filter(n=>n)},set:n=>{let l;!n||n.length===0?l=void 0:n[0]==="all"?l="all":l=`|${n.join("|")}|`,t.emit("update:value",l),t.emit("change",l)}}),s=Z(()=>{const n=e.options.map(a=>typeof a=="string"?{label:a,value:a}:a),l=[{label:"ALL",value:"all"}];return e.value==="all"?l:e.value?n:[...l,...n]});return{...x(e),modelValue:o,computedOptions:s}}});function en(e,t,o,s,n,l){const a=f("a-select");return v(),O(a,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=c=>e.modelValue=c),options:e.computedOptions,placeholder:e.placeholder,mode:"multiple","allow-clear":""},null,8,["value","options","placeholder"])}const tn=R(Yt,[["render",en]]),Fn=Object.freeze(Object.defineProperty({__proto__:null,default:tn},Symbol.toStringTag,{value:"Module"}));export{an as A,sn as B,un as C,wn as D,ft as E,yn as F,bn as G,fn as H,Fn as I,ut as J,rn as K,gn as L,mn as M,C as P,tn as S,Xt as _,hn as a,Rn as b,On as c,_n as d,En as e,Pn as f,kn as g,Sn as h,Cn as i,Ln as j,In as k,vn as l,ln as m,An as n,$n as o,Dn as p,Tn as q,u as r,cn as s,jn as t,dn as u,ie as v,S as w,ae as x,pn as y,ve as z};
