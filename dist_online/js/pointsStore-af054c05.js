import{y as O,z as de,A as te,L as ae,K as _e,B as re,P as ve,m as me}from"./common-b8130258.js";import{d as le,f as U,g as ie,r as G,h as Z,x as ne,y as ce,a as _,o as c,c as L,w as n,b as l,u as t,e as m,l as g,i as fe,z as se,A as oe,v as B,_ as ue,n as ye,k as I,p as ge,F as C,t as D}from"./vendor-cc06403f.js";import{M as $}from"./antd-914dcf46.js";const ke=r=>O.get("/api/points-mall-product",r),be=r=>O.post("/api/points-mall-product",r),ee=(r,b)=>O.put(`/api/points-mall-product/${r}`,b),we=r=>O.get(`/api/points-mall-product/${r}`),xe=({petId:r})=>O.delete(`/api/points-mall-product/${r}`),he=(r,b)=>O.put(`/api/points-mall-product/set-status/${r}`,{status:b}),Ie=()=>O.downfile("/api/points-mall-product/export"),Ce=r=>O.put(`/api/points-mall-product/adjust-stock/${r.id}`,r),De=r=>(se("data-v-39b78cac"),r=r(),oe(),r),Le=De(()=>B("span",{style:{margin:"0 10px"}},"-",-1)),Te=le({__name:"Form",props:["editId"],emits:["close","refresh"],setup(r,{emit:b}){const d=r;U.extend(ie);const{configState:P}=de(),M=G(!0),T=G(!1),E=()=>{T.value=!0,we(d.editId).then(s=>{a.value=s,a.value.gift_id=Number(s.gift_id),a.value.item_limit_type=s.item_limit_type||1,a.value.stocks_num=s.stocks_num||1,a.value.online_timing_str=s.online_timing?U.utc(s.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss"):"",s.is_time_limit===1&&s.start_time&&s.end_time?a.value.times=[s.start_time,s.end_time]:a.value.times=void 0}).finally(()=>T.value=!1)};d.editId&&E();const a=G({id:0,cycle_type:1,cycle_shape:1,cycle_times:1,cost_coin:0,vip:1,vip_max:1,game_growth:0,role_growth:0,gift_id:null,status:0,is_time_limit:0,times:void 0,use_stocks:0,item_limit_type:1,stocks_num:1,use_desc_key:void 0,online_type:1,online_timing:0,online_timing_str:"",product_type:1,is_filter:0,f_s_ids:""}),y=Z({uploadLoading:!1,optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"R1",value:1},{label:"R2",value:2},{label:"R3",value:3},{label:"R4",value:4},{label:"R5",value:5},{label:"R6",value:6},{label:"R7",value:7}],jumpType:[{label:"跳转到特定页面",value:0},{label:"打开游戏",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:1},{label:"按角色",value:2}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5,disabled:!0},{label:"终身领取",value:6}],progressList:[{label:"每次需完成1个行为",value:1},{label:"每次需完成2个行为",value:2},{label:"每次需完成3个行为",value:3},{label:"每次需完成4个行为",value:4},{label:"每次需完成5个行为",value:5},{label:"每次需完成6个行为",value:6},{label:"每次需完成7个行为",value:7},{label:"每次需完成8个行为",value:8},{label:"每次需完成9个行为",value:9},{label:"每次需完成10个行为",value:10}],rewardType:[{label:"平台道具奖励",value:0},{label:"游戏道具奖励",value:1},{label:"积分",value:2}],optionsGifts:[],optionsLang:[],optionsLangKey:[],optionsOriginTaskEvent:[],optionsTaskEvent:[],stocksTypeList:[{label:"每日兑换上限",value:1},{label:"每周兑换上限",value:2},{label:"每月兑换上限",value:3},{label:"每年兑换上限",value:4},{label:"总兑换上限",value:5}],productTypeList:[{label:"游戏",value:1},{label:"平台",value:2}],giftsList:[]}),K=Z({filterData:{task_where:[{required:!0,message:"请选择任务条件",trigger:"change"}]}}),w=G(!1),V=G(),J=s=>s&&s<U().startOf("day");ne(()=>{v(),u({page:1,page_size:1e3})}),ce(()=>P,s=>{s&&s.task_events&&s.task_events.length>0&&(y.optionsTaskEvent=s.task_events.map(e=>({originLabel:e.label,label:e.label,value:e.value})))},{immediate:!0});const Q=s=>{a.value.gift_id=null,y.optionsGifts=y.giftsList.filter(e=>s===1?e.game_project!=="funplus_zone":e.game_project==="funplus_zone")},v=()=>{te().then(s=>{s&&s.length>0&&(y.giftsList=s,y.optionsGifts=s.filter(e=>a.value.product_type===1?e.game_project!=="funplus_zone":e.game_project==="funplus_zone"))})},u=s=>{ae(s).then(e=>{e.data&&e.data.length>0&&(y.optionsLang=e.data.map(k=>({label:k.zh_cn,value:k.key})),y.optionsLangKey=[...y.optionsLang])})},h=s=>{y.optionsTwo.find(e=>e.value===s)},R=(s,e)=>e.label.toLowerCase().indexOf(s.toLowerCase())>=0,q=()=>{V.value.validate().then(()=>{w.value=!0;const{id:s,...e}=a.value;if(e.game_growth=e.game_growth||0,e.vip_max&&e.vip_max<e.vip)return w.value=!1,$.error({title:"提示",content:"等级上限不能小于等级下限"});const k=y.optionsGifts.find(f=>f.id===e.gift_id);if(k&&k.item_list_length>1)return w.value=!1,$.error({title:"提示",content:"所选礼包内包含多种道具/积分，请重新选择"});if(e.cost_coin===0)return w.value=!1,$.error({title:"提示",content:"兑换所需积分不能为0"});if(e.is_time_limit===1&&e.times?(e.start_time=e.times[0],e.end_time=e.times[1]):(e.start_time=0,e.end_time=0),e.online_type===2?e.online_timing=e.online_timing_str?U.utc(e.online_timing_str).unix():0:e.online_timing=0,e.product_type===2&&!e.use_desc_key)return w.value=!1,$.error({title:"提示",content:"请选择使用说明"});d.editId?a.value.status?$.confirm({title:"提示",content:"当前商品已上线，保存后立即生效，请确认后操作",okText:"确定",cancelText:"取消",onOk:()=>{ee(s,e).then(()=>{b("close"),b("refresh",!0)}).catch(()=>{}).finally(()=>{w.value=!1})},onCancel:()=>{w.value=!1}}):ee(s,e).then(()=>{b("close"),b("refresh",!0)}).catch(()=>{}).finally(()=>{w.value=!1}):be(e).then(()=>{b("close"),b("refresh")}).catch(()=>{}).finally(()=>{w.value=!1}),setTimeout(()=>{w.value=!1},1e3)}).catch(()=>{})};return(s,e)=>{const k=_("a-select"),f=_("a-form-item"),Y=_("a-input-number"),H=_("a-space"),S=_("a-radio-group"),W=_("a-date-picker"),X=_("SelectDateTime"),F=_("a-radio"),o=_("a-textarea"),p=_("SelectLang"),z=_("a-button"),j=_("a-form"),A=_("a-spin"),N=_("a-drawer");return c(),L(N,{open:t(M),"onUpdate:open":e[22]||(e[22]=i=>fe(M)?M.value=i:null),title:d.editId?"编辑商品":"新增商品",maskClosable:!1,width:800,onAfterOpenChange:e[23]||(e[23]=i=>!i&&b("close"))},{default:n(()=>[l(A,{spinning:t(T)},{default:n(()=>[l(j,{model:t(a),rules:t(K),name:"basic",ref_key:"formRef",ref:V,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:n(()=>[l(f,{label:"商品类型",name:"product_type",rules:[{required:!0,message:"请选择商品类型"}]},{default:n(()=>[l(k,{style:{width:"100%"},value:t(a).product_type,"onUpdate:value":e[0]||(e[0]=i=>t(a).product_type=i),onChange:Q,options:t(y).productTypeList},null,8,["value","options"])]),_:1}),l(f,{label:"商品名称",name:"gift_id",rules:[{required:!0,message:"请选择商品名称"}]},{default:n(()=>[l(k,{style:{width:"100%"},allowClear:"","show-search":"","filter-option":R,value:t(a).gift_id,"onUpdate:value":e[1]||(e[1]=i=>t(a).gift_id=i),options:t(y).optionsGifts,placeholder:"请选择商品名称",onChange:h},null,8,["value","options"])]),_:1}),l(f,{label:"兑换所需积分",name:"cost_coin",rules:[{required:!0,message:"请填写兑换所需积分"}]},{default:n(()=>[l(Y,{style:{width:"100%"},value:t(a).cost_coin,"onUpdate:value":e[2]||(e[2]=i=>t(a).cost_coin=i),min:1,max:15e5,placeholder:"请填写兑换所需积分"},null,8,["value"])]),_:1}),l(f,{label:"等级限制",name:"vip",rules:[{required:!0,message:"请选择等级限制"}]},{default:n(()=>[l(H,{style:{width:"100%"}},{default:n(()=>[l(k,{style:{width:"100px"},value:t(a).vip,"onUpdate:value":e[3]||(e[3]=i=>t(a).vip=i),options:t(y).optionsVip},null,8,["value","options"]),Le,l(k,{style:{width:"100px"},value:t(a).vip_max,"onUpdate:value":e[4]||(e[4]=i=>t(a).vip_max=i),options:t(y).optionsVip},null,8,["value","options"])]),_:1})]),_:1}),l(f,{label:"游戏成长值",name:"game_growth"},{default:n(()=>[l(Y,{style:{width:"100%"},value:t(a).game_growth,"onUpdate:value":e[5]||(e[5]=i=>t(a).game_growth=i),placeholder:"请输入游戏成长值"},null,8,["value"])]),_:1}),l(f,{label:"领取维度及周期",name:"cycle_times",rules:[{required:!0,message:"请填写领取维度及周期"}]},{default:n(()=>[l(H,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:n(()=>[l(k,{style:{width:"100%"},value:t(a).cycle_shape,"onUpdate:value":e[6]||(e[6]=i=>t(a).cycle_shape=i),options:t(y).trigger1},null,8,["value","options"]),l(k,{style:{width:"100%"},value:t(a).cycle_type,"onUpdate:value":e[7]||(e[7]=i=>t(a).cycle_type=i),options:t(y).trigger2},null,8,["value","options"]),l(Y,{style:{width:"100%"},value:t(a).cycle_times,"onUpdate:value":e[8]||(e[8]=i=>t(a).cycle_times=i),min:1,max:9999,placeholder:"请填写次数"},null,8,["value"]),m("次 ")]),_:1})]),_:1}),l(f,{label:"商品上线时间",onChange:e[10]||(e[10]=i=>t(a).times=void 0),rules:[{required:!0,message:"请选择起止时间"}]},{default:n(()=>[l(S,{value:t(a).online_type,"onUpdate:value":e[9]||(e[9]=i=>t(a).online_type=i),options:[{label:"立即上线",value:1},{label:"定时上线",value:2}]},null,8,["value"])]),_:1}),t(a).online_type===2?(c(),L(f,{key:0,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"online_timing_str",rules:[{required:!0,message:"请选择结束时间"}]},{default:n(()=>[l(W,{style:{width:"100%"},value:t(a).online_timing_str,"onUpdate:value":e[11]||(e[11]=i=>t(a).online_timing_str=i),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":J,"show-now":!1,"show-time":""},null,8,["value"])]),_:1})):g("",!0),l(f,{label:"商品有效期",onChange:e[13]||(e[13]=i=>t(a).times=void 0),rules:[{required:!0,message:"请选择起止时间"}]},{default:n(()=>[l(S,{value:t(a).is_time_limit,"onUpdate:value":e[12]||(e[12]=i=>t(a).is_time_limit=i),options:[{label:"永久",value:0},{label:"定时",value:1}]},null,8,["value"])]),_:1}),t(a).is_time_limit===1?(c(),L(f,{key:1,class:"time-limit-item","wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:n(()=>[l(X,{value:t(a).times,"onUpdate:value":e[14]||(e[14]=i=>t(a).times=i)},null,8,["value"])]),_:1})):g("",!0),l(f,{label:"是否限量兑换",rules:[{required:!0,message:"请选择是否限量兑换"}]},{default:n(()=>[l(S,{value:t(a).use_stocks,"onUpdate:value":e[15]||(e[15]=i=>t(a).use_stocks=i),options:[{label:"否",value:0},{label:"是",value:1}]},null,8,["value"])]),_:1}),t(a).use_stocks===1?(c(),L(f,{key:2,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"item_limit_type",rules:[{required:!0,message:"请选择兑换上限类型"}]},{default:n(()=>[l(k,{style:{width:"50%"},value:t(a).item_limit_type,"onUpdate:value":e[16]||(e[16]=i=>t(a).item_limit_type=i),options:t(y).stocksTypeList},null,8,["value","options"]),l(Y,{value:t(a).stocks_num,"onUpdate:value":e[17]||(e[17]=i=>t(a).stocks_num=i),min:1,max:9999,placeholder:"请填写兑换上限"},null,8,["value"])]),_:1})):g("",!0),l(f,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:n(()=>[l(S,{value:t(a).is_filter,"onUpdate:value":e[18]||(e[18]=i=>t(a).is_filter=i),onChange:e[19]||(e[19]=i=>t(_e)(t(a),["f_s_ids"]))},{default:n(()=>[l(F,{value:1},{default:n(()=>[m("开启")]),_:1}),l(F,{value:0},{default:n(()=>[m("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),t(a).is_filter===1?(c(),L(f,{key:3,label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:n(()=>[l(o,{value:t(a).f_s_ids,"onUpdate:value":e[20]||(e[20]=i=>t(a).f_s_ids=i),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1})):g("",!0),l(f,{label:"使用说明",name:"use_desc_key"},{default:n(()=>[l(p,{value:t(a).use_desc_key,"onUpdate:value":e[21]||(e[21]=i=>t(a).use_desc_key=i)},null,8,["value"])]),_:1}),l(f,{"wrapper-col":{offset:10,span:12}},{default:n(()=>[l(z,{type:"primary",onClick:q,loading:t(w)},{default:n(()=>[m("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const Ue=ue(Te,[["__scopeId","data-v-39b78cac"]]),pe=r=>(se("data-v-5ce9a7a3"),r=r(),oe(),r),Ye=pe(()=>B("div",{class:"preview-desc"},"库存调整后立即生效，请确认修改后保存",-1)),Oe=pe(()=>B("strong",null,"→",-1)),Ge=le({__name:"Index",setup(r){U.extend(ie);const b=[{dataIndex:"id",key:"id",title:"商品ID",width:"100px"},{dataIndex:"item_list",key:"item_list",title:"道具详情",width:"130px"},{dataIndex:"cost_coin",key:"cost_coin",title:"兑换积分",width:"130px"},{dataIndex:"product_type",key:"product_type",title:"商品类型",width:"130px"},{dataIndex:"vip",key:"vip",title:"等级限制",width:"130px"},{dataIndex:"game_growth",key:"game_growth",title:"游戏成长值",width:"130px"},{dataIndex:"cycle_shape",key:"cycle_shape",title:"领取维度",width:"130px"},{dataIndex:"cycle_type",key:"cycle_type",title:"周期",width:"130px"},{dataIndex:"cycle_times",key:"cycle_times",title:"周期内次数",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"online_type",key:"online_type",title:"上线时间",width:"130px"},{dataIndex:"is_time_limit",key:"is_time_limit",title:"有效期",width:"130px"},{dataIndex:"stocks_num",key:"stocks_num",title:"兑换上限",width:"100px"},{dataIndex:"now_stocks",key:"now_stocks",title:"实时库存",width:"100px"},{dataIndex:"action",key:"action",title:"操作",width:"200px",fixed:"right",align:"center"}],d=Z({editVisible:!1,editId:0,searchParams:{task_category:null,vip_restriction:null},previewOpen:!1,previewData:{},optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4}],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],trigger1:[{label:"账号",value:1},{label:"角色",value:2}],optionsGifts:[],optionsLang:[]}),P=G([]),M=G(),T=v=>M.value.requestTableData(!v);ne(()=>{E(),a({page:1,page_size:1e3}),y("task_desc")});const E=()=>{te().then(v=>{v&&v.length>0&&(d.optionsGifts=v)})},a=v=>{ae(v).then(u=>{u.data&&u.data.length>0&&(d.optionsLang=u.data.map(h=>({label:h.zh_cn,value:h.key})))})},y=async v=>{const u=await re({key:v});return(u==null?void 0:u.zh_cn)||"-"},K=v=>{d.previewData=v,d.previewOpen=!0},w=()=>{const{id:v,change_stocks:u}=d.previewData;if(u<0)return $.error({title:"提示",content:"所选礼包内包含多种道具/积分，请重新选择"});Ce({id:v,now_stocks:u}).then(()=>{d.previewOpen=!1,T(!0)})},V=(v,u)=>{d.editVisible=v,d.editId=u||0},J=v=>me("删除后，用户将看不到该商品，请确认后删除",xe,{petId:v}).then(()=>T()),Q=(v,u,h)=>{P.value[h]=!0,u.status=1-v;const R=v===1?"当前商品未上线，是否确认现在上线":"当前商品已上线，是否确认现在下线？";$.confirm({title:"提示",content:R,okText:"确定",cancelText:"取消",onOk:()=>{P.value[h]=!1,he(u.id,v).finally(()=>T(!0))},onCancel:()=>{P.value[h]=!1}})};return(v,u)=>{const h=_("UploadBtn"),R=_("PlusOutlined"),q=_("a-button"),s=_("a-space"),e=_("a-switch"),k=_("a-tooltip"),f=_("a-divider"),Y=_("a-typography-link"),H=_("CustomTable"),S=_("a-input-number"),W=_("a-form-item"),X=_("a-modal"),F=ye("has");return c(),I(C,null,[l(H,{ref_key:"RefCustomTable",ref:M,"data-api":t(ke),params:t(d).searchParams,columns:b},{top:n(()=>[l(s,{direction:"vertical"},{default:n(()=>[l(s,{wrap:"",style:{padding:"20px 0",gap:"20px"}},{default:n(()=>[ge(l(h,{ref:"uploadBtn",onUploadSuccess:T,downloadApi:t(Ie),fileType:"points-products",page:t(ve).PRODUCTCONFIG},null,8,["downloadApi","page"]),[[F,"Operation"]]),l(q,{type:"primary",onClick:u[0]||(u[0]=o=>V(!0))},{icon:n(()=>[l(R)]),default:n(()=>[m(" 新增商品 ")]),_:1})]),_:1})]),_:1})]),bodyCell:n(({column:o,record:p,index:z})=>{var j,A,N,i;return[o.key==="package"?(c(),I(C,{key:0},[m(D((j=p[o.key])==null?void 0:j.name),1)],64)):g("",!0),o.key==="item_list"?(c(),I(C,{key:1},[m(D(p[o.key]&&p[o.key].length>0?p[o.key][0].name+"*"+p[o.key][0].num:""),1)],64)):g("",!0),o.key==="vip"?(c(),I(C,{key:2},[m(" R"+D(p.vip)+" - R"+D(p.vip_max),1)],64)):g("",!0),o.dataIndex==="product_type"?(c(),I(C,{key:3},[m(D(p[o.key]!==1?"平台":"游戏"),1)],64)):g("",!0),o.dataIndex==="cycle_shape"?(c(),I(C,{key:4},[m(D((A=t(d).trigger1.find(x=>x.value===p[o.key]))==null?void 0:A.label),1)],64)):g("",!0),o.dataIndex==="cycle_type"?(c(),I(C,{key:5},[m(D((N=t(d).optionsGift1.find(x=>x.value===p[o.key]))==null?void 0:N.label),1)],64)):g("",!0),o.dataIndex==="progress"?(c(),I(C,{key:6},[m(D(p[o.key]?p[o.key]:"无限制"),1)],64)):g("",!0),o.dataIndex==="pkg_id"?(c(),I(C,{key:7},[m(D((i=t(d).optionsGifts.find(x=>x.value==p[o.key]))==null?void 0:i.label),1)],64)):g("",!0),o.key==="status"?(c(),L(e,{key:8,checked:p.status,"onUpdate:checked":x=>p.status=x,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:t(P)[z],onClick:x=>Q(x,p,z)},null,8,["checked","onUpdate:checked","loading","onClick"])):g("",!0),o.dataIndex==="online_type"&&p[o.key]===2?(c(),L(k,{key:9,title:`${t(U).utc(p.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss")}`,placement:"topLeft"},{default:n(()=>[m(D(t(U).utc(p.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss")),1)]),_:2},1032,["title"])):g("",!0),o.dataIndex==="online_type"&&p[o.key]===1?(c(),I(C,{key:10},[m("立即上线")],64)):g("",!0),o.dataIndex==="is_time_limit"&&p[o.key]===1?(c(),L(k,{key:11,title:`${t(U).utc(p.start_time*1e3).format("YYYY-MM-DD HH:mm:ss")} -
        ${t(U).utc(p.end_time*1e3).format("YYYY-MM-DD HH:mm:ss")}`,placement:"topLeft"},{default:n(()=>[m(" 定时 ")]),_:2},1032,["title"])):g("",!0),o.dataIndex==="is_time_limit"&&p[o.key]===0?(c(),I(C,{key:12},[m(" 永久 ")],64)):g("",!0),o.key==="action"?(c(),L(s,{key:13},{split:n(()=>[l(f,{type:"vertical",style:{margin:"0"}})]),default:n(()=>[l(Y,{disabled:!p.stocks_num,onClick:x=>K(p)},{default:n(()=>[m("库存调整")]),_:2},1032,["disabled","onClick"]),l(Y,{onClick:x=>V(!0,p.id)},{default:n(()=>[m("编辑")]),_:2},1032,["onClick"]),l(Y,{type:"danger",danger:"",onClick:x=>J(p.id)},{default:n(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)):g("",!0)]}),_:1},8,["data-api","params"]),t(d).editVisible?(c(),L(Ue,{key:0,"edit-id":t(d).editId,onClose:u[1]||(u[1]=o=>V(!1)),onRefresh:T},null,8,["edit-id"])):g("",!0),l(X,{class:"preview-modal",visible:t(d).previewOpen,"onUpdate:visible":u[4]||(u[4]=o=>t(d).previewOpen=o),title:"库存调整",onCancel:u[5]||(u[5]=o=>t(d).previewOpen=!1),onOk:u[6]||(u[6]=o=>t(d).previewOpen=!1)},{footer:n(()=>[l(q,{key:"back",onClick:u[3]||(u[3]=o=>t(d).previewOpen=!1)},{default:n(()=>[m(" 取消 ")]),_:1}),l(q,{key:"submit",type:"primary",onClick:w},{default:n(()=>[m(" 确认 ")]),_:1})]),default:n(()=>[Ye,l(W,{label:"实时库存",class:"preview-item"},{default:n(()=>[B("strong",null,D(t(d).previewData.now_stocks),1),Oe,l(S,{value:t(d).previewData.change_stocks,"onUpdate:value":u[2]||(u[2]=o=>t(d).previewData.change_stocks=o),min:0,max:t(d).previewData.stocks_num},null,8,["value","max"])]),_:1})]),_:1},8,["visible"])],64)}}});const Ve=ue(Ge,[["__scopeId","data-v-5ce9a7a3"]]);export{Ve as default};
