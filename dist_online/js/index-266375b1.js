import{d as x,bv as Q,bz as U,H as V,b as i,u as g,e as u,f as h,g as l,h as d,n as j,_ as M,a as N,w as W,a7 as ge,j as v,a6 as _e,l as y,F as T,m as G,t as $,bE as Oe,r as A,bC as he,k as w,q as ve,s as ye,bu as $e,p as Ie,bD as oe,Y as xe,v as H,x as be,bF as De,bG as Fe,bH as Le,bI as Te,bJ as ze,bK as je,bb as Me,bL as Pe,ai as Ee,ab as He,an as Ue,bM as Re,bN as Be,bO as Ae,bP as Ke,bQ as Ve,bn as Ge,ag as Ne,bo as qe,bR as Ye,bk as We,ap as Je,ak as Qe,ao as Xe,bS as Ze,aS as et,bT as tt,bU as ot,bV as nt,be as at,bW as lt,bX as st,bY as it,bZ as rt,b_ as ct,b$ as ut,c0 as dt,ac as pt,c1 as _t,c2 as mt,c3 as ft,c4 as gt,c5 as ht,c6 as vt,aE as yt,c7 as bt,c8 as wt,aH as St,c9 as kt,ca as Ct,af as Ot,cb as $t,cc as It,cd as xt,ce as Dt,cf as Ft,cg as Lt,ch as Tt,ci as zt,cj as jt,ck as Mt,cl as Y,aL as Pt,o as Et,cm as Ht,cn as q,co as Ut,a2 as re,cp as Rt,cq as Bt,J as At,L as Kt,cr as Vt,i as ce,a5 as ue,a9 as J,bj as Gt,cs as Nt,W as qt}from"./vendor-6ece677a.js";import{m as Z,z as Yt,A as Wt,i as Jt,a as Qt,B as Xt,b as Zt,c as eo,C as to,d as oo,e as no,f as ao,g as lo,h as so,D as io,j as ro,k as co,l as uo,n as po,o as _o,E as mo,p as fo,F as de,I as go,q as ho,r as vo,s as yo,t as bo,M as wo,P as So,u as ko,R as Co,G as Oo,v as $o,w as Io,x as xo,V as Do,S as Fo,y as Lo,H as To,J as zo,K as jo,T as Mo,L as Po,N as Eo,O as Ho,Q as Uo,U as Ro,W as Bo,X as Ao,Y as Ko}from"./antd-79d05377.js";import{v as ee,w as K,x as ne,y as Vo,z as we,_ as Go,u as pe,B as No,C as qo,d as Yo,D as Wo,E as Jo,F as Qo,G as Xo,H as Zo,l as Se,S as en,I as tn,J as on,K as nn,M as an}from"./common-9638d99e.js";const ln={components:{Button:{paddingContentHorizontal:10},Table:{colorBorderSecondary:"#e8e8e8",colorFillAlter:"#f7f7f7"},Menu:{colorItemBgHover:"rgba(255, 255, 255, .1)"},Breadcrumb:{colorBgTextHover:"transparent"}},token:{fontFamily:'"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif'}},sn="/png/fp-logo-1690b683.png",rn=x({__name:"Help",setup(e){const t=Q(),o=()=>{window.open(_.value)},{userState:n}=U(ee()),_=V(()=>{if(t.meta.help){if(typeof t.meta.help=="string")return t.meta.help;if(n.value.crt_game&&t.meta.help[n.value.crt_game.game_project])return t.meta.help[n.value.crt_game.game_project]}else return"";return""});return(p,r)=>{const c=i("QuestionCircleOutlined"),a=i("a-button"),s=i("a-tooltip");return g(_)?(u(),h(s,{key:0,title:"帮助"},{default:l(()=>[d(a,{type:"text",onClick:o,class:"tools-item"},{icon:l(()=>[d(c)]),_:1})]),_:1})):j("",!0)}}});const cn=M(rn,[["__scopeId","data-v-ff9a49c8"]]),un=["onClick"],dn={key:0,class:"no-redirect"},pn=["onClick"],_n=x({__name:"Index",setup(e){const t=Q(),o=r=>{const{params:c}=t;return Oe(r)(c)},n=N({breadcrumbs:[],getBreadcrumb:()=>{const r=t.matched.filter(c=>c.meta&&c.meta.title);n.breadcrumbs=r.filter(c=>c.meta&&c.meta.title&&c.meta.breadcrumb!==!1)},isDashboard(r){const c=r&&r.name;return c?c.toString().trim().toLocaleLowerCase()==="Dashboard".toLocaleLowerCase():!1},handleLink(r){const{redirect:c,path:a}=r;if(c){K.push(c).catch(s=>{console.warn(s)});return}K.push(o(a)).catch(s=>{console.warn(s)})}});W(()=>t.path,r=>{r.startsWith("/redirect/")||n.getBreadcrumb()});const{permissionState:_}=U(ne()),p=()=>{K.push(_.value.routes.filter(r=>!r.meta||!r.meta.hidden)[0].path)};return ge(()=>{n.getBreadcrumb()}),(r,c)=>{const a=i("home-outlined"),s=i("a-breadcrumb-item"),m=i("a-breadcrumb");return u(),h(m,{class:"app-breadcrumb"},{default:l(()=>[d(s,null,{default:l(()=>[v("a",{onClick:_e(p,["prevent"])},[d(a)],8,un)]),_:1}),(u(!0),y(T,null,G(g(n).breadcrumbs,(f,I)=>(u(),h(s,{key:f.path},{default:l(()=>[f.redirect==="noredirect"||I===g(n).breadcrumbs.length-1?(u(),y("span",dn,$(f.meta.title),1)):(u(),y("a",{key:1,onClick:_e(k=>g(n).handleLink(f),["prevent"])},$(f.meta.title),9,pn))]),_:2},1024))),128))]),_:1})}}});const mn=M(_n,[["__scopeId","data-v-68c3f351"]]),fn=["innerHTML"],gn={class:"pagination"},hn=x({__name:"LogDrawer",setup(e){const t=N({visible:!1,containerH:window.innerHeight-206,tableData:[],loading:!1,page:1,pagesize:20,total:0,showRoute:["/tool/propQuery"]}),o=[{title:"日期",dataIndex:"created_at",key:"created_at",width:"160px"},{title:"操作人员",dataIndex:"user_name",key:"user_name",width:"100px"},{title:"事件",dataIndex:"op_content",key:"op_content",width:"100px"},{title:"内容",dataIndex:"op_remark",key:"op_remark",ellipsis:!0}],n=Q(),_=V(()=>{var s;return`用户操作日志 - ${(s=n.meta)==null?void 0:s.title}`});W(()=>t.visible,a=>{a?p():r()});const p=async()=>{const a={page:t.page,page_size:t.pagesize};t.loading=!0;try{const s=await Vo(a);t.tableData=s.data,t.total=s.total}catch{}t.loading=!1},r=()=>{t.tableData=[],t.page=1,t.total=0},c=a=>{t.page=a,p()};return(a,s)=>{const m=i("FileTextOutlined"),f=i("a-button"),I=i("a-tooltip"),k=i("a-table"),O=i("a-pagination"),b=i("a-drawer");return u(),y(T,null,[g(t).showRoute.includes(g(n).path)?j("",!0):(u(),h(I,{key:0,title:"日志"},{default:l(()=>[d(f,{type:"text",onClick:s[0]||(s[0]=C=>g(t).visible=!0),class:"tools-item"},{icon:l(()=>[d(m)]),_:1})]),_:1})),d(b,{open:g(t).visible,"onUpdate:open":s[3]||(s[3]=C=>g(t).visible=C),title:g(_),placement:"right",width:"700px"},{default:l(()=>[d(k,{"data-source":g(t).tableData,columns:o,size:"small",pagination:!1,loading:g(t).loading,scroll:{y:g(t).containerH}},{bodyCell:l(({column:C,text:D})=>[C.dataIndex==="op_remark"?(u(),y("div",{key:0,class:"log-content",innerHTML:D,style:{"padding-right":"4px","white-space":"break-spaces"}},null,8,fn)):j("",!0)]),_:1},8,["data-source","loading","scroll"]),v("div",gn,[d(O,{size:"small",current:g(t).page,"onUpdate:current":s[1]||(s[1]=C=>g(t).page=C),"page-size":g(t).pagesize,"onUpdate:pageSize":s[2]||(s[2]=C=>g(t).pagesize=C),total:g(t).total,onChange:c,layout:"total, prev, pager, next, sizes"},null,8,["current","page-size","total"])])]),_:1},8,["open","title"])],64)}}});const vn=M(hn,[["__scopeId","data-v-17dc3b45"]]);function yn(){const e=A(Math.abs(window.screen.height-window.document.documentElement.clientHeight)<=17),t=()=>{if(e.value)document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():Z.error({content:"请升级浏览器!",duration:3}),e.value=!1;else{var o=document.documentElement;o.requestFullscreen?o.requestFullscreen():o.mozRequestFullScreen?o.mozRequestFullScreen():o.webkitRequestFullScreen?o.webkitRequestFullScreen():o.msRequestFullscreen?o.msRequestFullscreen():Z.error({content:"请升级浏览器!",duration:3}),e.value=!0}};return document.addEventListener("fullscreenchange",function(){document.fullscreenElement?e.value=!0:e.value=!1}),window.onresize=function(){e.value=Math.abs(window.screen.height-window.document.documentElement.clientHeight)<=17},window.addEventListener("keydown",function(o){o=o||window.event,(o.keyCode===122||o.code==="F11")&&!e.value&&(o.preventDefault(),t())}),[e,t]}const bn=x({__name:"GameList",setup(e){const t=ee(),{userState:o}=U(t),n=_=>{const p=localStorage.getItem("crtGame");_.game_project!==p&&(localStorage.setItem("crtGame",_.game_project),window.location.href=location.origin)};return(_,p)=>{var k;const r=i("a-avatar"),c=i("CaretDownFilled"),a=i("a-button"),s=i("a-space"),m=i("a-menu-item"),f=i("a-menu"),I=i("a-dropdown");return(k=g(o).crt_game)!=null&&k.game_name?(u(),h(I,{key:0,placement:"bottom",arrow:"",class:"tools-item"},{overlay:l(()=>[d(f,{class:"game-list"},{default:l(()=>[(u(!0),y(T,null,G(g(o).game_infos,(O,b)=>(u(),h(m,{key:b,onClick:C=>n(O),class:he({"game-item":!0,active:g(o).crt_game.game_project===O.game_project})},{default:l(()=>[d(s,null,{default:l(()=>[d(r,{src:O.game_icon_url,size:"small"},null,8,["src"]),w(" "+$(O.game_name),1)]),_:2},1024)]),_:2},1032,["onClick","class"]))),128))]),_:1})]),default:l(()=>[d(a,{type:"text"},{default:l(()=>[d(r,{src:g(o).crt_game.game_icon_url,size:"small",style:{"margin-right":"5px"}},null,8,["src"]),w(" "+$(g(o).crt_game.game_name)+" ",1),d(c,{style:{"font-size":"12px","margin-left":"5px"}})]),_:1})]),_:1})):j("",!0)}}});const wn=M(bn,[["__scopeId","data-v-f292f62d"]]),ke=e=>(ve("data-v-7db46132"),e=e(),ye(),e),Sn=ke(()=>v("img",{src:sn,alt:"",height:"34"},null,-1)),kn=[Sn],Cn={class:"tools-item"},On=ke(()=>v("div",{class:"menu-list"},null,-1)),$n=x({__name:"header",setup(e){const t=we(),{setAsideStatus:o}=t,{systemState:n}=U(t),[_,p]=yn(),r=Q(),c=()=>{K.replace(`/redirect${r.fullPath}`)},a=ee(),{logout:s}=a,{userState:m}=U(a),{permissionState:f}=U(ne()),I=()=>{K.push(f.value.dynamicRoutes[0].path)};return(k,O)=>{const b=i("MenuUnfoldOutlined"),C=i("MenuFoldOutlined"),D=i("a-button"),R=i("ReloadOutlined"),P=i("a-tag"),S=i("a-space"),L=i("CompressOutlined"),F=i("ExpandOutlined"),E=i("a-tooltip"),B=i("CaretDownFilled"),le=i("PoweroffOutlined"),z=i("a-menu-item"),X=i("a-menu"),se=i("a-dropdown"),ie=i("a-layout-header");return u(),h(ie,{class:"header"},{default:l(()=>[v("div",{class:"logo",onClick:I},kn),d(S,{size:1},{default:l(()=>[d(D,{class:"tools-item",type:"text",onClick:g(o)},{icon:l(()=>[g(n).asideStatus?(u(),h(b,{key:0})):(u(),h(C,{key:1}))]),_:1},8,["onClick"]),d(D,{class:"tools-item",type:"text",onClick:c},{icon:l(()=>[d(R)]),_:1}),v("div",Cn,[d(P,{color:"#ff4900",style:{"font-weight":"500",margin:"0"}},{default:l(()=>[w($(g(n).env),1)]),_:1})]),d(mn)]),_:1}),On,d(S,{size:1},{default:l(()=>[d(cn),d(vn),d(E,null,{title:l(()=>[w($(g(_)?"退出全屏":"全屏"),1)]),default:l(()=>[d(D,{class:"tools-item",type:"text",onClick:g(p)},{icon:l(()=>[g(_)?(u(),h(L,{key:0})):(u(),h(F,{key:1}))]),_:1},8,["onClick"])]),_:1}),d(wn),d(se,{placement:"bottom",arrow:"",class:"tools-item"},{overlay:l(()=>[d(X,null,{default:l(()=>[d(z,null,{default:l(()=>[d(S,null,{default:l(()=>[d(le),v("a",{href:"javascript:;",onClick:O[0]||(O[0]=(...Ce)=>g(s)&&g(s)(...Ce))},"退出登录")]),_:1})]),_:1})]),_:1})]),default:l(()=>[d(D,{type:"text"},{default:l(()=>[w($(g(m).userInfo.username)+" ",1),d(B,{style:{"font-size":"12px","margin-left":"5px"}})]),_:1})]),_:1})]),_:1})]),_:1})}}});const In=M($n,[["__scopeId","data-v-7db46132"]]),xn={class:"layout-sider-content"},Dn={class:"menu-scroller"},Fn=x({__name:"asider",setup(e){const{systemState:t}=U(we()),{permissionState:o}=U(ne()),n=s=>s.meta&&s.meta.hidden,_=N({openKeys:[],preOpenKeys:[],filterRoutes:[]}),p=()=>{c.matched.length>0&&(!t.value.asideStatus&&(_.openKeys=[c.matched[0].path]),a.value=[],c.matched.forEach(s=>a.value.push(s.path)),a.value.push(c.path))};W(()=>_.openKeys,(s,m)=>{_.preOpenKeys=m});const r=$e(),c=Q();W(c,s=>p());const a=A([]);return p(),(s,m)=>{const f=i("a-menu-item"),I=i("a-sub-menu"),k=i("a-menu"),O=i("a-layout-sider");return u(),h(O,{collapsed:g(t).asideStatus,"onUpdate:collapsed":m[1]||(m[1]=b=>g(t).asideStatus=b),trigger:null,collapsible:"","collapsed-width":"50"},{default:l(()=>[v("div",xn,[v("div",Dn,[d(k,{selectedKeys:g(a),"onUpdate:selectedKeys":m[0]||(m[0]=b=>Ie(a)?a.value=b:null),openKeys:g(_).openKeys,class:"header-menu",mode:"inline",theme:"dark"},{default:l(()=>[(u(!0),y(T,null,G(g(o).routes,b=>(u(),y(T,null,[n(b)?j("",!0):(u(),y(T,{key:0},[b.children&&b.children.filter(C=>{var D;return!((D=C.meta)!=null&&D.hidden)}).length>1?(u(),h(I,{key:b.path,title:b.meta.title},{icon:l(()=>[b.meta.icon?(u(),h(oe(b.meta.icon),{key:0})):j("",!0)]),default:l(()=>[(u(!0),y(T,null,G(b.children.filter(C=>{var D;return!((D=C.meta)!=null&&D.hidden)}),C=>(u(),h(f,{key:C.path,onClick:D=>g(r).push(C.path)},{default:l(()=>{var D,R,P;return[(D=C.meta)!=null&&D.icon?(u(),h(oe((R=C.meta)==null?void 0:R.icon),{key:0})):j("",!0),w(" "+$((P=C.meta)==null?void 0:P.title),1)]}),_:2},1032,["onClick"]))),128))]),_:2},1032,["title"])):(u(),y(T,{key:1},[n(b)?j("",!0):(u(),h(f,{key:b.path,onClick:C=>g(r).push(b.path)},{default:l(()=>[b.meta.icon?(u(),h(oe(b.meta.icon),{key:0})):j("",!0),v("span",null,$(b.meta.title),1)]),_:2},1032,["onClick"]))],64))],64))],64))),256))]),_:1},8,["selectedKeys","openKeys"])])])]),_:1},8,["collapsed"])}}});const Ln=M(Fn,[["__scopeId","data-v-2f26a245"]]),Tn={class:"home-wrap"},zn=x({__name:"Index",setup(e){return(t,o)=>{const n=i("a-layout-content"),_=i("router-view"),p=i("a-layout");return u(),y("div",Tn,[d(p,null,{default:l(()=>[d(In),d(p,{class:"content-wrap"},{default:l(()=>[d(Ln),d(_,null,{default:l(({Component:r,route:c})=>[d(xe,{name:"fade-transform",mode:"out-in"},{default:l(()=>[(u(),h(n,{class:"page-content",key:c.fullPath},{default:l(()=>[(u(),y("div",{class:"main-wrap",key:c.fullPath},[(u(),h(oe(r),{key:c.fullPath}))]))]),_:2},1024))]),_:2},1024)]),_:1})]),_:1})]),_:1})])}}});const jn=M(zn,[["__scopeId","data-v-9b01a85c"]]),Mn=x({__name:"App",setup(e){H.extend(be).locale("zh-cn");const t=["/login","/","/403"],o=Q(),n=ee(),{userState:_}=U(n);return(p,r)=>{const c=i("router-view"),a=i("a-watermark"),s=i("a-config-provider");return u(),h(s,{theme:g(ln),locale:g(Yt)},{default:l(()=>[d(a,De({content:g(_).userInfo.email},{font:{color:"rgba(0,0,0,0.03)"},gap:[50,50]}),{default:l(()=>[t.indexOf(g(o).path)>-1?(u(),h(c,{key:0})):(u(),h(jn,{key:1})),d(Go)]),_:1},16,["content"])]),_:1},8,["theme","locale"])}}}),Pn=Object.freeze(Object.defineProperty({__proto__:null,Affix:Wt,Alert:Jt,Avatar:Qt,Badge:Xt,Breadcrumb:Zt,Button:eo,Card:to,Checkbox:oo,CheckboxGroup:no,Col:ao,Collapse:lo,ConfigProvider:so,DatePicker:io,Descriptions:ro,DescriptionsItem:co,Divider:uo,Drawer:po,Dropdown:_o,Empty:mo,Flex:fo,Form:de,Image:go,Input:ho,InputNumber:vo,Layout:yo,Menu:bo,Modal:wo,Pagination:So,Popover:ko,Radio:Co,RadioGroup:Oo,RangePicker:$o,Result:Io,Row:xo,Select:Do,SelectOptGroup:Fo,SelectOption:Lo,Space:To,Spin:zo,Switch:jo,TabPane:Mo,Table:Po,Tabs:Eo,Tag:Ho,Tooltip:Uo,Tree:Ro,Typography:Bo,Upload:Ao,Watermark:Ko},Symbol.toStringTag,{value:"Module"})),En=Object.freeze(Object.defineProperty({__proto__:null,AccountBookOutlined:Fe,ApiOutlined:Le,AppstoreOutlined:Te,ArrowDownOutlined:ze,ArrowUpOutlined:je,CaretDownFilled:Me,CaretRightOutlined:Pe,CheckCircleFilled:Ee,CloseCircleFilled:He,CloseCircleOutlined:Ue,CloudDownloadOutlined:Re,CloudUploadOutlined:Be,ClusterOutlined:Ae,CompressOutlined:Ke,DatabaseOutlined:Ve,DeleteOutlined:Ge,DownOutlined:Ne,DownloadOutlined:qe,DropboxOutlined:Ye,EditOutlined:We,EllipsisOutlined:Je,ExclamationCircleFilled:Qe,ExclamationCircleOutlined:Xe,ExpandOutlined:Ze,EyeOutlined:et,FileTextOutlined:tt,FireOutlined:ot,FlagOutlined:nt,FolderOpenOutlined:at,ForkOutlined:lt,FormOutlined:st,GiftOutlined:it,GoldOutlined:rt,GroupOutlined:ct,HomeOutlined:ut,InboxOutlined:dt,LoadingOutlined:pt,LogoutOutlined:_t,MenuFoldOutlined:mt,MenuUnfoldOutlined:ft,MessageOutlined:gt,NodeIndexOutlined:ht,PayCircleOutlined:vt,PlusOutlined:yt,PoweroffOutlined:bt,ProjectOutlined:wt,QuestionCircleOutlined:St,ReloadOutlined:kt,RightSquareOutlined:Ct,SearchOutlined:Ot,SettingOutlined:$t,SlidersOutlined:It,SolutionOutlined:xt,TagsOutlined:Dt,TeamOutlined:Ft,ToolOutlined:Lt,TrophyOutlined:Tt,UploadOutlined:zt,UsergroupAddOutlined:jt,WarningOutlined:Mt},Symbol.toStringTag,{value:"Module"})),me=En,fe=Pn;function Hn(e){for(const t in fe){const o=fe[t];o.install&&e.use(o)}for(const t in me)e.component(t,me[t])}const Un=Object.freeze(Object.defineProperty({__proto__:null,default:Hn},Symbol.toStringTag,{value:"Module"}));function Rn(e){const t=Object.assign({"./antd/index.ts":Un});for(const o in t){const n=t[o].default;typeof n=="function"&&n(e)}}Y.configure({showSpinner:!1});K.beforeEach(async(e,t,o)=>{if(e.query.game_project&&localStorage.setItem("crtGame",e.query.game_project),e.path==="/"&&!localStorage.getItem("ticket")){o({path:"login",replace:!0});return}if(e.name==="Login"){if(e.query.ticket)localStorage.setItem("ticket",e.query.ticket);else if(!localStorage.getItem("ticket"))return o()}if(e.name==="Error"&&t.name==="Error")return o();const n=ee(),{setTicket:_,FETCH_PERMISSION:p,FETCH_GAME_PERMISSION:r}=n,{userState:c}=U(n),a=ne(),{permissionState:s}=U(a),{SET_ROUTES:m,setBtnPromise:f}=a,{FETCH_GLOBAL_CONFIG:I}=pe();if(Y.start(),localStorage.getItem("ticket")&&_(localStorage.getItem("ticket")),c.value.isLogin&&e.path!=="/")return Y.done(),o();if(No.indexOf(e.path)>-1&&!localStorage.getItem("ticket"))return Y.done(),o();try{await r(),await p();const k=c.value.userInfo.permission_list;m(k),f(k),s.value.routes.forEach(O=>{K.addRoute(O)}),Y.done(),s.value.dynamicRoutes.length===0?o({path:"/403",replace:!0}):(I(),e.path==="/login"||e.path==="/"?o({path:s.value.dynamicRoutes[0].path}):o({...e,replace:!0}))}catch(k){k==="no game infos"?o({path:"/403"}):(localStorage.removeItem("ticket"),o({path:"/login"})),Y.done()}});K.afterEach(e=>{Y.done();const t=e.matched.filter(o=>{var n,_;return((n=o.meta)==null?void 0:n.breadcrumb)!==!1&&((_=o.meta)==null?void 0:_.title)}).map(o=>{var n;return(n=o.meta)==null?void 0:n.title}).reverse();document.title=(t.length?t.join(" - ")+" - ":"")+"私域管理平台"});const Bn={class:"box-card-content"},An={key:0,class:"custom-table-top"},Kn={class:"tools",ref:"tools"},Vn={class:"left"},Gn={class:"right"},Nn={key:0,style:{"font-weight":"400",color:"#777","font-size":"13px"}},qn={key:1,class:"custom-table-footer"},Yn={name:"CustomTable"},Wn=x({...Yn,props:{height:{default:"auto"},bottomPadding:{default:0},dataApi:{type:Function,default:()=>Promise.resolve()},params:{default:()=>({})},tableModel:{default:"FillLeaveHeight"},rowKey:{},selection:{type:Boolean},columns:{default:null},noCard:{type:Boolean,default:!1},pagination:{type:Boolean,default:!0}},emits:["changeInfo","selection-change"],setup(e,{expose:t,emit:o}){const n=e,_=A(),p=A([]),r=V(()=>p.value.length>0?[...p.value,...n.columns||[]]:n.columns),c=A(!1),a=N({pageIndex:1,pageSize:15,total:1,list:[],sort_field:"",sort_type:"",selectedRowKeys:[]});function s(S=!1){if(!n.dataApi)return console.error("params dataApi err！");S&&(a.pageIndex=1);const L={...n.params,sort_field:a.sort_field,sort_type:a.sort_type};n.pagination&&(L.page=a.pageIndex,L.page_size=a.pageSize),c.value=!0,n.dataApi(L).then(async F=>{!p.value.length&&F.fields&&(p.value=[]),m(),a.list=F.data.map((E,B)=>F.data.length===1?{...E,first:!0,last:!0}:B===0?{...E,first:!0}:B===F.data.length-1?{...E,last:!0}:E),a.pageIndex=Number(F.current_page),a.total=F.total,a.pageSize=F.per_page,await re(),o("changeInfo",F)}).catch(F=>{console.error(F)}).finally(()=>{c.value=!1})}const m=()=>{a.selectedRowKeys=[],o("selection-change",[])},f=S=>{a.selectedRowKeys.push(S.id),o("selection-change",a.list.filter(L=>a.selectedRowKeys.includes(L.id)))},I=S=>{a.pageIndex=S,s(),m()},k=A(200),O=A("customTable"),b=Pt(async()=>{var S,L,F,E,B;await re(),await re(),k.value=((S=_.value)==null?void 0:S.clientHeight)||0,k.value-=((F=(L=_.value)==null?void 0:L.querySelector(".ant-table-header"))==null?void 0:F.clientHeight)||40,P(["leftTool","rightTool"])&&((E=_.value)!=null&&E.querySelector(".ant-table-title"))&&(k.value-=((B=_.value)==null?void 0:B.querySelector(".ant-table-title")).offsetHeight)},300),C=(S,L,F)=>{var E;a.sort_field=F.columnKey||F.field,a.sort_type=(E=F.order)==null?void 0:E.replace("end",""),a.pageIndex=1,s()},D=async()=>{n.tableModel==="FillLeaveHeight"&&(b(),window.removeEventListener("resize",b),window.addEventListener("resize",b))};Et(()=>{s(),D()}),t({requestTableData:s,renderHeight:D,customTable:O,clearSelection:m,toggleRowSelection:f});const R=Ht(),P=S=>{if(typeof S=="string")return!!R[S];for(let L=0;L<S.length;L++)if(R[S[L]])return!0;return!1};return(S,L)=>{const F=i("a-tooltip"),E=i("a-table"),B=i("a-pagination"),le=i("a-card");return u(),h(le,{class:he(["box-card mw-child-h-auto",{"no-card":S.noCard}]),border:!1},{default:l(()=>[v("div",Bn,[P("top")?(u(),y("div",An,[q(S.$slots,"top",{},void 0,!0)])):j("",!0),v("div",{class:"custom-table-wrap",id:"custom-table-wrap",ref_key:"customTabelWrap",ref:_},[d(E,{loading:c.value,id:"table",ref_key:"customTable",ref:O,"data-source":a.list,"row-selection":S.selection?{fixed:!0,selectedRowKeys:a.selectedRowKeys,onChange:(z,X)=>{a.selectedRowKeys=z,o("selection-change",X)}}:null,pagination:!1,scroll:{x:"100%",y:`${k.value}px`},rowKey:S.rowKey||"id",onChange:C,columns:g(r),size:"small"},Ut({bodyCell:l(({text:z,record:X,index:se,column:ie})=>[q(S.$slots,"bodyCell",{column:ie,record:X,index:se,text:z},void 0,!0)]),headerCell:l(({column:z})=>[q(S.$slots,"headerCell",{column:z},()=>[z.desc?(u(),h(F,{key:0,placement:"top",class:"th-over-hidden"},{title:l(()=>[v("span",null,$(z.desc),1)]),default:l(()=>[v("span",null,$(z.title),1)]),_:2},1024)):(u(),y(T,{key:1},[z.child?(u(),y("span",Nn,$(z.title),1)):(u(),y(T,{key:1},[w($(z.title),1)],64))],64))],!0)]),default:l(()=>[q(S.$slots,"default",{},void 0,!0)]),_:2},[P(["leftTool","rightTool"])?{name:"title",fn:l(()=>[v("div",Kn,[v("div",Vn,[q(S.$slots,"leftTool",{},void 0,!0)]),v("div",Gn,[q(S.$slots,"rightTool",{},void 0,!0)])],512)]),key:"0"}:void 0]),1032,["loading","data-source","row-selection","scroll","rowKey","columns"])],512),n.pagination?(u(),y("div",qn,[d(B,{size:"small",current:a.pageIndex,"onUpdate:current":L[0]||(L[0]=z=>a.pageIndex=z),"page-size":a.pageSize,"onUpdate:pageSize":L[1]||(L[1]=z=>a.pageSize=z),total:a.total,"page-size-options":["15","30","50","100"],onChange:I,"show-quick-jumper":"","show-size-changer":"",layout:"total, prev, pager, next, sizes"},null,8,["current","page-size","total"])])):j("",!0)])]),_:3},8,["class"])}}});const Jn=M(Wn,[["__scopeId","data-v-3668bf36"]]),Qn=Object.freeze(Object.defineProperty({__proto__:null,default:Jn},Symbol.toStringTag,{value:"Module"}));const Xn=x({name:"Editor",components:{WangEditor:Rt,Toolbar:Bt},props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},height:{type:Number,default:300}},emits:["update:value","change"],setup(e,{emit:t}){const o=At(),n=V({get:()=>e.value,set:a=>{t("update:value",a)}}),_={toolbarKeys:["headerSelect","fontSize","bold","underline","italic","through","clearStyle","color","bgColor","sup","sub","bulletedList","numberedList","todo"]},p={placeholder:"请输入内容..."};return W(()=>e.disabled,a=>{o.value&&(a?o.value.disable():o.value.enable())}),Kt(()=>{const a=o.value;a!=null&&a.destroy()}),{editorRef:o,valueHtml:n,mode:"default",toolbarConfig:_,editorConfig:p,handleCreated:a=>{o.value=a},onChange:a=>{a.getText()||t("update:value","")}}}});const Zn={class:"custom-editor"};function ea(e,t,o,n,_,p){const r=i("Toolbar"),c=i("WangEditor");return u(),y("div",Zn,[d(r,{style:{"border-bottom":"1px solid #f0f0f0"},editor:e.editorRef,defaultConfig:e.toolbarConfig,mode:e.mode},null,8,["editor","defaultConfig","mode"]),d(c,{style:Vt(`height: ${e.height}px; overflow-y: hidden;`),modelValue:e.valueHtml,"onUpdate:modelValue":t[0]||(t[0]=a=>e.valueHtml=a),defaultConfig:e.editorConfig,mode:e.mode,onOnCreated:t[1]||(t[1]=a=>e.editorRef=a),onOnChange:e.onChange},null,8,["style","modelValue","defaultConfig","mode","onOnChange"])])}const ta=M(Xn,[["render",ea],["__scopeId","data-v-2edbefed"]]),oa=Object.freeze(Object.defineProperty({__proto__:null,default:ta},Symbol.toStringTag,{value:"Module"})),na={class:"prop-icon"},aa=["src"],la=["src"],sa=["src"],ia={name:"PropIcon"},ra=x({...ia,props:{propDetail:{}},setup(e){const t=e;return(o,n)=>(u(),y("div",na,[ce(v("img",{src:t.propDetail.image_quality},null,8,aa),[[ue,t.propDetail.image_quality]]),ce(v("img",{src:t.propDetail.image},null,8,la),[[ue,t.propDetail.image]]),ce(v("img",{src:t.propDetail.image_chip},null,8,sa),[[ue,t.propDetail.image_chip]])]))}});const ca=M(ra,[["__scopeId","data-v-fd5e0ee4"]]),ua=Object.freeze(Object.defineProperty({__proto__:null,default:ca},Symbol.toStringTag,{value:"Module"})),te=e=>(ve("data-v-8c01742a"),e=e(),ye(),e),da={class:"content-box"},pa=te(()=>v("div",{class:"title"},"1.下载导入模板",-1)),_a=te(()=>v("div",{class:"desc"},"请先下载导入模板，按模板要求填写数据；为避免导入失败，填写过程请勿修改表头",-1)),ma={class:"btns-wrap"},fa={class:"content-box"},ga=te(()=>v("div",{class:"title"},"2.导入文件",-1)),ha={class:"desc"},va=te(()=>v("br",null,null,-1)),ya={class:"btns-wrap"},ba={class:"ant-upload-drag-icon"},wa=te(()=>v("p",{class:"ant-upload-text"},"点击或拖拽文件至此区域即可上传",-1)),Sa={name:"UploadBtn"},ka=x({...Sa,props:{uploadData:{default:()=>({})},page:{default:""},accept:{default:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"},downloadApi:{type:Function,default:()=>{}},fileType:{default:""},downloadData:{default:()=>({})},hideUploadBtn:{type:Boolean,default:!1},hideDownloadBtn:{type:Boolean,default:!1}},emits:["uploadSuccess"],setup(e,{emit:t}){const o=e,n=N({visibleUpload:!1,uploadLoading:!1,fileList:[],file:null});function _(){n.visibleUpload=!1,n.uploadLoading=!1,n.fileList=[]}const p=s=>(n.fileList=[s],!1);function r(s){n.fileList=[s.file]}function c(){if(n.fileList.length===0)return Z.error("未选择文件！");n.uploadLoading=!0;const s=new FormData;s.append("file",n.fileList[0]);for(const m in o.uploadData)s.append(m,o.uploadData[m]);qo(o.page,s).then(m=>{t("uploadSuccess",m),_()}).catch(()=>(n.uploadLoading=!1,Z.error("请按照模版上传")))}W(()=>n.visibleUpload,(s,m)=>{s===!1&&m===!0&&(n.uploadLoading=!1,n.fileList=[])});const a=s=>{s?Yo(o.fileType):o.downloadApi({...o.downloadData})};return(s,m)=>{const f=i("a-button"),I=i("CloudDownloadOutlined"),k=i("a-card"),O=i("inbox-outlined"),b=i("a-upload-dragger"),C=i("CloudUploadOutlined"),D=i("a-modal"),R=i("a-space");return u(),h(R,null,{default:l(()=>[o.hideUploadBtn?j("",!0):(u(),h(f,{key:0,onClick:m[0]||(m[0]=P=>n.visibleUpload=!0)},{default:l(()=>[w("批量更新")]),_:1})),o.hideDownloadBtn?j("",!0):(u(),h(f,{key:1,type:"primary",link:"",onClick:m[1]||(m[1]=P=>a(!1))},{default:l(()=>[w("导出配置")]),_:1})),d(D,{open:n.visibleUpload,"onUpdate:open":m[5]||(m[5]=P=>n.visibleUpload=P),title:"批量更新","mask-closable":!1},{footer:l(()=>[d(f,{link:"",onClick:_,disabled:n.uploadLoading},{default:l(()=>[w("取消")]),_:1},8,["disabled"]),d(f,{type:"primary",loading:n.uploadLoading,onClick:c},{icon:l(()=>[d(C)]),default:l(()=>[w(" 上传 ")]),_:1},8,["loading"])]),default:l(()=>[d(k,null,{default:l(()=>[v("div",da,[pa,_a,v("div",ma,[d(f,{type:"primary",onClick:m[2]||(m[2]=P=>a(!0))},{icon:l(()=>[d(I)]),default:l(()=>[w(" 下载导入模板 ")]),_:1})])])]),_:1}),d(k,null,{default:l(()=>[v("div",fa,[ga,v("div",ha,[q(s.$slots,"tip",{},()=>[w(" * 请按照模板格式准备需要导入的数据，更新后，已配置数据将被覆盖"),va,w(" * 文件小于2M，上传后即开始导入 ")],!0)]),v("div",ya,[d(b,{fileList:n.fileList,"onUpdate:fileList":m[3]||(m[3]=P=>n.fileList=P),name:"file",multiple:!1,action:"/",accept:o.accept,onChange:r,onRemove:m[4]||(m[4]=P=>n.fileList=[]),"before-upload":p},{default:l(()=>[v("p",ba,[d(O)]),wa]),_:1},8,["fileList","accept"])])])]),_:3})]),_:3},8,["open"])]),_:3})}}});const Ca=M(ka,[["__scopeId","data-v-8c01742a"]]),Oa=Object.freeze(Object.defineProperty({__proto__:null,default:Ca},Symbol.toStringTag,{value:"Module"}));function $a(e){const t=Object.assign({"../packages/customTable/Index.vue":Qn,"../packages/editor/Index.vue":oa,"../packages/pictrue/Index.vue":Wo,"../packages/propIcon/Index.vue":ua,"../packages/uploadBtn/Index.vue":Oa});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const Ia={install:$a};H.extend(be);const xa=x({name:"SelectDateTime",props:{value:{type:Array,default:()=>[]},isDisabledDate:{type:Boolean,default:!1}},emits:["update:value","change"],setup(e,t){const o=V({get:()=>{if(e.value)return e.value.length===0?[]:e.value.map(p=>p&&H.utc(p*1e3).format("YYYY-MM-DD HH:mm:ss")||void 0)},set:p=>{let r;p!=null&&p.length&&(r=[H.utc(p[0]).valueOf()/1e3,H.utc(p[1]).valueOf()/1e3]),t.emit("update:value",r),t.emit("change",r)}}),n=p=>e.isDisabledDate?p&&p<H().startOf("day"):!1,_=()=>{const p=!!e.isDisabledDate,r=H().hour(),c=H().minute(),a=H().second(),s=e.isDisabledDate?[H().hour(r).minute(c).second(a),H().hour(r).minute(c).second(a)]:[H().hour(0).minute(0).second(0),H().hour(r).minute(c).second(a)];return{hideDisabledOptions:p,defaultValue:s}};return{...J(e),modelValue:o,disabledDate:n,showTime:_,dayjs:H}}});function Da(e,t,o,n,_,p){const r=i("a-range-picker");return u(),h(r,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=c=>e.modelValue=c),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":e.disabledDate,"show-time":e.showTime()},null,8,["value","disabled-date","show-time"])}const Fa=M(xa,[["render",Da]]),La=Object.freeze(Object.defineProperty({__proto__:null,default:Fa},Symbol.toStringTag,{value:"Module"})),Ta=x({name:"SelectImgGallery",props:{value:{type:String,default:""},preview:{type:String,default:""}},emits:["update:value","change"],setup(e,{emit:t}){const o=de.useInjectFormItemContext(),n=(s,m={})=>{_.url=m.showUrl,t("update:value",s),t("change",m),o.onFieldChange()},_=N({url:"",isFirst:!0,previewVisible:!1}),p=()=>{Jo.$emit("showPhotoGallery",{callback:s=>{n(s.img_key,s)}})},r=()=>{if(!_.isFirst||!e.value)return;const s={search:e.value};Qo(s).then(m=>{n(m.img_key,m.preview_img||"")}).finally(()=>{_.isFirst=!1})};ge(()=>{r()});const c=s=>{s===!1&&(_.previewVisible=!1)},a=()=>{Z.error("选择的图片已不存在，请重新选择！"),n("")};return{...J(_),chooseImg:p,onVisibleChange:c,imgLoadError:a,triggerChange:n}}});const za={class:"custom-select-img"};function ja(e,t,o,n,_,p){const r=i("EyeOutlined"),c=i("DeleteOutlined"),a=i("a-space"),s=i("a-image"),m=i("PlusOutlined");return u(),y("div",za,[e.url||e.preview?(u(),h(s,{key:0,src:e.url||e.preview,style:{width:"auto","max-width":"100%","max-height":"100%"},preview:{visible:e.previewVisible,onVisibleChange:e.onVisibleChange},onError:e.imgLoadError},{previewMask:l(()=>[d(a,{align:"center"},{default:l(()=>[d(r,{onClick:t[0]||(t[0]=f=>e.previewVisible=!0)}),d(c,{onClick:t[1]||(t[1]=f=>e.triggerChange(""))})]),_:1})]),_:1},8,["src","preview","onError"])):(u(),y("div",{key:1,class:"img-plus",onClick:t[2]||(t[2]=(...f)=>e.chooseImg&&e.chooseImg(...f))},[d(m)]))])}const Ma=M(Ta,[["render",ja],["__scopeId","data-v-0be816df"]]),Pa=Object.freeze(Object.defineProperty({__proto__:null,default:Ma},Symbol.toStringTag,{value:"Module"})),Ea=x({name:"SelectImg",props:{value:{type:String,default:""},widthHeight:{type:Array,default:()=>[]},tips:{type:String,default:""}},emits:["update:value","change"],setup(e,{emit:t}){const o=de.useInjectFormItemContext(),n=f=>{t("update:value",f),t("change",f),o.onFieldChange()},_=N({loading:!1,previewVisible:!1}),p=V({get:()=>e.value,set:f=>{n(f)}}),r=V(()=>p.value?[{uid:Math.floor(Math.random()*100),name:"image.png",status:"done",url:p.value}]:[]),c=async f=>new Promise((I,k)=>{const O=new FormData;O.append("image",f),Xo(O).then(b=>{p.value=b.url,I("")}).catch(()=>{k("")})}),a=async f=>{if(!f)return!1;try{_.loading=!0,await c(f)}catch{}return _.loading=!1,!1},s=f=>{},m=f=>{_.previewVisible=f};return{...J(e),...J(_),imageUrl:p,fileList:r,triggerChange:n,beforeUpload:a,handleChange:s,setVisible:m}}}),Ha={key:0},Ua=v("div",{class:"ant-upload-text"},"Upload",-1),Ra={style:{display:"none"}};function Ba(e,t,o,n,_,p){const r=i("loading-outlined"),c=i("plus-outlined"),a=i("a-upload"),s=i("ExclamationCircleFilled"),m=i("a-typography-text"),f=i("a-image");return u(),y(T,null,[d(a,{style:{height:"110px"},"file-list":e.fileList,"onUpdate:fileList":t[0]||(t[0]=I=>e.fileList=I),action:"#","list-type":"picture-card",accept:"image/gif,image/jpeg,image/jpg,image/png,image/svg","before-upload":e.beforeUpload,onPreview:t[1]||(t[1]=()=>e.setVisible(!0)),onRemove:t[2]||(t[2]=I=>e.imageUrl="")},{default:l(()=>[e.imageUrl?j("",!0):(u(),y("div",Ha,[e.loading?(u(),h(r,{key:0})):(u(),h(c,{key:1})),Ua]))]),_:1},8,["file-list","before-upload"]),e.widthHeight.length>0?(u(),h(m,{key:0,type:"warning",style:{"font-size":"12px"}},{default:l(()=>[d(s),w(" "+$((e.tips?`${e.tips} `:"前台展示图片，建议尺寸比例: ")+`${e.widthHeight[0]} * ${e.widthHeight[1]}`),1)]),_:1})):j("",!0),v("div",Ra,[d(f,{width:200,preview:{visible:e.previewVisible,onVisibleChange:e.setVisible},src:e.value},null,8,["preview","src"])])],64)}const Aa=M(Ea,[["render",Ba]]),Ka=Object.freeze(Object.defineProperty({__proto__:null,default:Aa},Symbol.toStringTag,{value:"Module"})),Va=x({name:"SelectLang",props:{value:{type:String,default:void 0}},emits:["update:value","updateLabel","initLabel"],setup(e,t){const o=N({loading:!1,options:[]});W(()=>e.value,(r,c)=>{r&&!c&&!o.options.length&&Se({key:r}).then(a=>{o.options.push({label:a.zh_cn||a.en,value:r})})});const n=V({get:()=>{var r;return e.value&&t.emit("initLabel",(r=o.options.find(c=>c.value===e.value))==null?void 0:r.label),e.value},set:r=>{var c;t.emit("update:value",r),t.emit("updateLabel",(c=o.options.find(a=>a.value===r))==null?void 0:c.label)}}),_=r=>{if(!r){o.options=[];return}o.loading=!0,Zo({key:r}).then(c=>{o.options=c}).finally(()=>{o.loading=!1})},p=r=>{t.emit("update:value",r)};return{...J(o),modelValue:n,remoteMethod:_,handleChange:p}}});const Ga={class:"lang-select-wrap"},Na={class:"item"},qa={class:"t"},Ya={class:"b"};function Wa(e,t,o,n,_,p){const r=i("a-spin"),c=i("a-select");return u(),y("div",Ga,[d(c,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=a=>e.modelValue=a),allowClear:"",showSearch:"",placeholder:"请输入多语言key","filter-option":!1,"not-found-content":e.loading?void 0:null,"option-label-prop":"label",onSearch:e.remoteMethod,onChange:e.handleChange,style:{"min-width":"200px"},options:e.options},{notFoundContent:l(()=>[d(r,{size:"small"})]),option:l(({value:a,label:s})=>[v("div",Na,[v("div",qa,$(s),1),v("div",Ya,$(a),1)])]),_:1},8,["value","not-found-content","onSearch","onChange","options"])])}const Ja=M(Va,[["render",Wa],["__scopeId","data-v-e7d0c585"]]),Qa=Object.freeze(Object.defineProperty({__proto__:null,default:Ja},Symbol.toStringTag,{value:"Module"})),Xa=x({name:"SelectWithAllComp",components:{SelectWithAll:en},props:{value:{type:String,default:""},placeholder:{type:String,default:"请选择操作系统"},type:{type:String,default:"platform",validate:e=>["platform","langs","channels"].includes(e)}},emits:["update:value","change"],setup(e,t){const{configState:o}=U(pe()),n=V({get:()=>e.value,set:_=>{t.emit("update:value",_),t.emit("change",_)}});return{...J(e),modelValue:n,configState:o}}});function Za(e,t,o,n,_,p){const r=i("SelectWithAll");return u(),h(r,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=c=>e.modelValue=c),placeholder:e.placeholder,options:e.configState[e.type]},null,8,["value","placeholder","options"])}const el=M(Xa,[["render",Za]]),tl=Object.freeze(Object.defineProperty({__proto__:null,default:el},Symbol.toStringTag,{value:"Module"})),ol=x({name:"UploadFile",props:{value:{type:String,default:""}},setup(e,t){return{}}});function nl(e,t,o,n,_,p){return null}const al=M(ol,[["render",nl]]),ll=Object.freeze(Object.defineProperty({__proto__:null,default:al},Symbol.toStringTag,{value:"Module"}));function sl(e){const t=Object.assign({"./select-date-time/Index.vue":La,"./select-img-gallery/Index.vue":Pa,"./select-img/Index.vue":Ka,"./select-lang/Index.vue":Qa,"./select-with-all-comp/Index.vue":tl,"./select-with-all/Index.vue":tn,"./upload-file/Index.vue":ll});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const il={install:sl},rl=x({name:"FilterCell"}),cl=x({...rl,props:{record:{type:Object,default:()=>({})}},setup(e){const t=e,{getConfItem:o}=pe();return(n,_)=>{const p=i("a-tag"),r=i("a-descriptions-item"),c=i("a-descriptions"),a=i("a-typography-link"),s=i("a-tooltip");return t.record.is_filter===g(on).CLOSE?(u(),y(T,{key:0},[w("关闭")],64)):(u(),h(s,{key:1,placement:"left",color:"white",destroyTooltipOnHide:"",overlayStyle:{"max-width":"350px"}},{title:l(()=>[d(c,{column:1,size:"small",bordered:"",labelStyle:{width:"88px","white-space":"nowrap"}},{default:l(()=>[d(r,{label:"操作系统"},{default:l(()=>[t.record.f_os==="all"?(u(),y(T,{key:0},[w("ALL")],64)):(u(!0),y(T,{key:1},G(t.record.f_os.split("|").filter(m=>m),m=>(u(),h(p,{key:m},{default:l(()=>{var f;return[w($((f=g(o)("platform",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),d(r,{label:"语种"},{default:l(()=>[t.record.lang==="all"?(u(),y(T,{key:0},[w("ALL")],64)):(u(!0),y(T,{key:1},G(t.record.lang.split("|").filter(m=>m),m=>(u(),h(p,{key:m},{default:l(()=>{var f;return[w($((f=g(o)("langs",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),d(r,{label:"渠道"},{default:l(()=>[t.record.f_channel==="all"?(u(),y(T,{key:0},[w("ALL")],64)):(u(!0),y(T,{key:1},G(t.record.f_channel.split("|").filter(m=>m),m=>(u(),h(p,{key:m},{default:l(()=>{var f;return[w($((f=g(o)("channels",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),d(r,{label:"服务器"},{default:l(()=>[w($(e.record.f_s_ids),1)]),_:1}),d(r,{label:"城堡等级"},{default:l(()=>[w($(e.record.f_lv_ids),1)]),_:1}),e.record.uids!==void 0?(u(),h(r,{key:0,label:"uid 白名单"},{default:l(()=>[w($(e.record.uids||"-"),1)]),_:1})):j("",!0)]),_:1})]),default:l(()=>[d(a,null,{default:l(()=>[w("开启")]),_:1})]),_:1}))}}}),ul=Object.freeze(Object.defineProperty({__proto__:null,default:cl},Symbol.toStringTag,{value:"Module"})),dl=x({name:"LangKey"}),pl=x({...dl,props:{langKey:{default:""},i18n_name:{default:()=>[]}},setup(e){const t=e,o=A({}),n=A(!1),_=()=>{JSON.stringify(o.value)==="{}"&&(n.value=!0,Se({key:t.langKey}).then(p=>{o.value=p}).finally(()=>n.value=!1))};return(p,r)=>{const c=i("a-spin"),a=i("a-typography-text"),s=i("a-descriptions-item"),m=i("a-descriptions"),f=i("ExclamationCircleOutlined"),I=i("a-popover");return u(),y(T,null,[w($((t.i18n_name.find(k=>k.key===t.langKey)||{}).content||t.langKey||"-")+" ",1),t.langKey?(u(),h(I,{key:0,placement:"rightTop",onOpenChange:_,overlayInnerStyle:{"max-height":"500px","overflow-y":"auto"}},{content:l(()=>[g(n)?(u(),h(c,{key:0})):!g(n)&&JSON.stringify(g(o))==="[]"?(u(),h(a,{key:1,type:"secondary"},{default:l(()=>[w("无数据")]),_:1})):(u(),h(m,{key:2,column:1,size:"small",bordered:"",labelStyle:{width:"88px","white-space":"nowrap"},contentStyle:{"max-width":"400px"}},{default:l(()=>[(u(!0),y(T,null,G(g(o),(k,O)=>(u(),h(s,{key:O,label:O},{default:l(()=>[w($(k),1)]),_:2},1032,["label"]))),128))]),_:1}))]),default:l(()=>[d(f)]),_:1})):j("",!0)],64)}}}),_l=Object.freeze(Object.defineProperty({__proto__:null,default:pl},Symbol.toStringTag,{value:"Module"}));function ml(e){const t=Object.assign({"./filter-cell/Index.vue":ul,"./lang-key/Index.vue":_l});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const fl={install:ml},gl={name:"has",beforeMount(e,t,o){var p;const n=t.value;nn.checkBtnPromission(n)?o.type==="template"&&e.replaceWith(...e.children):e.parentNode&&((p=e.parentNode)==null?void 0:p.removeChild).call(p,e)},mounted(){},beforeUpdate(){},updated(){},beforeUnmount(){},unmounted(){}},hl=Object.freeze(Object.defineProperty({__proto__:null,default:gl},Symbol.toStringTag,{value:"Module"})),vl=e=>{const t=Object.assign({"./CommonPermission.ts":hl});for(const o in t){const{name:n,beforeMount:_}=t[o].default;e.directive(n,_)}},yl={install:vl};let ae=null;ae=Gt({render:()=>qt(Mn)});Rn(ae);ae.use(an).use(K).use(yl).use(Ia).use(il).use(fl).use(Nt);ae.mount("#app");
