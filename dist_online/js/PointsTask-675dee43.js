import{y as D,z as me,A as le,L as se,P as ne,B as ye,m as ke}from"./common-b8130258.js";import{M as oe,m as fe}from"./antd-914dcf46.js";import{d as ie,r as V,h as te,x as re,y as ee,a as _,n as pe,o as g,c as C,w as p,b as o,u as a,p as ue,v as A,e as v,k as f,F as h,s as be,t as w,l as k,i as he,z as we,A as xe,_ as de,f as ae,g as Le}from"./vendor-cc06403f.js";const Ie=c=>D.get("/api/task-config",c),Ce=c=>D.post("/api/task-config",c),De=(c,x)=>D.put(`/api/task-config/${c}`,x),Se=c=>D.get(`/api/task-config/${c}`),Ue=({petId:c})=>D.delete(`/api/task-config/${c}`),Te=(c,x)=>D.put(`/api/task-config/switch-status/${c}`,{status:x}),Ve=()=>D.downfile("/api/task-config/export"),Oe=c=>D.downfile("/api/task-config/export-rule-desc",c),_e=c=>(we("data-v-a905eda4"),c=c(),xe(),c),Pe={style:{display:"flex","align-items":"center","margin-bottom":"30px"}},Ge=_e(()=>A("span",{style:{display:"inline-block",width:"100px"}},[A("b",{style:{color:"red"}},"*"),v("progress: ")],-1)),Ne={style:{display:"inline-block",width:"100px"}},qe={key:0},Fe=_e(()=>A("strong",null,"任务描述示例：",-1)),Be=ie({__name:"Form",props:["editId"],emits:["close","refresh"],setup(c,{emit:x}){const d=c,{configState:O}=me(),P=V(!0),L=V(!1),Y=()=>{L.value=!0,Se(d.editId).then(s=>{t.value=s,t.value.pkg_id=Number(s.pkg_id),t.value.filterData=s.filter&&s.filter.includes("server_min")?JSON.parse(s.filter):{},t.value.filterData.open_filter=!1,t.value.task_where_data=s.task_where&&s.task_where.includes("event_key")?JSON.parse(s.task_where):[],s.task_validity_type===2&&s.task_start_time&&s.task_end_time?t.value.times=[s.task_start_time,s.task_end_time]:t.value.times=void 0;const e=s.task_params&&s.task_params.includes("params_arr")?JSON.parse(s.task_params):{};if(e.params_arr&&e.params_arr.length>0){const l=e.params_arr.findIndex(n=>n.is_progress===1);r.progressKey=l+1,e.params_arr=e.params_arr.map((n,T)=>(n.is_progress=T+1,n)),t.value.task_params_arr=e.params_arr}s&&s.status===1?(r.isOnline=!0,oe.confirm({title:"提示",content:"当前任务上线中，请下线后进行修改",okText:"确定",cancelText:"",cancelButtonProps:{ghost:!0},onOk:()=>{}})):r.isOnline=!1}).finally(()=>L.value=!1)};d.editId&&Y();const t=V({id:0,task_category:1,lang_key:void 0,filter:"{}",task_dimension_desc:"123",cycle_type:1,cycle_times:1,pkg_id:void 0,dimension:1,progress:1,image:"",task_key:new Date().getTime().toString(),event_id:null,task_event_id:null,url_target:0,url:"",reward_type:1,coin_num:0,task_validity_type:1,times:void 0,name_key:"",quantity:0,is_filter:!1,task_where:"",task_where_data:[{value:"",key:Date.now(),event_key:null,operator:""}],filterData:{server_min:0,server_max:99999,vip_restriction:1,game_growth:0,game_role_growth:0,open_filter:!1},task_params:"",task_params_arr:[],rule_desc:""}),r=te({lang_value:"",lang_label:"",task_desc:"",isOnline:!1,uploadLoading:!1,progressKey:0,optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4},{label:"限时任务",value:5}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],jumpType:[{label:"跳转到特定页面",value:0},{label:"打开游戏",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:0},{label:"按角色",value:1}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5,disabled:!0},{label:"终身领取",value:6}],progressList:[{label:"每次需完成1个行为",value:1},{label:"每次需完成2个行为",value:2},{label:"每次需完成3个行为",value:3},{label:"每次需完成4个行为",value:4},{label:"每次需完成5个行为",value:5},{label:"每次需完成6个行为",value:6},{label:"每次需完成7个行为",value:7},{label:"每次需完成8个行为",value:8},{label:"每次需完成9个行为",value:9},{label:"每次需完成10个行为",value:10}],rewardType:[{label:"游戏道具奖励",value:1}],optionsGifts:[],optionsGiftsFilter:[],optionsLang:[],optionsLangKey:[],optionsOriginTaskEvent:[],optionsTaskEvent:[]}),G=te({filterData:{task_where:[{required:!0,message:"请选择任务条件",trigger:"change"}]}}),S=V(!1),$=V();re(()=>{U(),N({page:1,page_size:1e3})}),ee(()=>O,s=>{s&&s.task_events&&s.task_events.length>0&&(r.optionsTaskEvent=s.task_events.map(e=>({originLabel:e.label,label:e.label,value:e.task_events_id})))},{immediate:!0}),ee(()=>t.value.task_category,s=>{s===5&&(t.value.task_validity_type=2)}),ee(()=>t.value.progress,s=>{r.optionsTaskEvent.forEach(e=>{e.label=e.originLabel.replace(/\{progress\}/g,s)})},{immediate:!0});const m=s=>{s&&((t.value.task_params_arr||[]).forEach(l=>{s=s.replace(/\{parameter\}/,l.value)}),r.task_desc=s.replace(/\{progress\}/g,String(t.value.progress)))},u=s=>{if(r.lang_label=s,s){const e=/\{parameter\}/g,l=s.match(e);if(l){const n=l.length;t.value.task_params_arr=Array.from({length:n},(T,q)=>({key:"parameter",value:1,is_progress:q+1})),r.task_desc=s.replace(/\{parameter\}/g,"1").replace(/\{progress\}/g,String(t.value.progress))}else t.value.task_params_arr=[]}else t.value.task_params_arr=[];t.value.task_params_arr},b=()=>{if(r.lang_label){const s=r.lang_label.split("{parameter}"),e=t.value.task_params_arr.map(l=>l.value);if(e.length>0){for(let l=0;l<e.length;l++)s[l]=s[l]+e[l];r.task_desc=s.join("").replace(/\{progress\}/g,String(t.value.progress))}}},U=()=>{le().then(s=>{s&&s.length>0&&(r.optionsGifts=s,r.optionsGiftsFilter=[...r.optionsGifts])})},N=s=>{se(s).then(e=>{e.data&&e.data.length>0&&(r.optionsLang=e.data.map(l=>({label:l.zh_cn,value:l.key})),r.optionsLangKey=[...r.optionsLang])})},H=s=>{s===void 0&&(r.optionsGiftsFilter=r.optionsGifts)},Q=s=>{const e=r.optionsGifts.filter(l=>{var n;return((n=l.label)==null?void 0:n.indexOf(s))>-1});r.optionsGiftsFilter=e},W=s=>{t.value.rule_desc=JSON.stringify(s||[]),fe.success("上传解析成功")},X=(s,e)=>{},E=()=>{$.value.validate().then(()=>{S.value=!0;const{id:s,...e}=t.value;try{if(e.filter=JSON.stringify(e.filterData),e.task_where=JSON.stringify(e.task_where_data),e.task_validity_type===2&&e.times?(e.task_start_time=e.times[0],e.task_end_time=e.times[1]):(e.task_start_time=0,e.task_end_time=0),e.task_params_arr&&e.task_params_arr.length>0){const l=[];e.task_params_arr.forEach(n=>{r.progressKey>0&&r.progressKey===n.is_progress?n.is_progress=1:n.is_progress=0,l.push(n)}),e.task_params=JSON.stringify({params_arr:l})}else e.task_params="{}"}catch{}e.task_params,e.rule_desc,d.editId?De(s,e).then(()=>{x("close"),x("refresh",!0)}).catch(()=>{}).finally(()=>{S.value=!1}):Ce(e).then(()=>{x("close"),x("refresh")}).catch(()=>{}).finally(()=>{S.value=!1}),setTimeout(()=>{S.value=!1},1e3)}).catch(()=>{})};return(s,e)=>{const l=_("a-select"),n=_("a-form-item"),T=_("SelectImg"),q=_("SelectLang"),K=_("UploadBtn"),I=_("a-input-number"),J=_("a-radio"),F=_("a-radio-group"),B=_("a-space"),M=_("a-input"),R=_("SelectDateTime"),y=_("a-button"),j=_("a-form"),ge=_("a-spin"),ve=_("a-drawer"),ce=pe("has");return g(),C(ve,{open:a(P),"onUpdate:open":e[20]||(e[20]=i=>he(P)?P.value=i:null),title:d.editId?"编辑任务":"新增任务",maskClosable:!1,width:800,onAfterOpenChange:e[21]||(e[21]=i=>!i&&x("close"))},{default:p(()=>[o(ge,{spinning:a(L)},{default:p(()=>[o(j,{model:a(t),rules:a(G),name:"basic",ref_key:"formRef",ref:$,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:p(()=>[o(n,{label:"任务类型",name:"task_category",rules:[{required:!0,message:"请选择任务类型"}]},{default:p(()=>[o(l,{style:{width:"100%"},value:a(t).task_category,"onUpdate:value":e[0]||(e[0]=i=>a(t).task_category=i),options:a(r).optionsTaskCategory},null,8,["value","options"])]),_:1}),o(n,{label:"任务条件",name:"task_event_id",rules:[{required:!0,message:"请选择任务事件"}]},{default:p(()=>[o(l,{style:{width:"100%"},value:a(t).task_event_id,"onUpdate:value":e[1]||(e[1]=i=>a(t).task_event_id=i),options:a(r).optionsTaskEvent,placeholder:"请选择任务事件"},null,8,["value","options"])]),_:1}),o(n,{label:"任务icon",name:"image",rules:[{required:!0,message:"请上传任务icon"}]},{default:p(()=>[o(T,{value:a(t).image,"onUpdate:value":e[2]||(e[2]=i=>a(t).image=i),"width-height":[72,72]},null,8,["value"])]),_:1}),o(n,{label:"任务描述",name:"lang_key",rules:[{required:!0,message:"请选择任务描述"}]},{default:p(()=>[o(q,{value:a(t).lang_key,"onUpdate:value":e[3]||(e[3]=i=>a(t).lang_key=i),onInitLabel:m,onUpdateLabel:u,placeholder:"请选择任务描述"},null,8,["value"])]),_:1}),o(n,{label:"任务说明",name:"file"},{default:p(()=>[ue(o(K,{ref:"uploadBtn",onUploadSuccess:W,downloadApi:a(Oe),fileType:"task-config-rule-desc",page:a(ne).TASKRULE,hideDownloadBtn:!a(t).msg_template_code,downloadData:a(t).msg_template_code?{msg_template_code:a(t).msg_template_code}:{}},null,8,["downloadApi","page","hideDownloadBtn","downloadData"]),[[ce,"Operation"]])]),_:1}),o(n,{style:{display:"none"},label:"VIP等级",name:"filterData.vip_restriction",rules:a(G)["filterData.vip_restriction"]},{default:p(()=>[o(l,{style:{width:"100%"},value:a(t).filterData.vip_restriction,"onUpdate:value":e[4]||(e[4]=i=>a(t).filterData.vip_restriction=i),options:a(r).optionsVip},null,8,["value","options"])]),_:1},8,["rules"]),o(n,{style:{display:"none"},label:"游戏成长值",name:"game_growth"},{default:p(()=>[o(I,{style:{width:"100%"},value:a(t).filterData.game_growth,"onUpdate:value":e[5]||(e[5]=i=>a(t).filterData.game_growth=i),min:0,max:15e5,placeholder:"请输入游戏成长值"},null,8,["value"])]),_:1}),o(n,{style:{display:"none"},label:"角色成长值",name:"game_role_growth"},{default:p(()=>[o(I,{style:{width:"100%"},value:a(t).filterData.game_role_growth,"onUpdate:value":e[6]||(e[6]=i=>a(t).filterData.game_role_growth=i),min:0,max:15e5,placeholder:"请输入角色成长值"},null,8,["value"])]),_:1}),o(n,{label:"任务参数",name:"progress",rules:[{required:!0,message:"请输入任务参数"}]},{default:p(()=>[A("div",Pe,[Ge,o(I,{style:{width:"50%"},disabled:a(r).progressKey>0,value:a(t).progress,"onUpdate:value":e[7]||(e[7]=i=>a(t).progress=i),min:1,max:9999,onChange:b},null,8,["disabled","value"]),v(" 次（周期内任务进度） ")]),(g(!0),f(h,null,be(a(t).task_params_arr,(i,Z)=>(g(),f("div",{key:Z,style:{display:"flex","align-items":"center","margin-bottom":"30px"}},[A("span",Ne,w(i.key+(Z+1))+": ",1),o(I,{style:{width:"50%"},value:i.value,"onUpdate:value":z=>i.value=z,min:1,max:9999,onChange:b},null,8,["value","onUpdate:value"]),o(F,{style:{"margin-left":"10px"},value:a(r).progressKey,"onUpdate:value":e[8]||(e[8]=z=>a(r).progressKey=z),onChange:z=>X(Z,i.is_progress)},{default:p(()=>[o(J,{value:i.is_progress},{default:p(()=>[v("设为进度")]),_:2},1032,["value"])]),_:2},1032,["value","onChange"])]))),128)),a(t).task_params_arr&&a(t).task_params_arr.length>0?(g(),f("div",qe,[Fe,v(" "+w(a(r).task_desc),1)])):k("",!0)]),_:1}),o(n,{label:"领奖设置",name:"cycle_type",rules:[{required:!0,message:"请选择领奖设置"}]},{default:p(()=>[o(B,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:p(()=>[o(l,{style:{width:"100%"},value:a(t).dimension,"onUpdate:value":e[9]||(e[9]=i=>a(t).dimension=i),options:a(r).trigger1,placeholder:"请选择维度"},null,8,["value","options"]),o(l,{value:a(t).cycle_type,"onUpdate:value":e[10]||(e[10]=i=>a(t).cycle_type=i),options:a(r).trigger2},null,8,["value","options"]),o(I,{style:{width:"100%"},value:a(t).cycle_times,"onUpdate:value":e[11]||(e[11]=i=>a(t).cycle_times=i),min:1,max:15e5,placeholder:"请输入周期次数"},null,8,["value"]),v("次 ")]),_:1})]),_:1}),o(n,{label:"跳转链接",name:"url",rules:[{required:!0,message:"请输入跳转链接"}]},{default:p(()=>[o(B,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:p(()=>[o(l,{value:a(t).url_target,"onUpdate:value":e[12]||(e[12]=i=>a(t).url_target=i),options:a(r).jumpType},null,8,["value","options"]),o(M,{style:{width:"100%"},value:a(t).url,"onUpdate:value":e[13]||(e[13]=i=>a(t).url=i),placeholder:"请输入跳转链接"},null,8,["value"])]),_:1})]),_:1}),o(n,{label:"奖励类型",name:"reward_type",rules:[{required:!0,message:"请选择奖励类型"}]},{default:p(()=>[o(l,{style:{width:"100%"},value:a(t).reward_type,"onUpdate:value":e[14]||(e[14]=i=>a(t).reward_type=i),options:a(r).rewardType},null,8,["value","options"])]),_:1}),a(t).reward_type===2?(g(),C(n,{key:0,label:"积分数量",name:"coin_num",rules:[{required:!0,message:"请输入积分数量"}]},{default:p(()=>[o(I,{style:{width:"100%"},value:a(t).coin_num,"onUpdate:value":e[15]||(e[15]=i=>a(t).coin_num=i),min:0,max:15e5,placeholder:"请输入积分数量"},null,8,["value"])]),_:1})):k("",!0),a(t).reward_type!==2?(g(),C(n,{key:1,label:"礼包名称（后台）",name:"pkg_id",rules:[{required:!0,message:"请选择礼包名称"}]},{default:p(()=>[o(l,{style:{width:"100%"},allowClear:"","filter-option":!1,showSearch:"",value:a(t).pkg_id,"onUpdate:value":e[16]||(e[16]=i=>a(t).pkg_id=i),options:a(r).optionsGiftsFilter,placeholder:"请选择礼包名称",onChange:H,onSearch:Q},null,8,["value","options"])]),_:1})):k("",!0),o(n,{label:"任务有效期",onChange:e[18]||(e[18]=i=>a(t).times=void 0),rules:[{required:!0,message:"请选择起止时间"}]},{default:p(()=>[o(F,{value:a(t).task_validity_type,"onUpdate:value":e[17]||(e[17]=i=>a(t).task_validity_type=i),options:[{label:"永久",value:1},{label:"定时",value:2}]},null,8,["value"])]),_:1}),a(t).task_validity_type===2?(g(),C(n,{key:2,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:p(()=>[o(R,{value:a(t).times,"onUpdate:value":e[19]||(e[19]=i=>a(t).times=i),isDisabledDate:!0},null,8,["value"])]),_:1})):k("",!0),a(r).isOnline?k("",!0):(g(),C(n,{key:3,"wrapper-col":{offset:10,span:12}},{default:p(()=>[o(y,{type:"primary",onClick:E,loading:a(S)},{default:p(()=>[v("保存")]),_:1},8,["loading"])]),_:1}))]),_:1},8,["model","rules"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const Ae=de(Be,[["__scopeId","data-v-a905eda4"]]),$e=ie({__name:"Index",setup(c){ae.extend(Le);const x=[{dataIndex:"id",key:"id",title:"任务ID",width:"100px"},{dataIndex:"task_category",key:"task_category",title:"任务类型",width:"100px"},{dataIndex:"lang_key",key:"lang_key",title:"任务描述",width:"130px"},{dataIndex:"filter_server",key:"filter",title:"服务器ID",width:"130px"},{dataIndex:"dimension",key:"dimension",title:"任务判定维度",width:"130px"},{dataIndex:"cycle_type",key:"cycle_type",title:"周期",width:"130px"},{dataIndex:"cycle_times",key:"cycle_times",title:"周期内次数",width:"130px"},{dataIndex:"progress",key:"progress",title:"单次进度",width:"130px"},{dataIndex:"url",key:"url",title:"跳转链接",width:"130px"},{dataIndex:"pkg_id",key:"pkg_id",title:"礼包名称（后台）",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"task_validity_type",key:"task_validity_type",title:"有效期",width:"200px"},{dataIndex:"game_project",key:"game_project",title:"类型",width:"130px"},{dataIndex:"action",key:"action",title:"操作",width:"130px",fixed:"right",align:"center"}],d=te({editVisible:!1,editId:0,searchParams:{task_category:null,vip_restriction:null},previewOpen:!1,previewData:{},optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4},{label:"限时任务",value:5}],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],trigger1:[{label:"账号",value:0},{label:"角色",value:1}],optionsGifts:[],optionsLang:[]}),O=V([]),P=V(),L=m=>P.value.requestTableData(!m);re(()=>{Y(),t({page:1,page_size:1e3}),r("task_desc")});const Y=()=>{le().then(m=>{m&&m.length>0&&(d.optionsGifts=m)})},t=m=>{se(m).then(u=>{u.data&&u.data.length>0&&(d.optionsLang=u.data.map(b=>({label:b.zh_cn,value:b.key})))})},r=async m=>{const u=await ye({key:m});return(u==null?void 0:u.zh_cn)||"-"},G=(m,u)=>{d.editVisible=m,d.editId=u||0},S=(m,u)=>ke(`确定要删除 ${u?"选中的":"此条"}数据吗？`,Ue,{petId:m}).then(()=>L()),$=(m,u,b)=>{O.value[b]=!0,u.status=1-m;const U=m===0?2:1;oe.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{O.value[b]=!1,Te(u.id,U).finally(()=>L(!0))},onCancel:()=>{O.value[b]=!1}})};return(m,u)=>{const b=_("a-select"),U=_("a-button"),N=_("a-space"),H=_("UploadBtn"),Q=_("PlusOutlined"),W=_("a-switch"),X=_("a-divider"),E=_("a-typography-link"),s=_("CustomTable"),e=pe("has");return g(),f(h,null,[o(s,{ref_key:"RefCustomTable",ref:P,"data-api":a(Ie),params:a(d).searchParams,columns:x},{top:p(()=>[o(N,{direction:"vertical"},{default:p(()=>[o(N,{wrap:"",style:{gap:"20px"}},{default:p(()=>[o(b,{style:{width:"215px"},allowClear:"",value:a(d).searchParams.task_category,"onUpdate:value":u[0]||(u[0]=l=>a(d).searchParams.task_category=l),options:a(d).optionsTaskCategory,placeholder:"请选择任务类型"},null,8,["value","options"]),o(b,{style:{width:"215px"},allowClear:"",value:a(d).searchParams.vip_restriction,"onUpdate:value":u[1]||(u[1]=l=>a(d).searchParams.vip_restriction=l),options:a(d).optionsVip,placeholder:"请选择VIP条件"},null,8,["value","options"]),o(U,{type:"primary",onClick:L},{default:p(()=>[v("搜索")]),_:1}),o(U,{onClick:u[2]||(u[2]=()=>{a(d).searchParams.task_category=null,a(d).searchParams.vip_restriction=null,L()})},{default:p(()=>[v("重置")]),_:1})]),_:1}),o(N,{wrap:"",style:{padding:"20px 0",gap:"20px"}},{default:p(()=>[ue(o(H,{ref:"uploadBtn",onUploadSuccess:L,downloadApi:a(Ve),fileType:"task-config",page:a(ne).TASKCONFIG,hideUploadBtn:!0},null,8,["downloadApi","page"]),[[e,"Operation"]]),o(U,{type:"primary",onClick:u[3]||(u[3]=l=>G(!0))},{icon:p(()=>[o(Q)]),default:p(()=>[v(" 新增任务 ")]),_:1})]),_:1})]),_:1})]),bodyCell:p(({column:l,record:n,index:T})=>{var q,K,I,J,F,B,M,R;return[l.key==="task_category"?(g(),f(h,{key:0},[v(w((q=a(d).optionsTaskCategory.find(y=>y.value===n[l.key]))==null?void 0:q.label),1)],64)):k("",!0),l.key==="lang_key"?(g(),f(h,{key:1},[v(w((K=a(d).optionsLang.find(y=>y.value===n[l.key]))==null?void 0:K.label),1)],64)):k("",!0),l.dataIndex==="filter_server"?(g(),f(h,{key:2},[v(w(n[l.key]&&((I=JSON.parse(n[l.key]))!=null&&I.server_max)?JSON.parse(n[l.key]).server_min+"-"+JSON.parse(n[l.key]).server_max:"-"),1)],64)):k("",!0),l.dataIndex==="filter_vip_restriction"?(g(),f(h,{key:3},[v(w(n[l.key]&&((J=JSON.parse(n[l.key]))!=null&&J.vip_restriction)?(F=a(d).optionsVip.find(y=>{var j;return y.value===((j=JSON.parse(n[l.key]))==null?void 0:j.vip_restriction)}))==null?void 0:F.label:"-"),1)],64)):k("",!0),l.dataIndex==="dimension"?(g(),f(h,{key:4},[v(w((B=a(d).trigger1.find(y=>y.value===n[l.key]))==null?void 0:B.label),1)],64)):k("",!0),l.dataIndex==="cycle_type"?(g(),f(h,{key:5},[v(w((M=a(d).optionsGift1.find(y=>y.value===n[l.key]))==null?void 0:M.label),1)],64)):k("",!0),l.dataIndex==="progress"?(g(),f(h,{key:6},[v(w(n[l.key]?n[l.key]:"无限制"),1)],64)):k("",!0),l.dataIndex==="pkg_id"?(g(),f(h,{key:7},[v(w((R=a(d).optionsGifts.find(y=>y.value==n[l.key]))==null?void 0:R.label),1)],64)):k("",!0),l.key==="status"?(g(),C(W,{key:8,checked:n.status,"onUpdate:checked":y=>n.status=y,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:a(O)[T],onClick:y=>$(y,n,T)},null,8,["checked","onUpdate:checked","loading","onClick"])):k("",!0),l.key==="task_validity_type"?(g(),f(h,{key:9},[v(w(n[l.key]===2?`${a(ae).utc(n.task_start_time*1e3).format("YYYY-MM-DD HH:mm:ss")} -
        ${a(ae).utc(n.task_end_time*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):k("",!0),l.key==="game_project"?(g(),f(h,{key:10},[v(w(n[l.key]==="funplus_zone"?"平台":"游戏"),1)],64)):k("",!0),l.key==="action"?(g(),C(N,{key:11},{split:p(()=>[o(X,{type:"vertical",style:{margin:"0"}})]),default:p(()=>[o(E,{onClick:y=>G(!0,n.id)},{default:p(()=>[v("编辑")]),_:2},1032,["onClick"]),o(E,{type:"danger",danger:"",onClick:y=>S(n.id,!1)},{default:p(()=>[v("删除")]),_:2},1032,["onClick"])]),_:2},1024)):k("",!0)]}),_:1},8,["data-api","params"]),a(d).editVisible?(g(),C(Ae,{key:0,"edit-id":a(d).editId,onClose:u[4]||(u[4]=l=>G(!1)),onRefresh:L},null,8,["edit-id"])):k("",!0)],64)}}});const Me=de($e,[["__scopeId","data-v-33732286"]]);export{Me as default};
