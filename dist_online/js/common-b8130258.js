import{bu as F,h as w,bv as _,_ as R,a as f,o as v,c as E,M as te,d as O,k as P,b as g,w as h,v as l,e as L,z as B,A as z,j as ae,u as b,m as de,bw as _e,bx as me,by as ge,bz as M,r as he,bA as fe,bB as $,J as K,a9 as ve,ab as x,F as A,l as T,s as ke,bC as ne,t as j,ac as q,L as Z,K as Y}from"./vendor-cc06403f.js";import{m as y,M as Ce}from"./antd-914dcf46.js";const ye=F("system",()=>{const e=w({asideStatus:!1,env:""});return{systemState:e,setAsideStatus:()=>{e.asideStatus=!e.asideStatus},setCrtEnv:s=>{e.env=s}}}),be=[{index:4,path:"/dailyCheck",name:"DailyCheck",redirect:"/dailyCheck/list",meta:{title:"签到活动",icon:"TagsOutlined",not_funplus_zone:!0},children:[{path:"/dailyCheck/list",name:"DailyCheckList",component:()=>_(()=>import("./dailycheck-a0daa51c.js").then(e=>e.I),["js/dailycheck-a0daa51c.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"月签到（旧）"}},{path:"/dailyCheck/detail/:id",name:"DailyCheckDetail",component:()=>_(()=>import("./dailycheck-a0daa51c.js").then(e=>e.D),["js/dailycheck-a0daa51c.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"奖励详情",hidden:!0,icon:"",activeMenu:"/dailyCheck/list"}},{path:"/dailyCheck/new",name:"DailyCheckNew",component:()=>_(()=>import("./DailyCheckNew-59457cef.js"),["js/DailyCheckNew-59457cef.js","js/shared/DailyCheckNew/DailyCheckNewDetail-c47bc7fd.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"月签到（新）"}},{path:"/dailyCheck/new/detail/:id",name:"DailyCheckNewDetail",component:()=>_(()=>import("./DailyCheckNewDetail-841a807b.js"),["js/DailyCheckNewDetail-841a807b.js","js/shared/DailyCheckNew/DailyCheckNewDetail-c47bc7fd.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"奖励详情",hidden:!0,icon:"",activeMenu:"/dailyCheck/new"}},{path:"/dailyCheck/weekly",name:"DailyCheckWeekly",component:()=>_(()=>import("./DailyCheckWeekly-c85c060a.js"),["js/DailyCheckWeekly-c85c060a.js","js/shared/DailyCheckWeekly/DailyCheckWeeklyDetail-1f853882.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"周签到"}},{path:"/dailyCheck/weekly/detail/:id",name:"DailyCheckWeeklyDetail",component:()=>_(()=>import("./DailyCheckWeeklyDetail-68da9e09.js"),["js/DailyCheckWeeklyDetail-68da9e09.js","js/shared/DailyCheckWeekly/DailyCheckWeeklyDetail-1f853882.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"奖励详情",hidden:!0,icon:"",activeMenu:"/dailyCheck/weekly"}},{path:"/dailyCheck/rule",name:"DailyCheckRule",component:()=>_(()=>import("./DailyCheckRule-3028ef8f.js"),["js/DailyCheckRule-3028ef8f.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"签到规则"}}]}],Le=Object.freeze(Object.defineProperty({__proto__:null,default:be},Symbol.toStringTag,{value:"Module"})),Pe=[{index:10,path:"/datamanage",name:"DataManage",redirect:"/datamanage/points",meta:{title:"数据明细",icon:"DatabaseOutlined"},children:[{path:"/datamanage/points",name:"DataManagePoints",component:()=>_(()=>import("./datamanage-92368bfd.js").then(e=>e.I),["js/datamanage-92368bfd.js","js/vendor-cc06403f.js"]),meta:{title:"积分明细",icon:""}},{path:"/datamanage/growth",name:"DataManageGrowth",component:()=>_(()=>import("./datamanage-92368bfd.js").then(e=>e.a),["js/datamanage-92368bfd.js","js/vendor-cc06403f.js"]),meta:{title:"成长值明细",icon:""}}]}],Ee=Object.freeze(Object.defineProperty({__proto__:null,default:Pe},Symbol.toStringTag,{value:"Module"})),Oe=[],Se=Object.freeze(Object.defineProperty({__proto__:null,default:Oe},Symbol.toStringTag,{value:"Module"})),De=[],we=Object.freeze(Object.defineProperty({__proto__:null,default:De},Symbol.toStringTag,{value:"Module"})),Re=[{index:3,path:"/minigame",name:"Minigame",redirect:"/minigame/list",meta:{title:"小游戏",icon:"TrophyOutlined"},children:[{path:"/minigame/list",name:"MinigameList",component:()=>_(()=>import("./minigame-53d80b94.js"),["js/minigame-53d80b94.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"小游戏",icon:"",breadcrumb:!1}}]}],Ie=Object.freeze(Object.defineProperty({__proto__:null,default:Re},Symbol.toStringTag,{value:"Module"})),Te={};function $e(e,t){const o=f("router-view");return v(),E(o)}const Ae=R(Te,[["render",$e]]),Ge=[{index:2,path:"/operation",name:"Operation",redirect:"/operation/sdkvajra",meta:{title:"运营位",icon:"FlagOutlined"},children:[{path:"/operation/sdkvajra",name:"OperationSDKVajra",component:()=>_(()=>import("./operation-61199f70.js").then(e=>e.I),["js/operation-61199f70.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"SDK金刚区",icon:""}},{path:"/operation/h5vajra",name:"OperationH5Vajra",component:()=>_(()=>import("./operation-61199f70.js").then(e=>e.a),["js/operation-61199f70.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"H5金刚区",icon:""}},{path:"/operation/banner",name:"OperationBanner",component:te(Ae),redirect:"/operation/banner/group",meta:{title:"活动Banner",icon:""},children:[{path:"/operation/banner/group",name:"OperationBannerList",component:()=>_(()=>import("./operation-61199f70.js").then(e=>e.b),["js/operation-61199f70.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"活动Banner",icon:"",breadcrumb:!1}},{path:"/operation/banner/detail/:id",name:"OperationBannerDetail",component:()=>_(()=>import("./operation-61199f70.js").then(e=>e.D),["js/operation-61199f70.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"Banner详情",icon:"",hidden:!0,permission:"OperationBannerList"}}]},{path:"/operation/sdkiconspush",name:"OperationSDKIconsPush",component:()=>_(()=>import("./operation-61199f70.js").then(e=>e.c),["js/operation-61199f70.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"SDK特殊图标推送",icon:"",not_funplus_zone:!0}},{path:"/operation/sdkads",name:"OperationSDKAds",component:()=>_(()=>import("./operation-61199f70.js").then(e=>e.d),["js/operation-61199f70.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"SDK广告位",icon:"",not_funplus_zone:!0}}]}],Me=Object.freeze(Object.defineProperty({__proto__:null,default:Ge},Symbol.toStringTag,{value:"Module"})),je=[{index:1,path:"/platform",name:"Platform",redirect:"/platform/entrance",meta:{title:"平台配置",icon:"GoldOutlined",funplus_zone:!0},children:[{path:"/platform/game",name:"PlatformGame",component:()=>_(()=>import("./platform-47fb962c.js").then(e=>e.I),["js/platform-47fb962c.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"游戏接入",icon:""}},{path:"/platform/entrance",name:"PlatformEntrance",component:()=>_(()=>import("./platform-47fb962c.js").then(e=>e.a),["js/platform-47fb962c.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"入口管理",icon:""}},{path:"/platform/rule",name:"PlatformRule",component:()=>_(()=>import("./platform-47fb962c.js").then(e=>e.b),["js/platform-47fb962c.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"通用规则",icon:""}},{path:"/platform/combinerule",name:"CombineRule",component:()=>_(()=>import("./Index-9946b5a3.js"),["js/Index-9946b5a3.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css"]),meta:{title:"融合开关",icon:""}}]}],Fe=Object.freeze(Object.defineProperty({__proto__:null,default:je},Symbol.toStringTag,{value:"Module"})),Ke=[{index:7,path:"/points",name:"Points",redirect:"/points/task",meta:{title:"积分系统",icon:"ProjectOutlined"},children:[{path:"/points/task",name:"PointsTask",component:()=>_(()=>import("./PointsTask-675dee43.js"),["js/PointsTask-675dee43.js","js/antd-914dcf46.js","js/vendor-cc06403f.js","css/antd-25a63267.css","css/Index-70edb989.css"]),meta:{title:"任务体系",icon:""}},{path:"/points/store",name:"PointsStore",component:()=>_(()=>import("./pointsStore-af054c05.js"),["js/pointsStore-af054c05.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css","css/Index-cc3666b8.css"]),meta:{title:"积分商城",icon:""}}]}],Ue=Object.freeze(Object.defineProperty({__proto__:null,default:Ke},Symbol.toStringTag,{value:"Module"})),Ne=[{index:5,path:"/pushmessage",name:"Pushmessage",redirect:"/pushmessage/list",meta:{title:"消息推送",icon:"MessageOutlined"},children:[{path:"/pushmessage/list",name:"PushmessageList",component:()=>_(()=>import("./pushMessage-bdcfed35.js"),["js/pushMessage-bdcfed35.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css","css/Index-95488f3c.css"]),meta:{title:"消息推送",icon:"",breadcrumb:!1}}]}],Ve=Object.freeze(Object.defineProperty({__proto__:null,default:Ne},Symbol.toStringTag,{value:"Module"})),Be=[{index:8,path:"/resource",name:"Resource",redirect:"/resource/language",meta:{title:"资源管理",icon:"DropboxOutlined"},children:[{path:"/resource/language",name:"ResourceLanguage",component:()=>_(()=>import("./resource-93171946.js").then(e=>e.I),["js/resource-93171946.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css","css/resource-1ccdf5b9.css"]),meta:{title:"多语言管理",icon:""}},{path:"/resource/gift",name:"ResourceGift",component:()=>_(()=>import("./resource-93171946.js").then(e=>e.a),["js/resource-93171946.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css","css/resource-1ccdf5b9.css"]),meta:{title:"礼包列表",icon:""}},{path:"/resource/props",name:"ResourceProps",component:()=>_(()=>import("./resource-93171946.js").then(e=>e.b),["js/resource-93171946.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css","css/resource-1ccdf5b9.css"]),meta:{title:"道具表管理",icon:""}}]}],ze=Object.freeze(Object.defineProperty({__proto__:null,default:Be},Symbol.toStringTag,{value:"Module"})),xe=[{index:6,path:"/rights",name:"Rights",redirect:"/rights/entrance",meta:{title:"权益配置",icon:"RightSquareOutlined"},children:[{path:"/rights/entrance",name:"RightsEntrance",component:()=>_(()=>import("./rights-7bef7457.js").then(e=>e.I),["js/rights-7bef7457.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css","css/rights-0c82a52c.css"]),meta:{title:"权益入口",icon:""}},{path:"/rights/gift",name:"RightsGift",component:()=>_(()=>import("./rights-7bef7457.js").then(e=>e.a),["js/rights-7bef7457.js","js/vendor-cc06403f.js","js/antd-914dcf46.js","css/antd-25a63267.css","css/rights-0c82a52c.css"]),meta:{title:"专属礼包",icon:"",not_funplus_zone:!0}}]}],We=Object.freeze(Object.defineProperty({__proto__:null,default:xe},Symbol.toStringTag,{value:"Module"})),He=[{index:9,path:"/user",name:"User",redirect:"/user/list",meta:{title:"用户管理",icon:"UsergroupAddOutlined"},children:[{path:"/user/list",name:"UserList",component:()=>_(()=>import("./user-4123a6c8.js"),["js/user-4123a6c8.js","js/vendor-cc06403f.js"]),meta:{title:"用户管理",icon:"",breadcrumb:!1}}]}],qe=Object.freeze(Object.defineProperty({__proto__:null,default:He},Symbol.toStringTag,{value:"Module"})),J=Object.assign({"./dailyCheck.ts":Le,"./dataManage.ts":Ee,"./dev.ts":Se,"./home.ts":we,"./minigame.ts":Ie,"./operation.ts":Me,"./platformConf.ts":Fe,"./points.ts":Ue,"./pushMessage.ts":Ve,"./resource.ts":ze,"./rights.ts":We,"./users.ts":qe});let U=[];for(const e in J)U=U.concat(J[e].default);const Ze=U,Ye="/gif/kit-risk-management-forecasting-and-assessment-6a89fdde.gif",I=e=>(B("data-v-1e705d2e"),e=e(),z(),e),Je={class:"wrapper darkAmber"},Qe=I(()=>l("div",{class:"view"},[l("img",{src:Ye,alt:""})],-1)),Xe=I(()=>l("div",{class:"panel-logo"},[l("svg",{width:"100px",height:"25px",viewBox:"0 0 300 75",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",class:"w-100"},[l("g",{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[l("g",{fill:"#FF5A00"},[l("path",{d:"M55.8479648,42.9125049 L55.8479648,62.9227814 C55.8479648,64.4627289 54.6047234,65.7133712 53.0738887,65.7133712 L11.702407,65.7133712 C10.0757079,65.7133712 8.7965173,64.3090355 8.9403139,62.6786801 L12.0798731,26.5908697 C12.2056951,25.1503709 13.4040002,24.0443812 14.8419662,24.0443812 L37.0914951,24.0443812 C36.5462663,22.6641543 36.2466901,21.1573563 36.2466901,19.5812457 C36.2466901,18.0051351 36.5462663,16.4983371 37.0914951,15.1181102 L14.3326866,15.1181102 C8.58082239,15.1181102 3.78161069,19.542069 3.27832257,25.3070778 L0.0428989523,62.5099188 C-0.523300181,69.0343538 4.58747083,74.6456693 11.097263,74.6456693 L53.6281047,74.6456693 C59.7544393,74.6456693 64.7244094,69.6491274 64.7244094,63.4833102 L64.7244094,42.9125049 C63.3523502,43.4609793 61.8544689,43.7623389 60.287685,43.7623389 C58.7209012,43.7623389 57.2200241,43.4609793 55.8479648,42.9125049 Z",id:"Path"}),l("path",{d:"M73.9397415,13.4673005 L70.0663235,13.4673005 C68.027997,13.4673005 66.3752191,11.8145226 66.3752191,9.7761962 L66.3752191,5.9027782 C66.3752191,2.64205363 63.7331655,0 60.4724409,0 C57.2117164,0 54.5696627,2.64205363 54.5696627,5.9027782 L54.5696627,9.7761962 C54.5696627,11.8145226 52.9168848,13.4673005 50.8785584,13.4673005 L47.0051404,13.4673005 C43.7444158,13.4673005 41.1023622,16.1093542 41.1023622,19.3700787 C41.1023622,22.6308033 43.7444158,25.2728569 47.0051404,25.2728569 L50.8785584,25.2728569 C52.9168848,25.2728569 54.5696627,26.9256348 54.5696627,28.9639613 L54.5696627,32.8373793 C54.5696627,36.0981038 57.2117164,38.7401575 60.4724409,38.7401575 C63.7331655,38.7401575 66.3752191,36.0981038 66.3752191,32.8373793 L66.3752191,28.9639613 C66.3752191,26.9256348 68.027997,25.2728569 70.0663235,25.2728569 L73.9397415,25.2728569 C77.2004661,25.2728569 79.8425197,22.6308033 79.8425197,19.3700787 C79.8425197,16.1093542 77.2004661,13.4673005 73.9397415,13.4673005 Z",id:"Path"}),l("path",{d:"M290.974807,40.287923 C287.781185,38.5244652 285.229872,37.0819087 285.229872,34.5986722 C285.229872,32.2743869 286.986513,30.0310767 290.81647,30.0310767 C293.290108,30.0310767 296.002745,31.1527318 296.961727,31.9534856 L298.718369,26.1862588 C298.079047,25.6254313 294.649414,23.6220472 290.338472,23.6220472 C283.075895,23.6220472 277.88962,27.2269388 277.88962,34.1967958 C277.88962,40.0449977 281.322241,43.1700368 285.23286,45.0924457 C289.463139,47.1738058 292.815098,48.8562884 292.815098,51.9033516 C292.815098,55.349292 289.941137,57.2717009 286.272504,57.2717009 C283.398544,57.2717009 280.207909,55.5892183 278.770929,54.4675631 L276.377953,61.5183952 C279.96891,63.6807304 282.442548,64.7244094 286.75349,64.7244094 C294.655389,64.7244094 300,59.3560601 300,52.227252 C299.994025,45.5752973 295.444085,42.7711594 290.974807,40.287923 Z",id:"Path"}),l("path",{d:"M199.113788,24.0944882 L195.96498,24.0944882 L190.743157,24.0944882 L188.503937,24.0944882 L188.503937,64.7244094 L195.96498,64.7244094 L195.96498,50.9325182 L199.11681,50.9325182 C208.070667,50.9325182 213.543307,46.0443676 213.543307,37.5150128 C213.540285,28.9826388 208.067645,24.0944882 199.113788,24.0944882 Z M199.113788,43.4780131 L195.96498,43.4780131 L195.96498,31.5489933 L199.11681,31.5489933 C202.930434,31.5489933 205.251244,33.6201924 205.251244,37.5119936 C205.248223,41.406814 202.927412,43.4780131 199.113788,43.4780131 Z",id:"Shape","fill-rule":"nonzero"}),l("polygon",{id:"Path",points:"226.600431 24.0944882 219.212598 24.0944882 219.212598 64.7244094 221.839782 64.7244094 226.600431 64.7244094 240.472441 64.7244094 240.472441 57.6412709 226.600431 57.6412709"}),l("polygon",{id:"Path",points:"172.128253 48.822069 159.796076 24.0944882 151.653543 24.0944882 151.653543 64.7244094 159.055846 64.7244094 159.055846 39.9998479 171.388023 64.7244094 179.527559 64.7244094 179.527559 24.0944882 172.128253 24.0944882"}),l("path",{d:"M135.249136,51.9500321 C135.249136,55.7694254 132.854357,58.0937472 128.976378,58.0937472 C125.098399,58.0937472 122.70362,55.7694254 122.70362,51.9500321 L122.70362,24.0944882 L115.275591,24.0944882 L115.275591,50.7485272 C115.275591,59.7159301 120.474308,65.1968504 128.976378,65.1968504 C137.478448,65.1968504 142.677165,59.7159301 142.677165,50.7485272 L142.677165,24.0944882 L135.249136,24.0944882 L135.249136,51.9500321 Z",id:"Path"}),l("path",{d:"M264.225514,51.9500321 C264.225514,55.7694254 261.830735,58.0937472 257.952756,58.0937472 C254.074777,58.0937472 251.679998,55.7694254 251.679998,51.9500321 L251.679998,24.0944882 L244.251969,24.0944882 L244.251969,50.7485272 C244.251969,59.7159301 249.450686,65.1968504 257.952756,65.1968504 C266.454826,65.1968504 271.653543,59.7159301 271.653543,50.7485272 L271.653543,24.0944882 L264.225514,24.0944882 L264.225514,51.9500321 Z",id:"Path"}),l("polygon",{id:"Path",points:"88.9916386 24.0944882 86.9291339 24.0944882 86.9291339 64.7244094 94.3523468 64.7244094 94.3523468 47.931393 107.055091 47.931393 107.055091 40.8482545 94.3523468 40.8482545 94.3523468 32.4819387 107.716535 32.4819387 107.716535 24.0944882 94.3523468 24.0944882"})])])]),L(" - "),l("span",{class:"sys-name"},"FunZone")],-1)),et=I(()=>l("div",{class:"panel-style-logo"},null,-1)),tt=I(()=>l("div",{class:"panel-header"},[l("p",null,"私域管理平台")],-1)),at=I(()=>l("i",{class:"icon-feishu"},null,-1)),nt=I(()=>l("span",null,"登录",-1)),ot=O({__name:"Index",setup(e){const t=()=>{location.href="https://admin-center.funplus.com/backend/admin/loginSso?system=1&referer="+encodeURIComponent(`${location.origin}/login`)};return(o,s)=>{const n=f("kinesis-element"),c=f("kinesis-container");return v(),P("div",Je,[g(c,{class:"kinesis-wrap"},{default:h(()=>[g(n,{strength:-8,class:"kinesis-style kinesis-style-1"}),g(n,{strength:10,class:"kinesis-style kinesis-style-2"}),g(n,{strength:-5,class:"kinesis-style kinesis-style-3"}),g(n,{strength:-8,class:"kinesis-style kinesis-style-4"}),g(n,{strength:10,class:"kinesis-style kinesis-style-5"}),g(n,{strength:-5,class:"kinesis-style kinesis-style-6"}),g(n,{strength:-8,class:"kinesis-style kinesis-style-7"}),g(n,{strength:10,class:"kinesis-style kinesis-style-8"}),g(n,{strength:5,class:"kinesis-view"},{default:h(()=>[Qe]),_:1}),l("div",{class:"glass-container kinesis-panel"},[Xe,et,tt,l("div",{class:"panel-btn-main",onClick:t},[at,L(" - "),nt])])]),_:1})])}}});const st=R(ot,[["__scopeId","data-v-1e705d2e"]]),it=O({__name:"Error",setup(e){const t=ae();return(o,s)=>{const n=f("a-button"),c=f("a-space"),i=f("a-result");return v(),E(i,{status:"403",title:"403"},{subTitle:h(()=>[L(" 抱歉，您没有当前页面的权限或页面不存在，请飞书联系管理员: 谷翼涵！ ")]),extra:h(()=>[g(c,null,{default:h(()=>[g(n,{type:"primary",onClick:s[0]||(s[0]=p=>b(t).replace("/"))},{default:h(()=>[L("回到首页")]),_:1})]),_:1})]),_:1})}}});const rt=R(it,[["__scopeId","data-v-11608d83"]]),ct=O({__name:"Redirect",setup(e){const t=de();return ae().replace(t.fullPath.replace("/redirect","")),(s,n)=>(v(),P("div"))}}),lt=[{path:"/login",name:"Login",component:st,meta:{hidden:!0,title:"登录"}},{path:"/redirect/:char(\\S+)+",name:"Redirect",component:te(ct),meta:{hidden:!0}},{path:"/error",redirect:"/403",meta:{hidden:!0},children:[{path:"/403",name:"Error",component:rt,meta:{hidden:!0}}]}],pt=Object.freeze(Object.defineProperty({__proto__:null,default:lt},Symbol.toStringTag,{value:"Module"})),Q=Object.assign({"./userManager.ts":pt});let N=[];for(const e in Q)N=N.concat(Q[e].default);const ut=N,oe=[...ut],dt=[...Ze.sort((e,t)=>e.index-t.index)],D=_e({history:me("/"),routes:oe}),k={MULTILINGUAL:"multilingual",PLATFORM_PROPS:"platformProps",DAILY_CHECK:"dailyCheck",LADDERCONFIGS:"ladderConfigs",RIGHTGIFT:"rightGift",TASKCONFIG:"taskConfig",TASKRULE:"taskRule",PRODUCTCONFIG:"productConfig",GIFTCONFIG:"giftConfig",DAILY_CHECK_WEEKLY:"dailyCheckWeekly",DAILY_CHECK_NEW:"dailyCheckNew"};var G=(e=>(e[e.PAGE=1]="PAGE",e[e.BUTTON=3]="BUTTON",e))(G||{}),_t=(e=>(e[e.CLOSE=0]="CLOSE",e[e.OPEN=1]="OPEN",e))(_t||{});const ia=["/login","/403","/error"],ra=[{value:1,img:new URL("/jpeg/banner_type_1-4ffe5097.jpeg",self.location).href,label:"样式一",width:686,height:292},{value:3,img:new URL("/jpeg/banner_type_2-40c10cde.jpeg",self.location).href,label:"样式二",width:686,height:292}],se=(e=[],t=[])=>{const o=localStorage.getItem("crtGame"),s=[];return t.forEach((n,c)=>{e.forEach((i,p)=>{var u,m;if(!(o==="funplus_zone"&&((u=n.meta)!=null&&u.not_funplus_zone))&&!(o!=="funplus_zone"&&((m=n.meta)!=null&&m.funplus_zone))&&n.name&&i.component_name===n.name&&i.category===G.PAGE){n.meta.id=i.id,n.meta.sort=i.sort,n.key=i.path,i._child=i._child||[];const d=i._child.filter(C=>C.category===G.BUTTON);n.meta.rule={button:d.map(C=>C.component_name.trim())};const r=i._child.filter(C=>C.category===G.PAGE);n.children=se(r,n.children),n.redirect=n.children[0]?n.children[0].path:"",s.push(n)}})}),s.sort((n,c)=>c.meta.sort-n.meta.sort)},ie=e=>{let t={};return e.forEach(o=>{t[o.name]=o.meta.id,o.children&&o.children.length&&(t=Object.assign(t,ie(o.children)))}),t},re=F("permission",()=>{const e=w({routes:[],dynamicRoutes:[],commonBtnPromissions:[],permissionMap:{},subRoutes:[]}),t=i=>{e.permissionMap=i},o=i=>{e.routes=i.concat(oe),e.dynamicRoutes=i};return{permissionState:e,setPermissionMap:t,setRoutes:o,setBtnPromise:i=>{i.forEach(p=>{p.name==="通用配置管理"&&p._child&&p._child.forEach(u=>e.commonBtnPromissions.push(u.component_name))})},SET_ROUTES:i=>{const p=se(i,dt);t(ie(p)),o(p)},setSubRoutes:i=>{e.subRoutes=i.filter(p=>{var u;return!((u=p.meta)!=null&&u.hidden)})}}}),mt=e=>{const t=new Blob([e.data],{type:e.headers["content-type"]});if(e.headers["content-type"]==="application/json"){const o=new FileReader;o.readAsText(t,"utf-8"),o.onload=function(){e=JSON.parse(o.result)}}else{const o=(e.headers["content-disposition"]||e.headers["Content-Disposition"]||"").split("filename=")[1],s=document.createElement("a");s.href=window.URL.createObjectURL(t),s.download=o,s.click()}},W=ge.create({timeout:6e4});W.interceptors.request.use(e=>e,e=>Promise.reject(e));W.interceptors.response.use(async e=>{if(e.status===200){if(e.config.responseType==="arraybuffer")return mt(e),Promise.resolve("");const t=e.data;if(t.errCode===0||t.code===200){const{setCrtEnv:o}=ye();return t.env&&o(t.env),t.data}else{if(t.code===403)y.error({content:"登录已失效，请重新登录！",onClose:()=>{D.replace("/login")}});else{const o=typeof t.msg=="string"?t.msg:"",s=typeof t.errMsg=="string"?t.errMsg:"",n=o||s;n&&y.error(n)}return Promise.reject(e.data)}}else return X(e),Promise.reject(e.data),e},e=>{const{response:t}=e;return t?(X(t),Promise.reject(t.data)):(y.error("网络连接异常,请稍后再试!"),Promise.reject("网络连接异常,请稍后再试!"))});const X=e=>{switch(e.status){case 401:break;case 403:break;case 404:y.error("网络连接异常,请稍后再试!");break;case 50:break;case 30:y.error({content:"登录已失效，请重新登录！",onClose:()=>{D.replace("/login")}});break;default:y.error(e.msg||e.errMsg)}},ca={isShow:(e,t="button")=>{const o=D.currentRoute;return(o.value.meta.rule&&o.value.meta.rule[t]||[]).indexOf(e)>-1},checkBtnPromission:e=>{const{permissionState:t}=M(re());return t.value.commonBtnPromissions.indexOf(e)>-1}},V=e=>e?/^[a-zA-Z0-9_]*$/.test(e):!1,gt=e=>Object.prototype.toString.call(e)==="[object FormData]",la=(e,t,o)=>new Promise((s,n)=>{Ce.confirm({title:"提示",content:e,onOk:()=>new Promise((c,i)=>{t(o).then(()=>{y.success("操作成功"),c("操作成功"),s("操作成功")}).catch(()=>i())}),onCancel:()=>{n()}})}),pa=(e,t)=>{const o={f_os:"all",f_channel:"all",lang:"all",f_s_ids:"0-99999",f_lv_ids:"0-99999"};t.forEach(s=>{e.is_filter;const n=o[s];e[s]=e.is_filter===1?n:""})},a={get(e,t={}){return a.request("GET",e,{params:S(t)})},post(e,t={}){return a.request("POST",e,{data:S(t)})},put(e,t={}){return a.request("PUT",e,{data:S(t)})},delete(e,t={}){return a.request("DELETE",e,{data:S(t)})},downfile(e,t={}){return a.request("POST",e,{data:S(t),responseType:"arraybuffer"})},downfileGet(e,t={}){return a.request("GET",e,{params:S(t),responseType:"arraybuffer"})},request(e="GET",t,o={}){const{userState:s}=M(ce());return t.startsWith("/api")&&s.value.crt_game&&(t=s.value.crt_game.api_server_url+t),t.startsWith("/backend")&&(t=t.replace("/backend","https://admin-center.funplus.com/backend")),new Promise((n,c)=>{W({method:e,url:t,...o}).then(i=>{n(i)}).catch(i=>{c(i)})})}},S=(e={})=>{var n,c;const{userState:t}=M(ce()),{permissionState:o}=M(re()),s={admin_project:(n=t.value.crt_game)==null?void 0:n.game_project,game_project:(c=t.value.crt_game)==null?void 0:c.game_project,system:"funplus_zone",ticket:t.value.ticket,permission_id:D.currentRoute.value.meta.permission&&o.value.permissionMap[D.currentRoute.value.meta.permission]||D.currentRoute.value.meta.id||"",user_id:t.value.userInfo.id,user_name:t.value.userInfo.username};return gt(e)?Object.keys(s).forEach(i=>{e.append(i,s[i])}):e={...s,...e},e},ht=e=>a.get("/backend/admin/publicSystemGame",{...e,is_admin:!0}),ft=e=>a.get("/backend/admin/publicChildPermission",{...e,is_admin:!0}),ce=F("user",()=>{const e=w({isLogin:!1,ticket:"",loading:!1,userInfo:{email:"",id:void 0,permission_list:[],route_path:[],username:""},commonBtnPromissions:[],game_infos:[]}),t=p=>{e.isLogin=!0,e.userInfo={...p}},o=()=>{const p=localStorage.getItem("ticket");localStorage.removeItem("ticket"),location.href=`https://admin-center.funplus.com/backend/admin/publicLogout?system=1&referer=&ticket=${p}`+encodeURIComponent(`${location.origin}/login`)},s=p=>{e.ticket=p},n=p=>{p.forEach(u=>{u.component==="CommonPromission"&&u._child&&u._child.forEach(m=>{e.commonBtnPromissions.push(m.component)})})};return{userState:e,login:t,logout:o,setTicket:s,saveBtnPromis:n,FETCH_PERMISSION:async()=>new Promise((p,u)=>{if(e.userInfo.permission_list.length)return p("success");e.ticket===""&&u(new Error("未检测到ticket!"));const m={ticket:e.ticket};ft(m).then(d=>{n(d.permission_list),t(d),p("success")}).catch(()=>{u(new Error("权限请求失败"))})}),FETCH_GAME_PERMISSION:async()=>new Promise((p,u)=>{if(e.game_infos,e.ticket==="")return u(new Error("未检测到ticket!"));const m={ticket:e.ticket};ht(m).then(d=>{if(e.game_infos=d.map(r=>({...r,gmUrl:{VITE_APP_SYSTEM:"funplus_zone",VITE_APP_ADMIN_CENTER_API:"https://admin-center.funplus.com/backend",BASE_URL:"/",MODE:"online",DEV:!1,PROD:!0,SSR:!1}[`VITE_APP_${r.game_project.toUpperCase()}_GM_URL`]})),e.game_infos,e.game_infos.length){const r=localStorage.getItem("crtGame"),C=e.game_infos.find(ue=>ue.game_project===r),pe=e.game_infos[0]||{},H=C||pe;e.crt_game=H,localStorage.setItem("crtGame",H.game_project),p("success")}else localStorage.removeItem("ticket"),u("no game infos")}).catch(()=>{u(new Error("权限请求失败"))})})}}),ee={[k.PLATFORM_PROPS]:"/api/items/import",[k.MULTILINGUAL]:"/api/lang/upload",[k.DAILY_CHECK]:"/api/active_checkin/update_reward",[k.LADDERCONFIGS]:"/api/vip-right-config/upload-ladder-configs",[k.RIGHTGIFT]:"/api/member-gift/import",[k.TASKCONFIG]:"/api/task-config/import",[k.PRODUCTCONFIG]:"/api/points-mall-product/import",[k.TASKRULE]:"/api/task-config/upload-rule-desc",[k.GIFTCONFIG]:"/api/gift-package/import",[k.DAILY_CHECK_WEEKLY]:"/api/active-checkin-weekly/update-config",[k.DAILY_CHECK_NEW]:"/api/active-checkin-v2/update-config"},ua=(e,t)=>{if(e===k.DAILY_CHECK_WEEKLY||e===k.DAILY_CHECK_NEW){const o=t.get("id");return a.put(`${ee[e]}/${o}`,t)}return a.post(ee[e],t)},da=e=>a.get("/api/resource/log/list",e),vt=()=>a.get("/api/common/public-enums"),_a=e=>a.downfile("/api/common/downloadTemplate",{file_type:e}),ma=F("config",()=>{const e=he({langs:[],channels:[],platform:[],show_pages:[],prize_type:[],game_projects:[],game_channels:[],task_events:[]});return{configState:e,getConfItem:(n,c)=>e.value[n].find(i=>i.value===c),FETCH_GLOBAL_CONFIG:async(n=!1)=>{if(!(!n&&e.value.langs.length))try{const c=await vt();e.value=c}catch(c){console.error("获取全局配置失败:",c)}},getGameChannel:n=>{const c=e.value.game_channels.find(i=>i.game_project===n);return(c==null?void 0:c.sdk_pkg_channels)||[]}}}),ga=fe(),kt={$on:(...e)=>$.on(...e),$off:(...e)=>$.off(...e),$once:(...e)=>$.once(...e),$emit:(...e)=>$.emit(...e)},ha=e=>a.get("/api/lang",e),fa=()=>a.downfile("/api/lang/export"),va=e=>a.get("/api/lang/keySearch",e),ka=e=>a.get("/api/lang/keyDetail",e),Ca=e=>a.post("/api/common/upload-img",e),Ct=e=>a.get("/api/resource/img/list",e),ya=e=>a.get("/api/resource/img/first",e),yt=e=>a.post("/api/resource/img/upload_check",e),bt=e=>a.post("/api/resource/img/upload_batch",e),ba=e=>a.get("/api/gift-package",e),La=()=>a.get("/api/gift-package/enum"),Pa=e=>a.post("/api/gift-package",e),Ea=(e,t)=>a.put(`/api/gift-package/${e}`,t),Oa=e=>a.get(`/api/gift-package/${e}`),Sa=({id:e})=>a.delete(`/api/gift-package/${e}`),Da=()=>a.downfile("/api/gift-package/export"),wa=e=>a.get("/api/item-i18n",e),Ra=({search_key:e,type:t,game_project:o})=>a.get("/api/item-i18n/search",{search_key:e,type:t,game_project:o}),Ia=e=>a.get("/api/item-i18n",e),Ta=e=>a.post("/api/item-i18n",e),$a=(e,t)=>a.put(`/api/item-i18n/${e}`,t),Aa=e=>a.get(`/api/item-i18n/${e}`),Ga=({id:e})=>a.delete(`/api/item-i18n/${e}`),Ma=e=>a.get("/api/platform-item",e),ja=e=>a.get(`/api/platform-item/${e}`),Fa=e=>a.post("/api/platform-item",e),Ka=e=>a.put(`/api/platform-item/${e.id}`,e),Ua=({id:e})=>a.delete(`/api/platform-item/${e}`),Na=({id:e})=>a.put(`/api/platform-item/copy/${e}`),Lt=O({name:"List",emits:["submit","close"],setup(e,t){const o=K("setStep"),s=w({searchParams:{search:"",page:1,page_size:20},crtItem:{},loading:!1,list:[],total:0,imgTypes:[]}),n=()=>{s.searchParams.page=1,c()},c=()=>{const d={...s.searchParams};s.loading=!0,Ct(d).then(r=>{s.list=r.data,s.searchParams.page=r.current_page,s.total=r.total}).finally(()=>{s.loading=!1})},i=d=>{s.searchParams.page=d,c()},p=d=>{s.crtItem=d};ve(()=>{c()});const u=()=>{if(!s.crtItem.id)return y.error("请选择一张图片！");t.emit("submit",s.crtItem)},m=()=>t.emit("close");return{...x(s),search:n,setCrt:p,handleCurrentChange:i,setStep:o,close:m,confirm:u}}});const Pt=e=>(B("data-v-644e8c9d"),e=e(),z(),e),Et={class:"img-list"},Ot=Pt(()=>l("div",{class:"left"}," asd ",-1)),St={class:"right"},Dt={class:"filter"},wt={class:"result"},Rt={class:"list"},It=["onClick"],Tt={class:"img"},$t=["src"],At={class:"name"},Gt={class:"pre"},Mt={class:"last"},jt={class:"pagination-wrap"},Ft={class:"footer"};function Kt(e,t,o,s,n,c){const i=f("a-divider"),p=f("a-input-search"),u=f("a-button"),m=f("a-pagination"),d=f("a-space");return v(),P(A,null,[l("div",Et,[e.imgTypes.length>1?(v(),P(A,{key:0},[Ot,g(i,{type:"vertical",style:{height:"100%"}})],64)):T("",!0),l("div",St,[l("div",Dt,[g(p,{value:e.searchParams.search,"onUpdate:value":t[0]||(t[0]=r=>e.searchParams.search=r),placeholder:"请输入图片名称",style:{width:"300px"},"enter-button":"查询",allowClear:"",onSearch:e.search},null,8,["value","onSearch"]),g(u,{onClick:t[1]||(t[1]=r=>e.setStep(2))},{default:h(()=>[L("上传图片")]),_:1})]),l("div",wt,[l("div",Rt,[(v(!0),P(A,null,ke(e.list,r=>(v(),P("div",{class:ne(["img-item",{active:e.crtItem.id===r.id}]),key:r.id,onClick:C=>e.setCrt(r)},[l("div",Tt,[l("img",{src:r.preview_img},null,8,$t)]),l("div",At,[l("div",Gt,j(r.img_key.length>5?r.img_key.substring(0,r.img_key.length-2):r.img_key),1),l("div",Mt,j(r.img_key.length>5&&r.img_key.substring(r.img_key.length-2)||""),1)])],10,It))),128))]),l("div",jt,[g(m,{current:e.searchParams.page,"onUpdate:current":t[2]||(t[2]=r=>e.searchParams.page=r),"default-page-size":e.searchParams.page_size,total:e.total,onCurrentChange:e.handleCurrentChange},null,8,["current","default-page-size","total","onCurrentChange"])])])])]),l("div",Ft,[g(d,{wrap:""},{default:h(()=>[g(u,{onClick:e.close},{default:h(()=>[L("取消")]),_:1},8,["onClick"]),g(u,{type:"primary",onClick:e.confirm},{default:h(()=>[L("确定")]),_:1},8,["onClick"])]),_:1})])],64)}const Ut=R(Lt,[["render",Kt],["__scopeId","data-v-644e8c9d"]]),Nt=O({name:"Upload",emits:["finish"],setup(e,t){const o=K("setStep"),s=K("isOnlyUpload"),n=w({list:[],loading:!1}),c=(m,d)=>!1,i=()=>{const m={};for(const d in n.list){const r=n.list[d];if(r.size&&r.size>1024*1024*2)return`文件(${r.name})大小超过大小限制`;if(!V(r.name.split(".")[0]))return`文件(${r.name})不符合命名规则, 文件名只包含数字、字母、下划线`;if(m[r.name])return`文件(${r.name})重复选择`;m[r.name]=!0}return""},p=async()=>{if(i())return y.error(i());const m=new FormData;q(n.list).map(d=>{m.append("file[]",d.originFileObj)});try{await yt(m),u()}catch(d){d.errCode===50&&d.data.exist.length}},u=async()=>{const m=new FormData;q(n.list).map(d=>{m.append("file[]",d.originFileObj)}),n.loading=!0;try{await bt(m),n.list=[],y.success("操作成功"),t.emit("finish")}catch{}n.loading=!1};return{...x(n),isOnlyUpload:s,imgChange:c,setStep:o,preCheck:p,checkValueLNL:V}}});const le=e=>(B("data-v-e3f620ea"),e=e(),z(),e),Vt={key:0},Bt=le(()=>l("div",{style:{"margin-top":"8px"}},"选择图片",-1)),zt={class:"img-item"},xt={class:"image"},Wt=["src"],Ht=["title"],qt={class:"pre"},Zt={class:"last"},Yt=le(()=>l("div",{class:"tips"},"单次最多上传 10 张图片, 且每张图片不可大于 2M, 文件名必须是字母、数字和下划线!",-1)),Jt={class:"footer"};function Qt(e,t,o,s,n,c){const i=f("plus-outlined"),p=f("CloseCircleOutlined"),u=f("a-upload"),m=f("a-button"),d=f("a-space");return v(),P(A,null,[g(u,{class:"upload-wrap",ref:"update",action:"#","list-type":"picture-card","file-list":e.list,"onUpdate:fileList":t[0]||(t[0]=r=>e.list=r),"before-upload":e.imgChange,"max-count":10,accept:"image/gif,image/jpeg,image/jpg,image/png,image/svg",multiple:!0},{previewIcon:h(()=>[]),itemRender:h(({file:r,actions:{remove:C}})=>[l("div",zt,[l("div",xt,[l("img",{src:r.thumbUrl},null,8,Wt)]),l("div",{class:ne(["title",{error:!("checkValueLNL"in e?e.checkValueLNL:b(V))(r.name.split(".")[0])}]),title:r.name},[l("div",qt,j(r.name.length>10?r.name.substring(0,r.name.length-6):r.name),1),l("div",Zt,j(r.name.length>10&&r.name.substring(r.name.length-6)||""),1)],10,Ht),g(p,{class:"del-icon",onClick:C},null,8,["onClick"])])]),tip:h(()=>[Yt]),default:h(()=>[e.list.length<10?(v(),P("div",Vt,[g(i),Bt])):T("",!0)]),_:1},8,["file-list","before-upload"]),l("div",Jt,[g(d,{wrap:""},{default:h(()=>[e.isOnlyUpload?T("",!0):(v(),E(m,{key:0,onClick:t[1]||(t[1]=r=>e.setStep(1))},{default:h(()=>[L("返回")]),_:1})),g(m,{type:"primary",onClick:e.preCheck,loading:e.loading,disabled:e.list.length===0},{default:h(()=>[L("确定")]),_:1},8,["onClick","loading","disabled"])]),_:1})])],64)}const Xt=R(Nt,[["render",Qt],["__scopeId","data-v-e3f620ea"]]),ea=O({__name:"Index",setup(e){let t;const o=w({visible:!1,step:1,isOnlyUpload:!1}),s=()=>{o.visible=!1},n=p=>{o.step=p};Z("isOnlyUpload",o.isOnlyUpload),Z("setStep",n);const c=p=>{t&&t(p),s()},i=()=>{o.step===2?o.step=1:(t&&t(),s())};return kt.$on("showPhotoGallery",p=>{t=p.callback,o.isOnlyUpload=p.isOnlyUpload||o.isOnlyUpload,o.visible=!0}),(p,u)=>{const m=f("a-modal");return v(),E(m,{open:b(o).visible,"onUpdate:open":u[0]||(u[0]=d=>b(o).visible=d),title:b(o).step===1?"图片库":"上传图片",width:"923px","destroy-on-close":"","wrap-class-name":"fp-mall-admin",footer:null},{default:h(()=>[!b(o).isOnlyUpload&&b(o).step===1?(v(),E(Ut,{key:0,onSubmit:c,onClose:s})):T("",!0),b(o).isOnlyUpload||b(o).step===2?(v(),E(Xt,{key:1,ref:"upload",onFinish:i},null,512)):T("",!0)]),_:1},8,["open","title"])}}}),Va=Object.freeze(Object.defineProperty({__proto__:null,default:ea},Symbol.toStringTag,{value:"Module"})),ta=O({name:"SelectWithAll",props:{value:{type:String,default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"请选择"}},emits:["update:value","change"],setup(e,t){const o=Y({get:()=>{if(e.value)return e.value==="all"?["all"]:e.value.split("|").filter(n=>n)},set:n=>{let c;!n||n.length===0?c=void 0:n[0]==="all"?c="all":c=`|${n.join("|")}|`,t.emit("update:value",c),t.emit("change",c)}}),s=Y(()=>{const n=e.options.map(i=>typeof i=="string"?{label:i,value:i}:i),c=[{label:"ALL",value:"all"}];return e.value==="all"?c:e.value?n:[...c,...n]});return{...x(e),modelValue:o,computedOptions:s}}});function aa(e,t,o,s,n,c){const i=f("a-select");return v(),E(i,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=p=>e.modelValue=p),options:e.computedOptions,placeholder:e.placeholder,mode:"multiple","allow-clear":""},null,8,["value","options","placeholder"])}const na=R(ta,[["render",aa]]),Ba=Object.freeze(Object.defineProperty({__proto__:null,default:na},Symbol.toStringTag,{value:"Module"})),za=e=>a.get("/api/active_checkin",e),xa=e=>a.get("/api/active_checkin/check_detail",{id:e}),Wa=e=>a.post("/api/active_checkin/update",e),Ha=e=>a.delete("/api/active_checkin",e),qa=e=>a.get("/api/active_checkin/copy",e),Za=e=>a.get("/api/active_checkin/enable",e),Ya=e=>a.get("/api/active_checkin/detail",e),Ja=e=>a.downfile("/api/active_checkin/export",e),Qa=e=>a.get("/api/active-checkin-v2",e),Xa=e=>a.get(`/api/active-checkin-v2/${e}`),en=e=>a.post("/api/active-checkin-v2",e),tn=e=>{const t=e.get("id");return a.put(`/api/active-checkin-v2/${t}`,e)},an=e=>a.delete(`/api/active-checkin-v2/${e.id}`,e),nn=e=>a.put(`/api/active-checkin-v2/copy/${e.id}`,e),on=e=>a.put(`/api/active-checkin-v2/enable/${e.id}`,e),sn=e=>a.get(`/api/active-checkin-v2/config-detail/${e.id}`,e),rn=e=>a.downfileGet(`/api/active-checkin-v2/export/${e.id}`,e),cn=e=>a.get("/api/active-checkin-weekly",e),ln=e=>a.get(`/api/active-checkin-weekly/${e}`),pn=e=>a.post("/api/active-checkin-weekly",e),un=e=>{const t=e.get("id");return a.put(`/api/active-checkin-weekly/${t}`,e)},dn=e=>a.delete(`/api/active-checkin-weekly/${e.id}`,e),_n=e=>a.put(`/api/active-checkin-weekly/copy/${e.id}`,e),mn=e=>a.put(`/api/active-checkin-weekly/enable/${e.id}`,e),gn=e=>a.get(`/api/active-checkin-weekly/config-detail/${e.id}`,e),hn=e=>a.downfileGet(`/api/active-checkin-weekly/export/${e.id}`,e),fn=e=>a.get("/api/game-platform",e),vn=e=>a.post("/api/game-platform",e),kn=(e,t)=>a.put(`/api/game-platform/${e}`,t),Cn=e=>a.get(`/api/game-platform/${e}`),yn=({id:e})=>a.delete(`/api/game-platform/${e}`),bn=(e,t)=>a.put(`/api/game-platform/set-status/${e}`,{status:t}),Ln=e=>a.get("/api/master-switch",e),Pn=e=>a.post("/api/master-switch",e),En=(e,t)=>a.put(`/api/master-switch/${e}`,t),On=e=>a.get(`/api/master-switch/${e}`),Sn=({id:e})=>a.delete(`/api/master-switch/${e}`),Dn=(e,t)=>a.put(`/api/master-switch/set-status/${e}`,{status:t}),wn=e=>a.get("/api/rule",e),Rn=e=>a.get("/api/rule/keyDetail",{key:e}),In=e=>a.put("/api/rule/update",e),Tn=e=>a.post("/api/rule/upload",e),$n=e=>a.get("/api/merge-switch",e),An=e=>a.post("/api/merge-switch",e),Gn=(e,t)=>a.put(`/api/merge-switch/${e}`,t),Mn=e=>a.get(`/api/merge-switch/${e}`),jn=({id:e})=>a.delete(`/api/merge-switch/${e}`),Fn=(e,t)=>a.put(`/api/merge-switch/set-status/${e}`,{status:t});export{Ra as $,La as A,ka as B,xa as C,Wa as D,za as E,Za as F,Ha as G,qa as H,Ja as I,Ya as J,pa as K,ha as L,ra as M,Cn as N,kn as O,k as P,vn as Q,fn as R,bn as S,yn as T,On as U,En as V,Pn as W,Ln as X,Dn as Y,Sn as Z,fa as _,en as a,Oa as a0,Ea as a1,Pa as a2,Da as a3,ba as a4,Sa as a5,ja as a6,Ka as a7,Fa as a8,Ma as a9,Mn as aA,Gn as aB,An as aC,$n as aD,Fn as aE,jn as aF,Ua as aa,Na as ab,wa as ac,Aa as ad,$a as ae,Ta as af,Ia as ag,Ga as ah,ce as ai,D as aj,re as ak,da as al,ye as am,ea as an,ia as ao,ua as ap,Va as aq,kt as ar,ya as as,Ca as at,va as au,na as av,Ba as aw,_t as ax,ca as ay,ga as az,Qa as b,an as c,_a as d,nn as e,rn as f,Xa as g,sn as h,Rn as i,In as j,Tn as k,wn as l,la as m,ln as n,un as o,pn as p,cn as q,mn as r,on as s,dn as t,tn as u,_n as v,hn as w,gn as x,a as y,ma as z};
