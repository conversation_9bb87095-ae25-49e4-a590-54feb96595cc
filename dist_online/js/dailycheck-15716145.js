import{d as V,r as x,b as l,e as v,f as $,g as t,h as e,u as a,k as u,p as H,v as Y,x as J,a as B,bu as Q,l as K,F as R,n as O,t as N,bv as W,c as X,i as Z}from"./vendor-6ece677a.js";import{r as C,d as ee,m as j,P as te}from"./common-9638d99e.js";import{M as ae}from"./antd-79d05377.js";const ne=[{dataIndex:"filter",key:"filter",title:"服务器",width:"70px"},{dataIndex:"bg_img",key:"bg_img",title:"签到背景图",width:"150px"},{dataIndex:"month",key:"month",title:"月份",width:"70px"},{dataIndex:"status",key:"status",title:"状态",width:"80px"},{dataIndex:"update_by",key:"update_by",title:"更新人",width:"120px"},{dataIndex:"action",key:"action",title:"操作",width:"180px",fixed:"right",align:"center"}],le=[{dataIndex:"day",key:"day",title:"奖励天数",width:"80px"},{dataIndex:"gift_type",key:"gift_type",title:"奖励类型",width:"110px"},{dataIndex:"pkg_id",key:"pkg_id",title:"礼包 id",width:"100px"},{dataIndex:"gift_name",key:"gift_name",title:"礼包名称",width:"150px"},{dataIndex:"vip_restriction",key:"vip_restriction",title:"奖励等级需求",width:"90px"},{dataIndex:"resigning_consumption",key:"resigning_consumption",title:"补签消耗",width:"90px"}],E=[{value:1,label:"1月"},{value:2,label:"2月"},{value:3,label:"3月"},{value:4,label:"4月"},{value:5,label:"5月"},{value:6,label:"6月"},{value:7,label:"7月"},{value:8,label:"8月"},{value:9,label:"9月"},{value:10,label:"10月"},{value:11,label:"11月"},{value:12,label:"12月"}],oe=o=>C.get("/api/active_checkin",o),ie=o=>C.get("/api/active_checkin/check_detail",{id:o}),se=o=>C.post("/api/active_checkin/update",o),de=o=>C.delete("/api/active_checkin",o),re=o=>C.get("/api/active_checkin/copy",o),ue=o=>C.get("/api/active_checkin/enable",o),ce=o=>C.get("/api/active_checkin/detail",o),pe=o=>C.downfile("/api/active_checkin/export",o),_e=V({__name:"Form",props:["editId"],emits:["close","refresh"],setup(o,{emit:c}){const y=o,k=x(!0),g=x(!1),I=()=>{g.value=!0,ie(y.editId).then(s=>{n.value=s}).finally(()=>g.value=!1)};y.editId&&I();const n=x({id:0,month:void 0,bg_img:"",filter:{server_min:1,server_max:99999}}),p=x([]),h=x(!1),T=x(),_=()=>{T.value.validate().then(()=>{h.value=!0;const s=new FormData;s.append("month",`${n.value.month}`),s.append("bg_img",n.value.bg_img),s.append("server_min",`${n.value.filter.server_min}`),s.append("server_max",`${n.value.filter.server_max}`),p.value.length&&s.append("file",p.value[0]),n.value.id&&s.append("id",`${n.value.id}`),se(s).then(()=>{c("close"),c("refresh")}).catch(()=>{}).finally(()=>{h.value=!1})}).catch(()=>{})},r=s=>(p.value=[...p.value||[],s],!1),w=async s=>p.value.length||n.value.id||n.value.file?Promise.resolve():Promise.reject("请上传奖品配置文件"),M=s=>{const i=n.value.filter.server_max||0,P=n.value.filter.server_min||0;n.value.filter.server_max=i<P?s:n.value.filter.server_max};return(s,i)=>{const P=l("a-select"),b=l("a-form-item"),A=l("SelectImg"),L=l("upload-outlined"),F=l("a-button"),d=l("a-upload"),D=l("ExclamationCircleFilled"),U=l("a-typography-text"),S=l("a-input-number"),m=l("a-flex"),q=l("a-form"),G=l("a-spin"),z=l("a-drawer");return v(),$(z,{open:a(k),"onUpdate:open":i[5]||(i[5]=f=>H(k)?k.value=f:null),title:y.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:i[6]||(i[6]=f=>!f&&c("close"))},{default:t(()=>[e(G,{spinning:a(g)},{default:t(()=>[e(q,{model:a(n),name:"basic",ref_key:"formRef",ref:T,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:t(()=>[e(b,{label:"月份",name:"month",rules:[{required:!0,message:"请输入月份"}]},{default:t(()=>[e(P,{value:a(n).month,"onUpdate:value":i[0]||(i[0]=f=>a(n).month=f),options:a(E),placeholder:"请选择月份"},null,8,["value","options"])]),_:1}),e(b,{label:"背景图",name:"bg_img"},{default:t(()=>[e(A,{value:a(n).bg_img,"onUpdate:value":i[1]||(i[1]=f=>a(n).bg_img=f),"width-height":[750,208]},null,8,["value"])]),_:1}),e(b,{label:"奖品配置",rules:[{validator:w}]},{default:t(()=>[e(d,{"file-list":a(p),"before-upload":r,onRemove:i[2]||(i[2]=()=>p.value=[]),"max-count":1,accept:"'.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'"},{default:t(()=>[e(F,null,{default:t(()=>[e(L),u(" 点击上传 ")]),_:1})]),_:1},8,["file-list"]),e(U,{type:"warning",style:{"font-size":"12px"}},{default:t(()=>[e(D),u(" 新增时必须上传奖励配置 ")]),_:1})]),_:1},8,["rules"]),e(b,{label:"服务器",required:""},{default:t(()=>[e(m,null,{default:t(()=>[e(b,{name:["filter","server_min"]},{default:t(()=>[e(S,{style:{width:"150px"},value:a(n).filter.server_min,"onUpdate:value":i[3]||(i[3]=f=>a(n).filter.server_min=f),min:0,onChange:M,placeholder:"最小服务器"},null,8,["value"])]),_:1}),e(U,{style:{margin:"0 5px 24px","line-height":"32px"}},{default:t(()=>[u("~")]),_:1}),e(b,{name:["filter","server_max"]},{default:t(()=>[e(S,{style:{width:"150px"},value:a(n).filter.server_max,"onUpdate:value":i[4]||(i[4]=f=>a(n).filter.server_max=f),min:a(n).filter.server_min,placeholder:"最大服务器"},null,8,["value","min"])]),_:1})]),_:1})]),_:1}),e(b,{"wrapper-col":{offset:6,span:16}},{default:t(()=>[e(F,{type:"primary",onClick:_,loading:a(h)},{default:t(()=>[u("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),me=V({__name:"Index",setup(o){Y.extend(J);const c=B({editVisible:!1,editId:0}),y=Q(),k=x(),g=()=>k.value.requestTableData(!0),I=(_,r)=>{c.editVisible=_,c.editId=r||0},n=(_,r)=>j(`确定要删除${r?"选中的":"此条"}数据吗？`,de,{id:_}).then(()=>g()),p=_=>j("确定要复制此条数据并生成一份新数据吗？",re,{id:_}).then(()=>g()),h=x([]),T=(_,r,w)=>{h.value[w]=!0,r.status=1-_,ae.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{h.value[w]=!1,ue({id:r.id,status:_}).finally(()=>{g()})},onCancel:()=>{h.value[w]=!1}})};return(_,r)=>{const w=l("PlusOutlined"),M=l("a-button"),s=l("CloudDownloadOutlined"),i=l("a-space"),P=l("a-image"),b=l("a-switch"),A=l("a-divider"),L=l("a-typography-link"),F=l("CustomTable");return v(),K(R,null,[e(F,{ref_key:"RefCustomTable",ref:k,"data-api":a(oe),params:{},columns:a(ne)},{leftTool:t(()=>[e(i,null,{default:t(()=>[e(M,{type:"primary",onClick:r[0]||(r[0]=d=>I(!0))},{icon:t(()=>[e(w)]),default:t(()=>[u(" 新增 ")]),_:1}),e(M,{type:"primary",onClick:r[1]||(r[1]=d=>a(ee)("checkin"))},{icon:t(()=>[e(s)]),default:t(()=>[u(" 下载奖品模板 ")]),_:1})]),_:1})]),bodyCell:t(({record:d,column:D,index:U})=>{var S;return[D.key==="bg_img"?(v(),K(R,{key:0},[d.bg_img?(v(),$(P,{key:0,src:d.bg_img,height:60},null,8,["src"])):(v(),K(R,{key:1},[u("-")],64))],64)):O("",!0),D.key==="filter"?(v(),K(R,{key:1},[u(N(d.filter.server_min)+"-"+N(d.filter.server_max),1)],64)):O("",!0),D.key==="month"?(v(),K(R,{key:2},[u(N((S=a(E).filter(m=>d.month===m.value)[0])==null?void 0:S.label),1)],64)):O("",!0),D.key==="status"?(v(),$(b,{key:3,checked:d.status,"onUpdate:checked":m=>d.status=m,checkedValue:10,unCheckedValue:1,"checked-children":"开启","un-checked-children":"关闭",loading:a(h)[U],onClick:m=>T(m,d,U)},null,8,["checked","onUpdate:checked","loading","onClick"])):O("",!0),D.key==="action"?(v(),$(i,{key:4},{split:t(()=>[e(A,{type:"vertical",style:{margin:"0"}})]),default:t(()=>[e(L,{type:"success",onClick:m=>p(d.id)},{default:t(()=>[u("复制")]),_:2},1032,["onClick"]),e(L,{onClick:m=>I(!0,d.id)},{default:t(()=>[u("编辑")]),_:2},1032,["onClick"]),e(L,{onClick:m=>a(y).push(`/dailyCheck/detail/${d.id}`)},{default:t(()=>[u("详情")]),_:2},1032,["onClick"]),e(L,{type:"danger",danger:"",onClick:m=>n(d.id,!1)},{default:t(()=>[u("删除")]),_:2},1032,["onClick"])]),_:2},1024)):O("",!0)]}),_:1},8,["data-api","columns"]),a(c).editVisible?(v(),$(_e,{key:0,"edit-id":a(c).editId,onClose:r[2]||(r[2]=d=>I(!1)),onRefresh:g},null,8,["edit-id"])):O("",!0)],64)}}}),ye=Object.freeze(Object.defineProperty({__proto__:null,default:me},Symbol.toStringTag,{value:"Module"})),fe=V({__name:"Detail",setup(o){const c=W(),y=B({id:c.params.id&&Number(c.params.id)||0}),k=x(),g=()=>k.value.requestTableData(!0);return(I,n)=>{const p=l("UploadBtn"),h=l("CustomTable"),T=X("has");return v(),$(h,{ref_key:"RefCustomTable",ref:k,"data-api":a(ce),params:{id:a(y).id},columns:a(le),pagination:!1},{rightTool:t(()=>[Z(e(p,{ref:"uploadBtn",onUploadSuccess:g,downloadApi:a(pe),uploadData:{id:a(y).id},downloadData:{id:a(y).id},fileType:"checkin",page:a(te).DAILY_CHECK},null,8,["downloadApi","uploadData","downloadData","page"]),[[T,"Operation"]])]),_:1},8,["data-api","params","columns"])}}}),ke=Object.freeze(Object.defineProperty({__proto__:null,default:fe},Symbol.toStringTag,{value:"Module"}));export{ke as D,ye as I};
