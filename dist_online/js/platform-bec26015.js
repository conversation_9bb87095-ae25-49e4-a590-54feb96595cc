import{d as V,r as k,b as n,e as g,f as S,g as a,h as t,u as e,k as w,p as W,a as z,l as D,n as U,F as M,t as N,bz as Y,a6 as le,m as H,j as ne}from"./vendor-6ece677a.js";import{r as $,u as K,m as B,s as Q,d as X}from"./common-9638d99e.js";import{M as J,m as oe}from"./antd-79d05377.js";const se=s=>$.get("/api/game-platform",s),ie=s=>$.post("/api/game-platform",s),ue=(s,u)=>$.put(`/api/game-platform/${s}`,u),de=s=>$.get(`/api/game-platform/${s}`),re=({id:s})=>$.delete(`/api/game-platform/${s}`),ce=(s,u)=>$.put(`/api/game-platform/set-status/${s}`,{status:u}),pe=s=>$.get("/api/master-switch",s),_e=s=>$.post("/api/master-switch",s),me=(s,u)=>$.put(`/api/master-switch/${s}`,u),fe=s=>$.get(`/api/master-switch/${s}`),ge=({id:s})=>$.delete(`/api/master-switch/${s}`),ve=(s,u)=>$.put(`/api/master-switch/set-status/${s}`,{status:u}),ye=s=>$.get("/api/rule",s),ke=s=>$.get("/api/rule/keyDetail",{key:s}),be=s=>$.put("/api/rule/update",s),he=s=>$.post("/api/rule/upload",s),Ce=s=>$.get("/api/merge-switch",s),we=s=>$.post("/api/merge-switch",s),xe=(s,u)=>$.put(`/api/merge-switch/${s}`,u),Ie=s=>$.get(`/api/merge-switch/${s}`),Se=({id:s})=>$.delete(`/api/merge-switch/${s}`),$e=(s,u)=>$.put(`/api/merge-switch/set-status/${s}`,{status:u}),Ue=[{dataIndex:"id",key:"id",title:"id",width:"70px"},{dataIndex:"game_project",key:"game_project",title:"game_project",width:"140px"},{dataIndex:"game_title",key:"game_title",title:"game_name",width:"200px"},{dataIndex:"icon_url",key:"icon_url",title:"icon",width:"80px"},{dataIndex:"game_ids",key:"game_ids",title:"game_id",width:"140px"},{dataIndex:"status",key:"status",title:"状态",width:"140px"},{dataIndex:"action",key:"action",title:"操作",width:"140px",fixed:"right",align:"center"}],Te=V({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(s,{emit:u}){const b=s,r=k(!0),v=k(!1),L=()=>{v.value=!0,de(b.editId).then(p=>{c.value=p,c.value.game_ids=JSON.parse(p.game_ids)}).finally(()=>v.value=!1)};b.editId&&L();const c=k({id:0,game_project:void 0,game_title:void 0,status:0,icon_url:"",game_ids:[]}),h=k(!1),y=k(),l=()=>{y.value.validate().then(()=>{h.value=!0;const{id:p,..._}=c.value;_.game_ids=JSON.stringify(_.game_ids),b.editId?ue(p,_).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{h.value=!1}):ie(_).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{h.value=!1})}).catch(()=>{})};return(p,_)=>{const j=n("a-input"),x=n("a-form-item"),o=n("SelectLang"),T=n("SelectImg"),i=n("a-select"),I=n("a-button"),f=n("a-form"),O=n("a-spin"),C=n("a-drawer");return g(),S(C,{open:e(r),"onUpdate:open":_[4]||(_[4]=m=>W(r)?r.value=m:null),title:"接入游戏",maskClosable:!1,width:500,onAfterOpenChange:_[5]||(_[5]=m=>!m&&u("close"))},{default:a(()=>[t(O,{spinning:e(v)},{default:a(()=>[t(f,{model:e(c),name:"basic",ref_key:"formRef",ref:y,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off",class:"modal-form"},{default:a(()=>[t(x,{label:"game_project",name:"game_project",rules:[{required:!0,message:"请输入项目名称"}]},{default:a(()=>[t(j,{value:e(c).game_project,"onUpdate:value":_[0]||(_[0]=m=>e(c).game_project=m),placeholder:" 请输入项目名称"},null,8,["value"])]),_:1}),t(x,{label:"game_name",name:"game_title",rules:[{required:!0,message:"请选择多语言"}]},{default:a(()=>[t(o,{value:e(c).game_title,"onUpdate:value":_[1]||(_[1]=m=>e(c).game_title=m)},null,8,["value"])]),_:1}),t(x,{label:"icon",name:"icon_url",rules:[{required:!0,message:"请上传通道ICON"}]},{default:a(()=>[t(T,{value:e(c).icon_url,"onUpdate:value":_[2]||(_[2]=m=>e(c).icon_url=m),"width-height":[72,72]},null,8,["value"])]),_:1}),t(x,{label:"game_ids",name:"game_ids",rules:[{required:!0,message:"请选择游戏"}]},{default:a(()=>[t(i,{value:e(c).game_ids,"onUpdate:value":_[3]||(_[3]=m=>e(c).game_ids=m),mode:"tags",style:{width:"100%"},placeholder:"请输入 game_ids 并回车创建"},null,8,["value"])]),_:1}),t(x,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[t(I,{type:"primary",onClick:l,loading:e(h)},{default:a(()=>[w("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open"])}}}),je=V({__name:"Index",setup(s){const u=z({editVisible:!1,editId:0}),b=k(),r=()=>b.value.requestTableData(!0),v=(l,p)=>{u.editVisible=l,u.editId=p||0},L=l=>B("删除后将无法拉取到项目下的角色，请谨慎操作",re,{id:l}).then(()=>r()),{FETCH_GLOBAL_CONFIG:c}=K(),h=k([]),y=(l,p,_)=>{h.value[_]=!0,p.status=1-l;const j=l===0?"是否关闭当前项目，关闭后将无法拉到项目下的角色，请谨慎操作":"是否开启当前项目";J.confirm({title:"提示",content:j,okText:"确定",cancelText:"取消",onOk:()=>{h.value[_]=!1,ce(p.id,l).finally(()=>{r(),c(!0)})},onCancel:()=>{h.value[_]=!1}})};return(l,p)=>{const _=n("PlusOutlined"),j=n("a-button"),x=n("a-switch"),o=n("LangKey"),T=n("a-image"),i=n("a-divider"),I=n("a-typography-link"),f=n("a-space"),O=n("CustomTable");return g(),D(M,null,[t(O,{ref_key:"RefCustomTable",ref:b,"data-api":e(se),params:{},columns:e(Ue)},{leftTool:a(()=>[t(j,{type:"primary",onClick:p[0]||(p[0]=C=>v(!0))},{icon:a(()=>[t(_)]),default:a(()=>[w(" 新增 ")]),_:1})]),bodyCell:a(({record:C,column:m,index:P})=>[m.key==="status"?(g(),S(x,{key:0,checked:C.status,"onUpdate:checked":q=>C.status=q,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(h)[P],onClick:q=>y(q,C,P)},null,8,["checked","onUpdate:checked","loading","onClick"])):U("",!0),m.key==="game_title"?(g(),S(o,{key:1,"lang-key":C.game_title,i18n_name:C.i18n_name},null,8,["lang-key","i18n_name"])):U("",!0),m.key==="icon_url"?(g(),S(T,{key:2,src:C.icon_url,height:60},null,8,["src"])):U("",!0),m.key==="game_ids"?(g(),D(M,{key:3},[w(N(JSON.parse(C.game_ids).join("、")),1)],64)):U("",!0),m.key==="action"?(g(),S(f,{key:4},{split:a(()=>[t(i,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(I,{onClick:q=>v(!0,C.id)},{default:a(()=>[w("编辑")]),_:2},1032,["onClick"]),t(I,{type:"danger",onClick:q=>L(C.id)},{default:a(()=>[w("删除")]),_:2},1032,["onClick"])]),_:2},1024)):U("",!0)]),_:1},8,["data-api","columns"]),e(u).editVisible?(g(),S(Te,{key:0,"edit-id":e(u).editId,onClose:p[1]||(p[1]=C=>v(!1)),onRefresh:r},null,8,["edit-id"])):U("",!0)],64)}}}),We=Object.freeze(Object.defineProperty({__proto__:null,default:je},Symbol.toStringTag,{value:"Module"})),Oe=[{dataIndex:"id",key:"id",title:"id",width:"70px"},{dataIndex:"game_project",key:"game_project",title:"游戏",width:"100px"},{dataIndex:"status",key:"status",title:"状态",width:"100px"},{dataIndex:"filter",key:"filter",title:"筛选器",width:"60px"},{dataIndex:"action",key:"action",title:"操作",width:"100px",fixed:"right",align:"center"}],Re=V({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(s,{emit:u}){const b=s,r=K(),{configState:v}=Y(r),{getGameChannel:L}=r,c=k(!0),h=k(!1),y=()=>{h.value=!0,fe(b.editId).then(x=>{l.value=x}).finally(()=>h.value=!1)};b.editId&&y();const l=k({id:0,game_project:void 0,status:0,is_red_dot:0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:"",uids:""}),p=k(!1),_=k(),j=()=>{_.value.validate().then(()=>{if(p.value=!0,b.editId){const{id:x,...o}=l.value;o.uids=o.uids.replace(/\s+/g,"").replace(/，/g,",").replace(/,+/g,","),me(x,o).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{p.value=!1})}else _e(l.value).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{p.value=!1})}).catch(()=>{})};return(x,o)=>{const T=n("a-select"),i=n("a-form-item"),I=n("a-radio"),f=n("a-radio-group"),O=n("SelectWithAllComp"),C=n("SelectWithAll"),m=n("a-textarea"),P=n("a-button"),q=n("a-form"),E=n("a-spin"),F=n("a-drawer");return g(),S(F,{open:e(c),"onUpdate:open":o[11]||(o[11]=d=>W(c)?c.value=d:null),title:b.editId?"编辑入口":"新增入口",maskClosable:!1,width:500,onAfterOpenChange:o[12]||(o[12]=d=>!d&&u("close"))},{default:a(()=>[t(E,{spinning:e(h)},{default:a(()=>[t(q,{model:e(l),name:"basic",ref_key:"formRef",ref:_,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off",class:"modal-form"},{default:a(()=>[t(i,{label:"游戏",name:"game_project",rules:[{required:!0,message:"请选择游戏"}]},{default:a(()=>[t(T,{value:e(l).game_project,"onUpdate:value":o[0]||(o[0]=d=>e(l).game_project=d),placeholder:"请选择游戏",options:e(v).game_projects,onChange:o[1]||(o[1]=()=>e(l).f_channel=void 0)},{option:a(({value:d,label:G})=>[w(N(G)+"-"+N(d),1)]),_:1},8,["value","options"])]),_:1}),t(i,{label:"是否需要红点",name:"is_red_dot"},{default:a(()=>[t(f,{value:e(l).is_red_dot,"onUpdate:value":o[2]||(o[2]=d=>e(l).is_red_dot=d)},{default:a(()=>[t(I,{value:1},{default:a(()=>[w("需要")]),_:1}),t(I,{value:0},{default:a(()=>[w("不需要")]),_:1})]),_:1},8,["value"])]),_:1}),t(i,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:a(()=>[t(f,{value:e(l).is_filter,"onUpdate:value":o[3]||(o[3]=d=>e(l).is_filter=d),onChange:o[4]||(o[4]=d=>e(Q)(e(l),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:a(()=>[t(I,{value:1},{default:a(()=>[w("开启")]),_:1}),t(I,{value:0},{default:a(()=>[w("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(l).is_filter===1?(g(),D(M,{key:0},[t(i,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:a(()=>[t(O,{value:e(l).f_os,"onUpdate:value":o[5]||(o[5]=d=>e(l).f_os=d),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(i,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:a(()=>[t(O,{value:e(l).lang,"onUpdate:value":o[6]||(o[6]=d=>e(l).lang=d),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(i,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:a(()=>[t(C,{value:e(l).f_channel,"onUpdate:value":o[7]||(o[7]=d=>e(l).f_channel=d),placeholder:"请选择渠道",options:e(l).game_project?e(L)(e(l).game_project||""):[]},null,8,["value","options"])]),_:1}),t(i,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:a(()=>[t(m,{value:e(l).f_s_ids,"onUpdate:value":o[8]||(o[8]=d=>e(l).f_s_ids=d),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(i,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:a(()=>[t(m,{value:e(l).f_lv_ids,"onUpdate:value":o[9]||(o[9]=d=>e(l).f_lv_ids=d),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1}),t(i,{label:"uid白名单",name:"uids"},{default:a(()=>[t(m,{value:e(l).uids,"onUpdate:value":o[10]||(o[10]=d=>e(l).uids=d),placeholder:"请输入uid，多个uid以“,”隔开","allow-clear":""},null,8,["value"])]),_:1})],64)):U("",!0),t(i,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[t(P,{type:"primary",onClick:j,loading:e(p)},{default:a(()=>[w("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),Le=V({__name:"Index",setup(s){const u=z({editVisible:!1,editId:0}),b=k(),r=()=>b.value.requestTableData(!0),v=(y,l)=>{u.editVisible=y,u.editId=l||0},L=y=>B("删除后游戏内私域入口将消失，请谨慎操作",ge,{id:y}).then(()=>r()),c=k([]),h=(y,l,p)=>{c.value[p]=!0,l.status=1-y,J.confirm({title:"提示",content:y===0?"关闭后游戏内私域入口将消失，请谨慎操作，是否确认关闭？":"开启后游戏内将展示私域入口，实时生效，是否确认开启？",okText:"确定",cancelText:"取消",onOk:()=>{c.value[p]=!1,ve(l.id,y).finally(()=>r())},onCancel:()=>{c.value[p]=!1}})};return(y,l)=>{const p=n("PlusOutlined"),_=n("a-button"),j=n("a-switch"),x=n("FilterCell"),o=n("a-divider"),T=n("a-typography-link"),i=n("a-space"),I=n("CustomTable");return g(),D(M,null,[t(I,{ref_key:"RefCustomTable",ref:b,"data-api":e(pe),params:{},columns:e(Oe)},{leftTool:a(()=>[t(_,{type:"primary",onClick:l[0]||(l[0]=f=>v(!0))},{icon:a(()=>[t(p)]),default:a(()=>[w(" 新增 ")]),_:1})]),bodyCell:a(({record:f,column:O,index:C})=>[O.key==="status"?(g(),S(j,{key:0,checked:f.status,"onUpdate:checked":m=>f.status=m,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(c)[C],onClick:m=>h(m,f,C)},null,8,["checked","onUpdate:checked","loading","onClick"])):U("",!0),O.key==="filter"?(g(),S(x,{key:1,record:f},null,8,["record"])):U("",!0),O.key==="action"?(g(),S(i,{key:2},{split:a(()=>[t(o,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(T,{onClick:m=>v(!0,f.id)},{default:a(()=>[w("编辑")]),_:2},1032,["onClick"]),t(T,{type:"danger",onClick:m=>L(f.id)},{default:a(()=>[w("删除")]),_:2},1032,["onClick"])]),_:2},1024)):U("",!0)]),_:1},8,["data-api","columns"]),e(u).editVisible?(g(),S(Re,{key:0,"edit-id":e(u).editId,onClose:l[1]||(l[1]=f=>v(!1)),onRefresh:r},null,8,["edit-id"])):U("",!0)],64)}}}),ze=Object.freeze(Object.defineProperty({__proto__:null,default:Le},Symbol.toStringTag,{value:"Module"})),qe=[{dataIndex:"name",key:"name",title:"规则分类"},{dataIndex:"key",key:"key",title:"规则key"},{dataIndex:"action",key:"action",title:"操作",align:"center"}],De=["innerHTML"],Me=V({__name:"Form",props:["editKey"],emits:["close","refresh"],setup(s,{emit:u}){const b=s,r=k({name:void 0,key:"",file:"",content:{},last_uploaded_file:""}),v=k("en"),L=k(!0),c=k(!1);(()=>{c.value=!0,ke(b.editKey).then(T=>{r.value={...r.value,...T}}).finally(()=>c.value=!1)})();const y=k(!1),l=k(),p=()=>{l.value.validate().then(()=>{y.value=!0,be(r.value).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{y.value=!1})}).catch(()=>{})},_=k({fileList:[],url:""}),j=async T=>{if(!T)return!1;try{const i=new FormData;i.append("file",T),i.append("key",r.value.key),he(i).then(I=>{r.value.content=I.content,oe.success("上传解析成功")}).catch()}catch{}return!1},x=()=>{r.value.last_uploaded_file?window.open(r.value.last_uploaded_file,"_blank"):X(r.value.key)},o=async(T,i)=>{let I=!0;if(r.value.key==="faq_rules")I=!1;else for(const f in i){if((f==="en"||f==="zh_cn")&&!i[f].text)return Promise.reject("英文或中文内容不可为空");i[f].text||(I=!1)}return I?Promise.reject("内容不可为空"):Promise.resolve()};return(T,i)=>{const I=n("a-input"),f=n("a-form-item"),O=n("upload-outlined"),C=n("a-button"),m=n("CloudDownloadOutlined"),P=n("a-space"),q=n("a-upload"),E=n("a-radio"),F=n("a-radio-group"),d=n("a-descriptions-item"),G=n("a-descriptions"),Z=n("Editor"),ee=n("a-form"),te=n("a-spin"),ae=n("a-drawer");return g(),S(ae,{open:e(L),"onUpdate:open":i[6]||(i[6]=R=>W(L)?L.value=R:null),title:"编辑规则",maskClosable:!1,width:600,onAfterOpenChange:i[7]||(i[7]=R=>!R&&u("close"))},{default:a(()=>[t(te,{spinning:e(c)},{default:a(()=>[t(ee,{model:e(r),name:"basic",ref_key:"formRef",ref:l,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:a(()=>[t(f,{label:"规则分类",name:"name",rules:[{required:!0,message:"请选择规则分类"}]},{default:a(()=>[t(I,{value:e(r).name,"onUpdate:value":i[0]||(i[0]=R=>e(r).name=R),placeholder:"请输入规则分类"},null,8,["value"])]),_:1}),t(f,{label:"规则KEY",name:"key",rules:[{required:!0,message:"请输入规则KEY"}]},{default:a(()=>[t(I,{value:e(r).key,"onUpdate:value":i[1]||(i[1]=R=>e(r).key=R),placeholder:"请输入规则key",disabled:""},null,8,["value"])]),_:1}),t(f,{label:"规则文档",name:"file"},{default:a(()=>[t(q,{name:"file",style:{height:"110px"},"file-list":e(_).fileList,"onUpdate:fileList":i[2]||(i[2]=R=>e(_).fileList=R),action:"#",accept:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel","before-upload":j,onRemove:i[3]||(i[3]=R=>e(_).url="")},{default:a(()=>[t(P,null,{default:a(()=>[t(C,null,{default:a(()=>[t(O),w(" 点击上传 ")]),_:1}),t(C,{type:"primary",onClick:le(x,["stop"])},{icon:a(()=>[t(m)]),default:a(()=>[w(N(e(r).last_uploaded_file?"下载文档":"下载模板"),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["file-list"])]),_:1}),t(f,{label:"按语种编辑",name:"content",rules:[{validator:o}]},{default:a(()=>[t(F,{value:e(v),"onUpdate:value":i[4]||(i[4]=R=>W(v)?v.value=R:null),style:{"line-height":"32px","margin-bottom":"8px"}},{default:a(()=>[t(P,{wrap:""},{default:a(()=>[(g(!0),D(M,null,H(e(r).content,(R,A)=>(g(),S(E,{key:A,value:A},{default:a(()=>[w(N(A),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["value"]),e(v)?(g(),D(M,{key:0},[e(r).key==="faq_rules"?(g(),D(M,{key:0},[e(r).content[e(v)].length?(g(),S(G,{key:0,layout:"vertical",bordered:"",column:1,size:"small"},{default:a(()=>[(g(!0),D(M,null,H(e(r).content[e(v)],(R,A)=>(g(),S(d,{label:R[`question${A+1}`]},{default:a(()=>[ne("span",{innerHTML:R[`answer${A+1}`]},null,8,De)]),_:2},1032,["label"]))),256))]),_:1})):U("",!0)],64)):JSON.stringify(e(r).content)!=="{}"?(g(),S(Z,{key:e(v),value:e(r).content[e(v)].text,"onUpdate:value":i[5]||(i[5]=R=>e(r).content[e(v)].text=R)},null,8,["value"])):U("",!0)],64)):U("",!0)]),_:1},8,["rules"]),t(f,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[t(C,{type:"primary",onClick:p,loading:e(y)},{default:a(()=>[w("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open"])}}}),Pe=V({__name:"Index",setup(s){const u=z({editVisible:!1,key:""}),b=k(),r=()=>b.value.requestTableData(!0),v=(c,h)=>{u.editVisible=c,u.key=h||""},L=c=>{X(c.key==="faq_rules"?"faq":"rule")};return(c,h)=>{const y=n("a-divider"),l=n("a-typography-link"),p=n("a-space"),_=n("CustomTable");return g(),D(M,null,[t(_,{ref_key:"RefCustomTable",ref:b,"data-api":e(ye),params:{},columns:e(qe),pagination:!1},{bodyCell:a(({record:j,column:x})=>[x.key==="action"?(g(),S(p,{key:0},{split:a(()=>[t(y,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(l,{onClick:o=>v(!0,j.key)},{default:a(()=>[w("编辑")]),_:2},1032,["onClick"]),t(l,{onClick:o=>L(j)},{default:a(()=>[w("下载模板")]),_:2},1032,["onClick"])]),_:2},1024)):U("",!0)]),_:1},8,["data-api","columns"]),e(u).editVisible?(g(),S(Me,{key:0,"edit-key":e(u).key,onClose:h[0]||(h[0]=j=>v(!1)),onRefresh:r},null,8,["edit-key"])):U("",!0)],64)}}}),Ke=Object.freeze(Object.defineProperty({__proto__:null,default:Pe},Symbol.toStringTag,{value:"Module"})),Ve=[{dataIndex:"id",key:"id",title:"id",width:"70px"},{dataIndex:"game_project",key:"game_project",title:"游戏",width:"100px"},{dataIndex:"status",key:"status",title:"状态",width:"100px"},{dataIndex:"filter",key:"filter",title:"筛选器",width:"60px"},{dataIndex:"action",key:"action",title:"操作",width:"100px",fixed:"right",align:"center"}],Ne=V({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(s,{emit:u}){const b=s,r=K(),{configState:v}=Y(r),{getGameChannel:L}=r,c=k(!0),h=k(!1),y=()=>{h.value=!0,Ie(b.editId).then(x=>{l.value=x}).finally(()=>h.value=!1)};b.editId&&y();const l=k({id:0,game_project:void 0,status:0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:"",uids:""}),p=k(!1),_=k(),j=()=>{_.value.validate().then(()=>{if(p.value=!0,JSON.stringify(l.value),b.editId){const{id:x,...o}=l.value;o.uids=o.uids.replace(/\s+/g,"").replace(/，/g,",").replace(/,+/g,","),xe(x,o).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{p.value=!1})}else we(l.value).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{p.value=!1})}).catch(()=>{})};return(x,o)=>{const T=n("a-select"),i=n("a-form-item"),I=n("a-radio"),f=n("a-radio-group"),O=n("SelectWithAllComp"),C=n("SelectWithAll"),m=n("a-textarea"),P=n("a-button"),q=n("a-form"),E=n("a-spin"),F=n("a-drawer");return g(),S(F,{open:e(c),"onUpdate:open":o[10]||(o[10]=d=>W(c)?c.value=d:null),title:b.editId?"编辑":"新增",maskClosable:!1,width:500,onAfterOpenChange:o[11]||(o[11]=d=>!d&&u("close"))},{default:a(()=>[t(E,{spinning:e(h)},{default:a(()=>[t(q,{model:e(l),name:"basic",ref_key:"formRef",ref:_,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off",class:"modal-form"},{default:a(()=>[t(i,{label:"游戏",name:"game_project",rules:[{required:!0,message:"请选择游戏"}]},{default:a(()=>[t(T,{value:e(l).game_project,"onUpdate:value":o[0]||(o[0]=d=>e(l).game_project=d),placeholder:"请选择游戏",options:e(v).game_projects,onChange:o[1]||(o[1]=()=>e(l).f_channel=void 0)},{option:a(({value:d,label:G})=>[w(N(G)+"-"+N(d),1)]),_:1},8,["value","options"])]),_:1}),t(i,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:a(()=>[t(f,{value:e(l).is_filter,"onUpdate:value":o[2]||(o[2]=d=>e(l).is_filter=d),onChange:o[3]||(o[3]=d=>e(Q)(e(l),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:a(()=>[t(I,{value:1},{default:a(()=>[w("开启")]),_:1}),t(I,{value:0},{default:a(()=>[w("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),e(l).is_filter===1?(g(),D(M,{key:0},[t(i,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:a(()=>[t(O,{value:e(l).f_os,"onUpdate:value":o[4]||(o[4]=d=>e(l).f_os=d),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),t(i,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:a(()=>[t(O,{value:e(l).lang,"onUpdate:value":o[5]||(o[5]=d=>e(l).lang=d),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),t(i,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:a(()=>[t(C,{value:e(l).f_channel,"onUpdate:value":o[6]||(o[6]=d=>e(l).f_channel=d),placeholder:"请选择渠道",options:e(l).game_project?e(L)(e(l).game_project||""):[]},null,8,["value","options"])]),_:1}),t(i,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:a(()=>[t(m,{value:e(l).f_s_ids,"onUpdate:value":o[7]||(o[7]=d=>e(l).f_s_ids=d),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),t(i,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:a(()=>[t(m,{value:e(l).f_lv_ids,"onUpdate:value":o[8]||(o[8]=d=>e(l).f_lv_ids=d),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1}),t(i,{label:"uid白名单",name:"uids"},{default:a(()=>[t(m,{value:e(l).uids,"onUpdate:value":o[9]||(o[9]=d=>e(l).uids=d),placeholder:"请输入uid，多个uid以“,”隔开","allow-clear":""},null,8,["value"])]),_:1})],64)):U("",!0),t(i,{"wrapper-col":{offset:6,span:16}},{default:a(()=>[t(P,{type:"primary",onClick:j,loading:e(p)},{default:a(()=>[w("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),Ae=V({__name:"Index",setup(s){const u=z({editVisible:!1,editId:0}),b=k(),r=()=>b.value.requestTableData(!0),v=(y,l)=>{u.editVisible=y,u.editId=l||0},L=y=>B("删除后游戏内私域融合入口将消失，请谨慎操作",Se,{id:y}).then(()=>r()),c=k([]),h=(y,l,p)=>{c.value[p]=!0,l.status=1-y,J.confirm({title:"提示",content:y===0?"关闭后游戏内私域融合入口将消失，请谨慎操作，是否确认关闭？":"开启后游戏内将展示私域合入口，实时生效，是否确认开启？",okText:"确定",cancelText:"取消",onOk:()=>{c.value[p]=!1,$e(l.id,y).finally(()=>r())},onCancel:()=>{c.value[p]=!1}})};return(y,l)=>{const p=n("PlusOutlined"),_=n("a-button"),j=n("a-switch"),x=n("FilterCell"),o=n("a-divider"),T=n("a-typography-link"),i=n("a-space"),I=n("CustomTable");return g(),D(M,null,[t(I,{ref_key:"RefCustomTable",ref:b,"data-api":e(Ce),params:{},columns:e(Ve)},{leftTool:a(()=>[t(_,{type:"primary",onClick:l[0]||(l[0]=f=>v(!0))},{icon:a(()=>[t(p)]),default:a(()=>[w(" 新增 ")]),_:1})]),bodyCell:a(({record:f,column:O,index:C})=>[O.key==="status"?(g(),S(j,{key:0,checked:f.status,"onUpdate:checked":m=>f.status=m,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:e(c)[C],onClick:m=>h(m,f,C)},null,8,["checked","onUpdate:checked","loading","onClick"])):U("",!0),O.key==="filter"?(g(),S(x,{key:1,record:f},null,8,["record"])):U("",!0),O.key==="action"?(g(),S(i,{key:2},{split:a(()=>[t(o,{type:"vertical",style:{margin:"0"}})]),default:a(()=>[t(T,{onClick:m=>v(!0,f.id)},{default:a(()=>[w("编辑")]),_:2},1032,["onClick"]),t(T,{type:"danger",onClick:m=>L(f.id)},{default:a(()=>[w("删除")]),_:2},1032,["onClick"])]),_:2},1024)):U("",!0)]),_:1},8,["data-api","columns"]),e(u).editVisible?(g(),S(Ne,{key:0,"edit-id":e(u).editId,onClose:l[1]||(l[1]=f=>v(!1)),onRefresh:r},null,8,["edit-id"])):U("",!0)],64)}}}),Be=Object.freeze(Object.defineProperty({__proto__:null,default:Ae},Symbol.toStringTag,{value:"Module"}));export{We as I,ze as a,Ke as b,Be as c};
