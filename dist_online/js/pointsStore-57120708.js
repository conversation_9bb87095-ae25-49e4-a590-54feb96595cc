import{r as O,u as ue,g as te,L as ae,l as pe,P as de,m as _e}from"./common-9638d99e.js";import{d as le,v as Y,x as ne,r as U,a as J,o as ie,w as re,b as r,e as c,f as T,g as o,h as l,u as t,k as v,n as g,p as me,_ as se,c as ve,l as I,i as ce,F as C,t as L,j as Q,q as fe,s as ge}from"./vendor-6ece677a.js";import{M as q}from"./antd-79d05377.js";const ye=m=>O.get("/api/points-mall-product",m),ke=m=>O.post("/api/points-mall-product",m),ee=(m,k)=>O.put(`/api/points-mall-product/${m}`,k),be=m=>O.get(`/api/points-mall-product/${m}`),we=({petId:m})=>O.delete(`/api/points-mall-product/${m}`),he=(m,k)=>O.put(`/api/points-mall-product/set-status/${m}`,{status:k}),xe=()=>O.downfile("/api/points-mall-product/export"),Ie=m=>O.put(`/api/points-mall-product/adjust-stock/${m.id}`,m),Ce=le({__name:"Form",props:["editId"],emits:["close","refresh"],setup(m,{emit:k}){const p=m;Y.extend(ne);const{configState:G}=ue(),M=U(!0),D=U(!1),F=()=>{D.value=!0,be(p.editId).then(s=>{a.value=s,a.value.gift_id=Number(s.gift_id),a.value.item_limit_type=s.item_limit_type||1,a.value.stocks_num=s.stocks_num||1,a.value.online_timing_str=s.online_timing?Y.utc(s.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss"):"",s.is_time_limit===1&&s.start_time&&s.end_time?a.value.times=[s.start_time,s.end_time]:a.value.times=void 0}).finally(()=>D.value=!1)};p.editId&&F();const a=U({id:0,cycle_type:1,cycle_shape:1,cycle_times:1,cost_coin:0,vip:1,game_growth:0,role_growth:0,gift_id:null,status:0,is_time_limit:0,times:void 0,use_stocks:0,item_limit_type:1,stocks_num:1,use_desc_key:null,online_type:1,online_timing:0,online_timing_str:""}),y=J({uploadLoading:!1,optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsOne:[{label:"系统通知",value:1}],optionsTwo:[],optionsVip:[{label:"R1",value:1},{label:"R2",value:2},{label:"R3",value:3},{label:"R4",value:4},{label:"R5",value:5},{label:"R6",value:6},{label:"R7",value:7}],jumpType:[{label:"跳转到特定页面",value:0},{label:"打开游戏",value:2}],domains:[{value:"",key:Date.now()}],trigger1:[{label:"按账号",value:1},{label:"按角色",value:2}],trigger2:[{label:"每日领取",value:1},{label:"每周领取",value:2},{label:"每月领取",value:3},{label:"每年领取",value:4},{label:"当前等级领取",value:5,disabled:!0},{label:"终身领取",value:6}],progressList:[{label:"每次需完成1个行为",value:1},{label:"每次需完成2个行为",value:2},{label:"每次需完成3个行为",value:3},{label:"每次需完成4个行为",value:4},{label:"每次需完成5个行为",value:5},{label:"每次需完成6个行为",value:6},{label:"每次需完成7个行为",value:7},{label:"每次需完成8个行为",value:8},{label:"每次需完成9个行为",value:9},{label:"每次需完成10个行为",value:10}],rewardType:[{label:"平台道具奖励",value:0},{label:"游戏道具奖励",value:1},{label:"积分",value:2}],optionsGifts:[],optionsLang:[],optionsLangKey:[],optionsOriginTaskEvent:[],optionsTaskEvent:[],stocksTypeList:[{label:"每日兑换上限",value:1},{label:"每周兑换上限",value:2},{label:"每月兑换上限",value:3},{label:"每年兑换上限",value:4},{label:"总兑换上限",value:5}]}),N=J({filterData:{task_where:[{required:!0,message:"请选择任务条件",trigger:"change"}]}}),h=U(!1),P=U(),A=s=>s&&s<Y().startOf("day");ie(()=>{j(),_({page:1,page_size:1e3})}),re(()=>G,s=>{s&&s.task_events&&s.task_events.length>0&&(y.optionsTaskEvent=s.task_events.map(e=>({originLabel:e.label,label:e.label,value:e.value})))},{immediate:!0});const j=()=>{te().then(s=>{s&&s.length>0&&(y.optionsGifts=s)})},_=s=>{ae(s).then(e=>{e.data&&e.data.length>0&&(y.optionsLang=e.data.map(b=>({label:b.zh_cn,value:b.key})),y.optionsLangKey=[...y.optionsLang])})},u=s=>{y.optionsTwo.find(e=>e.value===s)},x=(s,e)=>e.label.toLowerCase().indexOf(s.toLowerCase())>=0,$=()=>{P.value.validate().then(()=>{h.value=!0;const{id:s,...e}=a.value;e.game_growth=e.game_growth||0;const b=y.optionsGifts.find(f=>f.id===e.gift_id);if(b&&b.item_list_length>1)return h.value=!1,q.error({title:"提示",content:"所选礼包内包含多种道具/积分，请重新选择"});if(e.cost_coin===0)return h.value=!1,q.error({title:"提示",content:"兑换所需积分不能为0"});e.is_time_limit===1&&e.times?(e.start_time=e.times[0],e.end_time=e.times[1]):(e.start_time=0,e.end_time=0),e.online_type===2?e.online_timing=e.online_timing_str?Y.utc(e.online_timing_str).unix():0:e.online_timing=0,p.editId?a.value.status?q.confirm({title:"提示",content:"当前商品已上线，保存后立即生效，请确认后操作",okText:"确定",cancelText:"取消",onOk:()=>{ee(s,e).then(()=>{k("close"),k("refresh",!0)}).catch(()=>{}).finally(()=>{h.value=!1})},onCancel:()=>{h.value=!1}}):ee(s,e).then(()=>{k("close"),k("refresh",!0)}).catch(()=>{}).finally(()=>{h.value=!1}):ke(e).then(()=>{k("close"),k("refresh")}).catch(()=>{}).finally(()=>{h.value=!1}),setTimeout(()=>{h.value=!1},1e3)}).catch(()=>{})};return(s,e)=>{const b=r("a-select"),f=r("a-form-item"),V=r("a-input-number"),S=r("a-space"),R=r("a-radio-group"),B=r("a-date-picker"),E=r("SelectDateTime"),z=r("SelectLang"),K=r("a-button"),n=r("a-form"),d=r("a-spin"),H=r("a-drawer");return c(),T(H,{open:t(M),"onUpdate:open":e[17]||(e[17]=i=>me(M)?M.value=i:null),title:p.editId?"编辑商品":"新增商品",maskClosable:!1,width:800,onAfterOpenChange:e[18]||(e[18]=i=>!i&&k("close"))},{default:o(()=>[l(d,{spinning:t(D)},{default:o(()=>[l(n,{model:t(a),rules:t(N),name:"basic",ref_key:"formRef",ref:P,"label-col":{span:4},"wrapper-col":{span:16},autocomplete:"off"},{default:o(()=>[l(f,{label:"商品名称",name:"gift_id",rules:[{required:!0,message:"请选择商品名称"}]},{default:o(()=>[l(b,{style:{width:"100%"},allowClear:"","show-search":"","filter-option":x,value:t(a).gift_id,"onUpdate:value":e[0]||(e[0]=i=>t(a).gift_id=i),options:t(y).optionsGifts,placeholder:"请选择商品名称",onChange:u},null,8,["value","options"])]),_:1}),l(f,{label:"兑换所需积分",name:"cost_coin",rules:[{required:!0,message:"请填写兑换所需积分"}]},{default:o(()=>[l(V,{style:{width:"100%"},value:t(a).cost_coin,"onUpdate:value":e[1]||(e[1]=i=>t(a).cost_coin=i),min:1,max:15e5,placeholder:"请填写兑换所需积分"},null,8,["value"])]),_:1}),l(f,{label:"等级限制",name:"vip",rules:[{required:!0,message:"请选择等级限制"}]},{default:o(()=>[l(b,{style:{width:"100%"},value:t(a).vip,"onUpdate:value":e[2]||(e[2]=i=>t(a).vip=i),options:t(y).optionsVip},null,8,["value","options"])]),_:1}),l(f,{label:"游戏成长值",name:"game_growth"},{default:o(()=>[l(V,{style:{width:"100%"},value:t(a).game_growth,"onUpdate:value":e[3]||(e[3]=i=>t(a).game_growth=i),placeholder:"请输入游戏成长值"},null,8,["value"])]),_:1}),l(f,{label:"领取维度及周期",name:"cycle_times",rules:[{required:!0,message:"请填写领取维度及周期"}]},{default:o(()=>[l(S,{nowrap:"",class:"space-wrapper",style:{gap:"5px"}},{default:o(()=>[l(b,{style:{width:"100%"},value:t(a).cycle_shape,"onUpdate:value":e[4]||(e[4]=i=>t(a).cycle_shape=i),options:t(y).trigger1},null,8,["value","options"]),l(b,{style:{width:"100%"},value:t(a).cycle_type,"onUpdate:value":e[5]||(e[5]=i=>t(a).cycle_type=i),options:t(y).trigger2},null,8,["value","options"]),l(V,{style:{width:"100%"},value:t(a).cycle_times,"onUpdate:value":e[6]||(e[6]=i=>t(a).cycle_times=i),min:1,max:9999,placeholder:"请填写次数"},null,8,["value"]),v("次 ")]),_:1})]),_:1}),l(f,{label:"商品上线时间",onChange:e[8]||(e[8]=i=>t(a).times=void 0),rules:[{required:!0,message:"请选择起止时间"}]},{default:o(()=>[l(R,{value:t(a).online_type,"onUpdate:value":e[7]||(e[7]=i=>t(a).online_type=i),options:[{label:"立即上线",value:1},{label:"定时上线",value:2}]},null,8,["value"])]),_:1}),t(a).online_type===2?(c(),T(f,{key:0,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"online_timing_str",rules:[{required:!0,message:"请选择结束时间"}]},{default:o(()=>[l(B,{style:{width:"100%"},value:t(a).online_timing_str,"onUpdate:value":e[9]||(e[9]=i=>t(a).online_timing_str=i),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":A,"show-now":!1,"show-time":""},null,8,["value"])]),_:1})):g("",!0),l(f,{label:"商品有效期",onChange:e[11]||(e[11]=i=>t(a).times=void 0),rules:[{required:!0,message:"请选择起止时间"}]},{default:o(()=>[l(R,{value:t(a).is_time_limit,"onUpdate:value":e[10]||(e[10]=i=>t(a).is_time_limit=i),options:[{label:"永久",value:0},{label:"定时",value:1}]},null,8,["value"])]),_:1}),t(a).is_time_limit===1?(c(),T(f,{key:1,class:"time-limit-item","wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:o(()=>[l(E,{value:t(a).times,"onUpdate:value":e[12]||(e[12]=i=>t(a).times=i)},null,8,["value"])]),_:1})):g("",!0),l(f,{label:"是否限量兑换",rules:[{required:!0,message:"请选择是否限量兑换"}]},{default:o(()=>[l(R,{value:t(a).use_stocks,"onUpdate:value":e[13]||(e[13]=i=>t(a).use_stocks=i),options:[{label:"否",value:0},{label:"是",value:1}]},null,8,["value"])]),_:1}),t(a).use_stocks===1?(c(),T(f,{key:2,class:"stocks-item","wrapper-col":{offset:6,span:16},name:"item_limit_type",rules:[{required:!0,message:"请选择兑换上限类型"}]},{default:o(()=>[l(b,{style:{width:"50%"},value:t(a).item_limit_type,"onUpdate:value":e[14]||(e[14]=i=>t(a).item_limit_type=i),options:t(y).stocksTypeList},null,8,["value","options"]),l(V,{value:t(a).stocks_num,"onUpdate:value":e[15]||(e[15]=i=>t(a).stocks_num=i),min:1,max:9999,placeholder:"请填写兑换上限"},null,8,["value"])]),_:1})):g("",!0),l(f,{label:"使用说明",name:"use_desc_key"},{default:o(()=>[l(z,{value:t(a).use_desc_key,"onUpdate:value":e[16]||(e[16]=i=>t(a).use_desc_key=i)},null,8,["value"])]),_:1}),l(f,{"wrapper-col":{offset:10,span:12}},{default:o(()=>[l(K,{type:"primary",onClick:$,loading:t(h)},{default:o(()=>[v("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}});const De=se(Ce,[["__scopeId","data-v-d8b9f795"]]),oe=m=>(fe("data-v-0626a7ec"),m=m(),ge(),m),Le=oe(()=>Q("div",{class:"preview-desc"},"库存调整后立即生效，请确认修改后保存",-1)),Ye=oe(()=>Q("strong",null,"→",-1)),Te=le({__name:"Index",setup(m){Y.extend(ne);const k=[{dataIndex:"id",key:"id",title:"商品ID",width:"100px"},{dataIndex:"item_list",key:"item_list",title:"道具详情",width:"130px"},{dataIndex:"cost_coin",key:"cost_coin",title:"兑换积分",width:"130px"},{dataIndex:"vip",key:"vip",title:"等级限制",width:"130px"},{dataIndex:"game_growth",key:"game_growth",title:"游戏成长值",width:"130px"},{dataIndex:"cycle_shape",key:"cycle_shape",title:"领取维度",width:"130px"},{dataIndex:"cycle_type",key:"cycle_type",title:"周期",width:"130px"},{dataIndex:"cycle_times",key:"cycle_times",title:"周期内次数",width:"130px"},{dataIndex:"status",key:"status",title:"状态",width:"130px"},{dataIndex:"online_type",key:"online_type",title:"上线时间",width:"130px"},{dataIndex:"is_time_limit",key:"is_time_limit",title:"有效期",width:"130px"},{dataIndex:"stocks_num",key:"stocks_num",title:"兑换上限",width:"100px"},{dataIndex:"now_stocks",key:"now_stocks",title:"实时库存",width:"100px"},{dataIndex:"action",key:"action",title:"操作",width:"200px",fixed:"right",align:"center"}],p=J({editVisible:!1,editId:0,searchParams:{task_category:null,vip_restriction:null},previewOpen:!1,previewData:{},optionsTaskCategory:[{label:"日常任务",value:1},{label:"活跃任务",value:2},{label:"成长任务",value:3},{label:"游戏任务",value:4}],optionsVip:[{label:"LV1",value:1},{label:"LV2",value:2},{label:"LV3",value:3},{label:"LV4",value:4},{label:"LV5",value:5},{label:"LV6",value:6},{label:"LV7",value:7}],optionsGift:[{label:"每日礼包",value:1},{label:"每周礼包",value:2},{label:"每月礼包",value:3},{label:"每年礼包",value:4},{label:"等级礼包",value:5},{label:"活动礼包",value:6}],optionsGift1:[{label:"日",value:1},{label:"周",value:2},{label:"月",value:3},{label:"年",value:4},{label:"等级",value:5},{label:"终身",value:6}],trigger1:[{label:"账号",value:1},{label:"角色",value:2}],optionsGifts:[],optionsLang:[]}),G=U([]),M=U(),D=_=>M.value.requestTableData(!_);ie(()=>{F(),a({page:1,page_size:1e3}),y("task_desc")});const F=()=>{te().then(_=>{_&&_.length>0&&(p.optionsGifts=_)})},a=_=>{ae(_).then(u=>{u.data&&u.data.length>0&&(p.optionsLang=u.data.map(x=>({label:x.zh_cn,value:x.key})))})},y=async _=>{const u=await pe({key:_});return(u==null?void 0:u.zh_cn)||"-"},N=_=>{p.previewData=_,p.previewOpen=!0},h=()=>{const{id:_,change_stocks:u}=p.previewData;if(u<0)return q.error({title:"提示",content:"所选礼包内包含多种道具/积分，请重新选择"});Ie({id:_,now_stocks:u}).then(()=>{p.previewOpen=!1,D(!0)})},P=(_,u)=>{p.editVisible=_,p.editId=u||0},A=_=>_e("删除后，用户将看不到该商品，请确认后删除",we,{petId:_}).then(()=>D()),j=(_,u,x)=>{G.value[x]=!0,u.status=1-_;const $=_===1?"当前商品未上线，是否确认现在上线":"当前商品已上线，是否确认现在下线？";q.confirm({title:"提示",content:$,okText:"确定",cancelText:"取消",onOk:()=>{G.value[x]=!1,he(u.id,_).finally(()=>D(!0))},onCancel:()=>{G.value[x]=!1}})};return(_,u)=>{const x=r("UploadBtn"),$=r("PlusOutlined"),s=r("a-button"),e=r("a-space"),b=r("a-switch"),f=r("a-tooltip"),V=r("a-divider"),S=r("a-typography-link"),R=r("CustomTable"),B=r("a-input-number"),E=r("a-form-item"),z=r("a-modal"),K=ve("has");return c(),I(C,null,[l(R,{ref_key:"RefCustomTable",ref:M,"data-api":t(ye),params:t(p).searchParams,columns:k},{top:o(()=>[l(e,{direction:"vertical"},{default:o(()=>[l(e,{wrap:"",style:{padding:"20px 0",gap:"20px"}},{default:o(()=>[ce(l(x,{ref:"uploadBtn",onUploadSuccess:D,downloadApi:t(xe),fileType:"points-products",page:t(de).PRODUCTCONFIG},null,8,["downloadApi","page"]),[[K,"Operation"]]),l(s,{type:"primary",onClick:u[0]||(u[0]=n=>P(!0))},{icon:o(()=>[l($)]),default:o(()=>[v(" 新增商品 ")]),_:1})]),_:1})]),_:1})]),bodyCell:o(({column:n,record:d,index:H})=>{var i,W,X,Z;return[n.key==="package"?(c(),I(C,{key:0},[v(L((i=d[n.key])==null?void 0:i.name),1)],64)):g("",!0),n.key==="item_list"?(c(),I(C,{key:1},[v(L(d[n.key]&&d[n.key].length>0?d[n.key][0].name+"*"+d[n.key][0].num:""),1)],64)):g("",!0),n.dataIndex==="vip"?(c(),I(C,{key:2},[v(" R"+L(d[n.key]),1)],64)):g("",!0),n.dataIndex==="cycle_shape"?(c(),I(C,{key:3},[v(L((W=t(p).trigger1.find(w=>w.value===d[n.key]))==null?void 0:W.label),1)],64)):g("",!0),n.dataIndex==="cycle_type"?(c(),I(C,{key:4},[v(L((X=t(p).optionsGift1.find(w=>w.value===d[n.key]))==null?void 0:X.label),1)],64)):g("",!0),n.dataIndex==="progress"?(c(),I(C,{key:5},[v(L(d[n.key]?d[n.key]:"无限制"),1)],64)):g("",!0),n.dataIndex==="pkg_id"?(c(),I(C,{key:6},[v(L((Z=t(p).optionsGifts.find(w=>w.value==d[n.key]))==null?void 0:Z.label),1)],64)):g("",!0),n.key==="status"?(c(),T(b,{key:7,checked:d.status,"onUpdate:checked":w=>d.status=w,checkedValue:1,unCheckedValue:0,"checked-children":"已上线","un-checked-children":"未上线",loading:t(G)[H],onClick:w=>j(w,d,H)},null,8,["checked","onUpdate:checked","loading","onClick"])):g("",!0),n.dataIndex==="online_type"&&d[n.key]===2?(c(),T(f,{key:8,title:`${t(Y).utc(d.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss")}`,placement:"topLeft"},{default:o(()=>[v(L(t(Y).utc(d.online_timing*1e3).format("YYYY-MM-DD HH:mm:ss")),1)]),_:2},1032,["title"])):g("",!0),n.dataIndex==="online_type"&&d[n.key]===1?(c(),I(C,{key:9},[v("立即上线")],64)):g("",!0),n.dataIndex==="is_time_limit"&&d[n.key]===1?(c(),T(f,{key:10,title:`${t(Y).utc(d.start_time*1e3).format("YYYY-MM-DD HH:mm:ss")} -
        ${t(Y).utc(d.end_time*1e3).format("YYYY-MM-DD HH:mm:ss")}`,placement:"topLeft"},{default:o(()=>[v(" 定时 ")]),_:2},1032,["title"])):g("",!0),n.dataIndex==="is_time_limit"&&d[n.key]===0?(c(),I(C,{key:11},[v(" 永久 ")],64)):g("",!0),n.key==="action"?(c(),T(e,{key:12},{split:o(()=>[l(V,{type:"vertical",style:{margin:"0"}})]),default:o(()=>[l(S,{disabled:!d.stocks_num,onClick:w=>N(d)},{default:o(()=>[v("库存调整")]),_:2},1032,["disabled","onClick"]),l(S,{onClick:w=>P(!0,d.id)},{default:o(()=>[v("编辑")]),_:2},1032,["onClick"]),l(S,{type:"danger",danger:"",onClick:w=>A(d.id)},{default:o(()=>[v("删除")]),_:2},1032,["onClick"])]),_:2},1024)):g("",!0)]}),_:1},8,["data-api","params"]),t(p).editVisible?(c(),T(De,{key:0,"edit-id":t(p).editId,onClose:u[1]||(u[1]=n=>P(!1)),onRefresh:D},null,8,["edit-id"])):g("",!0),l(z,{class:"preview-modal",visible:t(p).previewOpen,"onUpdate:visible":u[4]||(u[4]=n=>t(p).previewOpen=n),title:"库存调整",onCancel:u[5]||(u[5]=n=>t(p).previewOpen=!1),onOk:u[6]||(u[6]=n=>t(p).previewOpen=!1)},{footer:o(()=>[l(s,{key:"back",onClick:u[3]||(u[3]=n=>t(p).previewOpen=!1)},{default:o(()=>[v(" 取消 ")]),_:1}),l(s,{key:"submit",type:"primary",onClick:h},{default:o(()=>[v(" 确认 ")]),_:1})]),default:o(()=>[Le,l(E,{label:"实时库存",class:"preview-item"},{default:o(()=>[Q("strong",null,L(t(p).previewData.now_stocks),1),Ye,l(B,{value:t(p).previewData.change_stocks,"onUpdate:value":u[2]||(u[2]=n=>t(p).previewData.change_stocks=n),min:0,max:t(p).previewData.stocks_num},null,8,["value","max"])]),_:1})]),_:1},8,["visible"])],64)}}});const Me=se(Te,[["__scopeId","data-v-0626a7ec"]]);export{Me as default};
