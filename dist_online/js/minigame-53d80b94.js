import{y as I,K as P,m as B}from"./common-b8130258.js";import{d as A,f as S,g as F,r as C,a as s,o as f,c as g,w as i,b as l,u as t,l as v,e as k,k as R,F as G,i as K,h as E,t as V}from"./vendor-cc06403f.js";import{M as W}from"./antd-914dcf46.js";const j=[{dataIndex:"id",key:"id",title:"id",width:"70px"},{dataIndex:"name_key",key:"name_key",title:"小游戏名称",width:"150px"},{dataIndex:"img_id",key:"img_id",title:"游戏封面",width:"100px"},{dataIndex:"status",key:"status",title:"活动状态",width:"80px"},{dataIndex:"times",key:"times",title:"活动时间",width:"160px"},{dataIndex:"link_url",key:"link_url",title:"跳转链接",width:"200px"},{dataIndex:"filter",key:"filter",title:"筛选器",width:"60px"},{dataIndex:"updated_by",key:"updated_by",title:"更新人",width:"140px"},{dataIndex:"is_online",key:"is_online",title:"上线状态",width:"140px"},{dataIndex:"action",key:"action",title:"操作",width:"120px",fixed:"right",align:"center"}],Y={1:{type:"success",label:"进行中"},2:{type:"default",label:"未开始"},3:{type:"error",label:"已结束"}},z=u=>I.get("/api/mini-game",u),J=u=>I.post("/api/mini-game",u),Q=u=>I.get(`/api/mini-game/${u}`),X=(u,d)=>I.put(`/api/mini-game/${u}`,d),Z=({id:u})=>I.delete(`/api/mini-game/${u}`),ee=({id:u,is_online:d})=>I.put(`/api/mini-game/switch-status/${u}`,{is_online:d}),te=A({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(u,{emit:d}){const y=u;S.extend(F);const b=C(!0),h=C(!1),$=()=>{h.value=!0,Q(y.editId).then(r=>{a.value=r,r.online_at&&r.offline_at?(a.value.is_forever=0,a.value.times=[r.online_at,r.offline_at]):(a.value.is_forever=1,a.value.times=void 0)}).finally(()=>h.value=!1)};y.editId&&$();const a=C({id:0,name_key:void 0,link_url:"",img_id:"",is_forever:0,times:void 0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:""}),w=C(!1),p=C(),m=()=>{p.value.validate().then(()=>{w.value=!0;const{id:r,...e}=a.value;e.is_forever===0&&e.times?(e.online_at=e.times[0],e.offline_at=e.times[1]):(e.online_at=0,e.offline_at=""),y.editId?X(r,e).then(()=>{d("close"),d("refresh")}).catch(()=>{}).finally(()=>{w.value=!1}):J(e).then(()=>{d("close"),d("refresh")}).catch(()=>{}).finally(()=>{w.value=!1})}).catch(()=>{})};return(r,e)=>{const T=s("SelectLang"),_=s("a-form-item"),q=s("a-input"),L=s("SelectImg"),U=s("a-radio-group"),N=s("SelectDateTime"),M=s("a-radio"),o=s("SelectWithAllComp"),c=s("a-textarea"),D=s("a-button"),x=s("a-form"),O=s("a-spin"),H=s("a-drawer");return f(),g(H,{open:t(b),"onUpdate:open":e[13]||(e[13]=n=>K(b)?b.value=n:null),title:y.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:e[14]||(e[14]=n=>!n&&d("close"))},{default:i(()=>[l(O,{spinning:t(h)},{default:i(()=>[l(x,{model:t(a),name:"basic",ref_key:"formRef",ref:p,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:i(()=>[l(_,{label:"名称",name:"name_key",rules:[{required:!0,message:"请选择多语言"}]},{default:i(()=>[l(T,{value:t(a).name_key,"onUpdate:value":e[0]||(e[0]=n=>t(a).name_key=n)},null,8,["value"])]),_:1}),l(_,{label:"跳转URL",name:"link_url",rules:[{required:!0,message:"请输入跳转URL"}]},{default:i(()=>[l(q,{value:t(a).link_url,"onUpdate:value":e[1]||(e[1]=n=>t(a).link_url=n),placeholder:"请输入跳转URL"},null,8,["value"])]),_:1}),l(_,{label:"封面",name:"img_id",rules:[{required:!0,message:"请上传封面"}]},{default:i(()=>[l(L,{value:t(a).img_id,"onUpdate:value":e[2]||(e[2]=n=>t(a).img_id=n),"width-height":[200,200]},null,8,["value"])]),_:1}),l(_,{label:"活动时间",onChange:e[4]||(e[4]=n=>t(a).times=void 0)},{default:i(()=>[l(U,{value:t(a).is_forever,"onUpdate:value":e[3]||(e[3]=n=>t(a).is_forever=n),options:[{label:"永久",value:1},{label:"定时",value:0}]},null,8,["value"])]),_:1}),t(a).is_forever===0?(f(),g(_,{key:0,"wrapper-col":{offset:6,span:16},name:"times",rules:[{required:!0,message:"请选择起止时间"}]},{default:i(()=>[l(N,{value:t(a).times,"onUpdate:value":e[5]||(e[5]=n=>t(a).times=n)},null,8,["value"])]),_:1})):v("",!0),l(_,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:i(()=>[l(U,{value:t(a).is_filter,"onUpdate:value":e[6]||(e[6]=n=>t(a).is_filter=n),onChange:e[7]||(e[7]=n=>t(P)(t(a),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:i(()=>[l(M,{value:1},{default:i(()=>[k("开启")]),_:1}),l(M,{value:0},{default:i(()=>[k("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),t(a).is_filter===1?(f(),R(G,{key:1},[l(_,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:i(()=>[l(o,{value:t(a).f_os,"onUpdate:value":e[8]||(e[8]=n=>t(a).f_os=n),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),l(_,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:i(()=>[l(o,{value:t(a).lang,"onUpdate:value":e[9]||(e[9]=n=>t(a).lang=n),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),l(_,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:i(()=>[l(o,{value:t(a).f_channel,"onUpdate:value":e[10]||(e[10]=n=>t(a).f_channel=n),placeholder:"请选择渠道",type:"channels"},null,8,["value"])]),_:1}),l(_,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:i(()=>[l(c,{value:t(a).f_s_ids,"onUpdate:value":e[11]||(e[11]=n=>t(a).f_s_ids=n),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),l(_,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:i(()=>[l(c,{value:t(a).f_lv_ids,"onUpdate:value":e[12]||(e[12]=n=>t(a).f_lv_ids=n),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1})],64)):v("",!0),l(_,{"wrapper-col":{offset:6,span:16}},{default:i(()=>[l(D,{type:"primary",onClick:m,loading:t(w)},{default:i(()=>[k("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),ie=A({__name:"Index",setup(u){S.extend(F);const d=E({editVisible:!1,editId:0}),y=C(),b=()=>y.value.requestTableData(!0),h=(p,m)=>{d.editVisible=p,d.editId=m||0},$=(p,m)=>B(`确定要删除${m?"选中的":"此条"}数据吗？`,Z,{id:p}).then(()=>b()),a=C([]),w=(p,m,r)=>{a.value[r]=!0,m.is_online=1-p,W.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{a.value[r]=!1,ee({id:m.id,is_online:p}).finally(()=>{b()})},onCancel:()=>{a.value[r]=!1}})};return(p,m)=>{const r=s("PlusOutlined"),e=s("a-button"),T=s("a-image"),_=s("LangKey"),q=s("FilterCell"),L=s("a-tag"),U=s("a-switch"),N=s("a-space"),M=s("CustomTable");return f(),R(G,null,[l(M,{ref_key:"RefCustomTable",ref:y,"data-api":t(z),params:{},columns:t(j)},{leftTool:i(()=>[l(e,{type:"primary",onClick:m[0]||(m[0]=o=>h(!0))},{icon:i(()=>[l(r)]),default:i(()=>[k(" 新增 ")]),_:1})]),bodyCell:i(({record:o,column:c,index:D})=>[c.key==="img_id"?(f(),g(T,{key:0,src:o.img_id,height:60},null,8,["src"])):v("",!0),c.key==="times"?(f(),R(G,{key:1},[k(V(o.offline_at?`${t(S).utc(o.online_at*1e3).format("YYYY-MM-DD HH:mm:ss")} - ${t(S).utc(o.offline_at*1e3).format("YYYY-MM-DD HH:mm:ss")}`:"永久"),1)],64)):v("",!0),c.key==="name_key"?(f(),g(_,{key:2,"lang-key":o.name_key,i18n_name:o.i18n_name},null,8,["lang-key","i18n_name"])):v("",!0),c.key==="filter"?(f(),g(q,{key:3,record:o},null,8,["record"])):v("",!0),c.key==="status"?(f(),g(L,{key:4,color:t(Y)[o.status].type},{default:i(()=>[k(V(t(Y)[o.status].label),1)]),_:2},1032,["color"])):v("",!0),c.key==="is_online"?(f(),g(U,{key:5,checked:o.is_online,"onUpdate:checked":x=>o.is_online=x,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:t(a)[D],onClick:x=>w(x,o,D)},null,8,["checked","onUpdate:checked","loading","onClick"])):v("",!0),c.key==="action"?(f(),g(N,{key:6,size:0},{default:i(()=>[l(e,{type:"link",onClick:x=>h(!0,o.id)},{default:i(()=>[k("编辑")]),_:2},1032,["onClick"]),l(e,{type:"link",danger:"",onClick:x=>$(o.id,!1)},{default:i(()=>[k("删除")]),_:2},1032,["onClick"])]),_:2},1024)):v("",!0)]),_:1},8,["data-api","columns"]),t(d).editVisible?(f(),g(te,{key:0,"edit-id":t(d).editId,onClose:m[1]||(m[1]=o=>h(!1)),onRefresh:b},null,8,["edit-id"])):v("",!0)],64)}}});export{ie as default};
