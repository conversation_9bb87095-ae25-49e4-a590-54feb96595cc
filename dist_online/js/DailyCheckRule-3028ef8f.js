import{i as Q,j as W,k as X,d as $,l as Z}from"./common-b8130258.js";import{d as K,r as c,a as l,o as r,c as m,w as n,b as o,u as e,e as k,q as ee,t as F,i as M,k as x,F as h,s as V,v as te,l as R,h as ae}from"./vendor-cc06403f.js";import{m as ne}from"./antd-914dcf46.js";const oe=[{dataIndex:"name",key:"name",title:"规则分类"},{dataIndex:"key",key:"key",title:"规则key"},{dataIndex:"action",key:"action",title:"操作",align:"center"}],le=["innerHTML"],se=K({__name:"Form",props:["editKey"],emits:["close","refresh"],setup(q,{emit:d}){const L=q,a=c({name:void 0,key:"",file:"",content:{},last_uploaded_file:""}),i=c("en"),g=c(!0),_=c(!1);(()=>{_.value=!0,Q(L.editKey).then(y=>{a.value={...a.value,...y}}).finally(()=>_.value=!1)})();const b=c(!1),C=c(),T=()=>{C.value.validate().then(()=>{b.value=!0,W(a.value).then(()=>{d("close"),d("refresh")}).catch(()=>{}).finally(()=>{b.value=!1})}).catch(()=>{})},w=c({fileList:[],url:""}),D=async y=>{if(!y)return!1;try{const t=new FormData;t.append("file",y),t.append("key",a.value.key),X(t).then(p=>{a.value.content=p.content,ne.success("上传解析成功")}).catch()}catch{}return!1},U=()=>{a.value.last_uploaded_file?window.open(a.value.last_uploaded_file,"_blank"):$(a.value.key)},N=async(y,t)=>{let p=!0;if(a.value.key==="faq_rules")p=!1;else for(const u in t){if((u==="en"||u==="zh_cn")&&!t[u].text)return Promise.reject("英文或中文内容不可为空");t[u].text||(p=!1)}return p?Promise.reject("内容不可为空"):Promise.resolve()};return(y,t)=>{const p=l("a-input"),u=l("a-form-item"),B=l("upload-outlined"),O=l("a-button"),I=l("CloudDownloadOutlined"),E=l("a-space"),P=l("a-upload"),S=l("a-radio"),j=l("a-radio-group"),z=l("a-descriptions-item"),A=l("a-descriptions"),H=l("Editor"),Y=l("a-form"),J=l("a-spin"),G=l("a-drawer");return r(),m(G,{open:e(g),"onUpdate:open":t[6]||(t[6]=s=>M(g)?g.value=s:null),title:"编辑规则",maskClosable:!1,width:600,onAfterOpenChange:t[7]||(t[7]=s=>!s&&d("close"))},{default:n(()=>[o(J,{spinning:e(_)},{default:n(()=>[o(Y,{model:e(a),name:"basic",ref_key:"formRef",ref:C,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:n(()=>[o(u,{label:"规则分类",name:"name",rules:[{required:!0,message:"请选择规则分类"}]},{default:n(()=>[o(p,{value:e(a).name,"onUpdate:value":t[0]||(t[0]=s=>e(a).name=s),placeholder:"请输入规则分类"},null,8,["value"])]),_:1}),o(u,{label:"规则KEY",name:"key",rules:[{required:!0,message:"请输入规则KEY"}]},{default:n(()=>[o(p,{value:e(a).key,"onUpdate:value":t[1]||(t[1]=s=>e(a).key=s),placeholder:"请输入规则key",disabled:""},null,8,["value"])]),_:1}),o(u,{label:"规则文档",name:"file"},{default:n(()=>[o(P,{name:"file",style:{height:"110px"},"file-list":e(w).fileList,"onUpdate:fileList":t[2]||(t[2]=s=>e(w).fileList=s),action:"#",accept:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel","before-upload":D,onRemove:t[3]||(t[3]=s=>e(w).url="")},{default:n(()=>[o(E,null,{default:n(()=>[o(O,null,{default:n(()=>[o(B),k(" 点击上传 ")]),_:1}),o(O,{type:"primary",onClick:ee(U,["stop"])},{icon:n(()=>[o(I)]),default:n(()=>[k(F(e(a).last_uploaded_file?"下载文档":"下载模板"),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["file-list"])]),_:1}),o(u,{label:"按语种编辑",name:"content",rules:[{validator:N}]},{default:n(()=>[o(j,{value:e(i),"onUpdate:value":t[4]||(t[4]=s=>M(i)?i.value=s:null),style:{"line-height":"32px","margin-bottom":"8px"}},{default:n(()=>[o(E,{wrap:""},{default:n(()=>[(r(!0),x(h,null,V(e(a).content,(s,v)=>(r(),m(S,{key:v,value:v},{default:n(()=>[k(F(v),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["value"]),e(i)?(r(),x(h,{key:0},[e(a).key==="faq_rules"?(r(),x(h,{key:0},[e(a).content[e(i)].length?(r(),m(A,{key:0,layout:"vertical",bordered:"",column:1,size:"small"},{default:n(()=>[(r(!0),x(h,null,V(e(a).content[e(i)],(s,v)=>(r(),m(z,{label:s[`question${v+1}`]},{default:n(()=>[te("span",{innerHTML:s[`answer${v+1}`]},null,8,le)]),_:2},1032,["label"]))),256))]),_:1})):R("",!0)],64)):JSON.stringify(e(a).content)!=="{}"?(r(),m(H,{key:e(i),value:e(a).content[e(i)].text,"onUpdate:value":t[5]||(t[5]=s=>e(a).content[e(i)].text=s)},null,8,["value"])):R("",!0)],64)):R("",!0)]),_:1},8,["rules"]),o(u,{"wrapper-col":{offset:6,span:16}},{default:n(()=>[o(O,{type:"primary",onClick:T,loading:e(b)},{default:n(()=>[k("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open"])}}}),de=K({__name:"Index",setup(q){const d=ae({editVisible:!1,key:""}),L=c(),a=()=>L.value.requestTableData(!0),i=(_,f)=>{d.editVisible=_,d.key=f||""},g=()=>{$("rule")};return(_,f)=>{const b=l("a-divider"),C=l("a-typography-link"),T=l("a-space"),w=l("CustomTable");return r(),x(h,null,[o(w,{ref_key:"RefCustomTable",ref:L,"data-api":e(Z),params:{},columns:e(oe),pagination:!1},{bodyCell:n(({record:D,column:U})=>[U.key==="action"?(r(),m(T,{key:0},{split:n(()=>[o(b,{type:"vertical",style:{margin:"0"}})]),default:n(()=>[o(C,{onClick:N=>i(!0,D.key)},{default:n(()=>[k("编辑")]),_:2},1032,["onClick"]),o(C,{onClick:f[0]||(f[0]=N=>g())},{default:n(()=>[k("下载模板")]),_:1})]),_:2},1024)):R("",!0)]),_:1},8,["data-api","columns"]),e(d).editVisible?(r(),m(se,{key:0,"edit-key":e(d).key,onClose:f[1]||(f[1]=D=>i(!1)),onRefresh:a},null,8,["edit-key"])):R("",!0)],64)}}});export{de as default};
