import{z as B,K as M,aA as W,aB as P,aC as z,aD as G,m as J,aE as K,aF as H}from"./common-b8130258.js";import{d as A,bz as Q,r as m,a as s,o as v,c as w,w as n,b as l,u as t,e as g,t as q,k as V,F as N,l as x,i as X,h as Y}from"./vendor-cc06403f.js";import{M as Z}from"./antd-914dcf46.js";const ee=[{dataIndex:"id",key:"id",title:"id",width:"70px"},{dataIndex:"game_project",key:"game_project",title:"游戏",width:"100px"},{dataIndex:"status",key:"status",title:"状态",width:"100px"},{dataIndex:"filter",key:"filter",title:"筛选器",width:"60px"},{dataIndex:"action",key:"action",title:"操作",width:"100px",fixed:"right",align:"center"}],te=A({__name:"Detail",props:["editId"],emits:["close","refresh"],setup(j,{emit:u}){const _=j,k=B(),{configState:h}=Q(k),{getGameChannel:T}=k,f=m(!0),y=m(!1),r=()=>{y.value=!0,W(_.editId).then(C=>{e.value=C}).finally(()=>y.value=!1)};_.editId&&r();const e=m({id:0,game_project:void 0,status:0,is_filter:0,f_os:"",f_channel:"",lang:"",f_s_ids:"",f_lv_ids:"",uids:""}),d=m(!1),I=m(),D=()=>{I.value.validate().then(()=>{if(d.value=!0,JSON.stringify(e.value),_.editId){const{id:C,...a}=e.value;a.uids=a.uids.replace(/\s+/g,"").replace(/，/g,",").replace(/,+/g,","),P(C,a).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{d.value=!1})}else z(e.value).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{d.value=!1})}).catch(()=>{})};return(C,a)=>{const R=s("a-select"),i=s("a-form-item"),S=s("a-radio"),c=s("a-radio-group"),b=s("SelectWithAllComp"),U=s("SelectWithAll"),p=s("a-textarea"),F=s("a-button"),O=s("a-form"),$=s("a-spin"),E=s("a-drawer");return v(),w(E,{open:t(f),"onUpdate:open":a[10]||(a[10]=o=>X(f)?f.value=o:null),title:_.editId?"编辑":"新增",maskClosable:!1,width:500,onAfterOpenChange:a[11]||(a[11]=o=>!o&&u("close"))},{default:n(()=>[l($,{spinning:t(y)},{default:n(()=>[l(O,{model:t(e),name:"basic",ref_key:"formRef",ref:I,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off",class:"modal-form"},{default:n(()=>[l(i,{label:"游戏",name:"game_project",rules:[{required:!0,message:"请选择游戏"}]},{default:n(()=>[l(R,{value:t(e).game_project,"onUpdate:value":a[0]||(a[0]=o=>t(e).game_project=o),placeholder:"请选择游戏",options:t(h).game_projects,onChange:a[1]||(a[1]=()=>t(e).f_channel=void 0)},{option:n(({value:o,label:L})=>[g(q(L)+"-"+q(o),1)]),_:1},8,["value","options"])]),_:1}),l(i,{label:"筛选器",name:"is_filter",rules:[{required:!0,message:"请选择筛选器"}]},{default:n(()=>[l(c,{value:t(e).is_filter,"onUpdate:value":a[2]||(a[2]=o=>t(e).is_filter=o),onChange:a[3]||(a[3]=o=>t(M)(t(e),["f_os","f_channel","lang","f_s_ids","f_lv_ids"]))},{default:n(()=>[l(S,{value:1},{default:n(()=>[g("开启")]),_:1}),l(S,{value:0},{default:n(()=>[g("关闭")]),_:1})]),_:1},8,["value"])]),_:1}),t(e).is_filter===1?(v(),V(N,{key:0},[l(i,{label:"操作系统",name:"f_os",rules:[{required:!0,message:"请选择操作系统"}]},{default:n(()=>[l(b,{value:t(e).f_os,"onUpdate:value":a[4]||(a[4]=o=>t(e).f_os=o),placeholder:"请选择操作系统",type:"platform"},null,8,["value"])]),_:1}),l(i,{label:"语种",name:"lang",rules:[{required:!0,message:"请选择语种"}]},{default:n(()=>[l(b,{value:t(e).lang,"onUpdate:value":a[5]||(a[5]=o=>t(e).lang=o),placeholder:"请选择语种",type:"langs"},null,8,["value"])]),_:1}),l(i,{label:"渠道",name:"f_channel",rules:[{required:!0,message:"请选择渠道"}]},{default:n(()=>[l(U,{value:t(e).f_channel,"onUpdate:value":a[6]||(a[6]=o=>t(e).f_channel=o),placeholder:"请选择渠道",options:t(e).game_project?t(T)(t(e).game_project||""):[]},null,8,["value","options"])]),_:1}),l(i,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:n(()=>[l(p,{value:t(e).f_s_ids,"onUpdate:value":a[7]||(a[7]=o=>t(e).f_s_ids=o),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),l(i,{label:"城堡等级",name:"f_lv_ids",rules:[{required:!0,message:"请输入城堡等级"}]},{default:n(()=>[l(p,{value:t(e).f_lv_ids,"onUpdate:value":a[8]||(a[8]=o=>t(e).f_lv_ids=o),placeholder:"请输入城堡等级，例如1,7-9999","allow-clear":""},null,8,["value"])]),_:1}),l(i,{label:"uid白名单",name:"uids"},{default:n(()=>[l(p,{value:t(e).uids,"onUpdate:value":a[9]||(a[9]=o=>t(e).uids=o),placeholder:"请输入uid，多个uid以“,”隔开","allow-clear":""},null,8,["value"])]),_:1})],64)):x("",!0),l(i,{"wrapper-col":{offset:6,span:16}},{default:n(()=>[l(F,{type:"primary",onClick:D,loading:t(d)},{default:n(()=>[g("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),oe=A({__name:"Index",setup(j){const u=Y({editVisible:!1,editId:0}),_=m(),k=()=>_.value.requestTableData(!0),h=(r,e)=>{u.editVisible=r,u.editId=e||0},T=r=>J("删除后游戏内私域融合入口将消失，请谨慎操作",H,{id:r}).then(()=>k()),f=m([]),y=(r,e,d)=>{f.value[d]=!0,e.status=1-r,Z.confirm({title:"提示",content:r===0?"关闭后游戏内私域融合入口将消失，请谨慎操作，是否确认关闭？":"开启后游戏内将展示私域合入口，实时生效，是否确认开启？",okText:"确定",cancelText:"取消",onOk:()=>{f.value[d]=!1,K(e.id,r).finally(()=>k())},onCancel:()=>{f.value[d]=!1}})};return(r,e)=>{const d=s("PlusOutlined"),I=s("a-button"),D=s("a-switch"),C=s("FilterCell"),a=s("a-divider"),R=s("a-typography-link"),i=s("a-space"),S=s("CustomTable");return v(),V(N,null,[l(S,{ref_key:"RefCustomTable",ref:_,"data-api":t(G),params:{},columns:t(ee)},{leftTool:n(()=>[l(I,{type:"primary",onClick:e[0]||(e[0]=c=>h(!0))},{icon:n(()=>[l(d)]),default:n(()=>[g(" 新增 ")]),_:1})]),bodyCell:n(({record:c,column:b,index:U})=>[b.key==="status"?(v(),w(D,{key:0,checked:c.status,"onUpdate:checked":p=>c.status=p,checkedValue:1,unCheckedValue:0,"checked-children":"开启","un-checked-children":"关闭",loading:t(f)[U],onClick:p=>y(p,c,U)},null,8,["checked","onUpdate:checked","loading","onClick"])):x("",!0),b.key==="filter"?(v(),w(C,{key:1,record:c},null,8,["record"])):x("",!0),b.key==="action"?(v(),w(i,{key:2},{split:n(()=>[l(a,{type:"vertical",style:{margin:"0"}})]),default:n(()=>[l(R,{onClick:p=>h(!0,c.id)},{default:n(()=>[g("编辑")]),_:2},1032,["onClick"]),l(R,{type:"danger",onClick:p=>T(c.id)},{default:n(()=>[g("删除")]),_:2},1032,["onClick"])]),_:2},1024)):x("",!0)]),_:1},8,["data-api","columns"]),t(u).editVisible?(v(),w(te,{key:0,"edit-id":t(u).editId,onClose:e[1]||(e[1]=c=>h(!1)),onRefresh:k},null,8,["edit-id"])):x("",!0)],64)}}});export{oe as default};
