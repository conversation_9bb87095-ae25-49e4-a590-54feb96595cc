server {
  listen 80;
  server_name zone-admin.funplus.com;
  add_header 'Access-Control-Allow-Origin' '*';
  add_header 'Access-Control-Allow-Credentials' 'true';
  add_header 'Access-Control-Allow-Headers' '*';
  add_header 'Access-Control-Expose-Headers' '*';

  location / {
    gzip on;
    gzip_types text/plain application/x-javascript text/css application/xml application/json text/javascript application/x-httpd-php application/javascript image/jpeg image/gif image/png;
    root  /data/nginx/dist;
    index  index.php index.html index.htm;
    try_files $uri $uri/ /index.html;
  }
  
  location ^~ /backend/{
    gzip on;
    gzip_types text/plain application/x-javascript text/css application/xml application/json text/javascript application/x-httpd-php application/javascript image/jpeg image/gif image/png;
    resolver ******* valid=60s;
    resolver_timeout 3s;
    proxy_pass https://admin-center.funplus.com/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
}
