import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath } from 'url'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import { visualizer } from 'rollup-plugin-visualizer'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { manualChunksPlugin } from 'vite-plugin-webpackchunkname'

const pathSrc = path.resolve(__dirname, 'src')
const { cdn, tag } = process.env
const branch = tag ? tag.split('_')[0] : ''
const version = tag ? tag.split('_')[2] : ''
const base = branch === 'master' ? cdn : '/'
console.log('base', base)
console.log('version', version)

// https://vitejs.dev/config/
export default ({ mode }) => {
  return defineConfig({
    server: {
      host: true,
      proxy: {
        '/backend': {
          target: 'https://admin-center-release.nenglianghe.cn/backend', // release
          // target: 'https://admin-center-master.nenglianghe.cn/backend', // master
          // target: 'https://admin-center.funplus.com/backend',
          changeOrigin: true, // 是否跨域
          ws: false, // 是否支持websocket
          secure: false, // 如果是https接口，需要配置这个参数
          rewrite: (path) => path.replace(/^\/backend/, '')
        }
      }
    },
    build: {
      outDir: branch === 'master' ? `${version}/dist` : `dist_${mode}`,
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
          assetFileNames: '[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
          manualChunks: function (id) {
            if (id.includes('ant-design-vue')) {
              return 'antd'
            }
          }
        }
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/_variable.scss" as *;`
        }
      }
    },
    plugins: [
      manualChunksPlugin(),
      AutoImport({
        // Auto import functions from Vue, e.g. ref, reactive, toRef...
        // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        imports: [
          'vue',
          'vue-router',
          {
            'ant-design-vue': ['message', 'Modal', 'notification']
          }
        ],
        dts: path.resolve(pathSrc, 'auto-imports.d.ts'),
        dirs: [path.resolve(pathSrc, 'composables'), path.resolve(pathSrc, 'utils'), path.resolve(pathSrc, 'hooks')],
        vueTemplate: true
      }),
      visualizer(),
      vue(),
      vueJsx()
    ],
    esbuild: {
      pure: mode === 'online' ? ['console.log', 'debugger'] : []
    }
  })
}
