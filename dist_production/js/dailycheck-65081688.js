import{C as z,D as H,d as J,E as Y,m as V,F as Q,G as W,H as X,I as Z,P as ee,J as te}from"./common-72b741fb.js";import{d as N,r as k,a as n,o as f,c as L,w as t,b as e,u as a,e as r,i as ae,f as le,g as ne,h as j,j as oe,k as U,F as S,l as T,t as A,m as ie,n as se,p as de}from"./vendor-70efaa3a.js";import{M as re}from"./antd-016b48d6.js";const ue=[{dataIndex:"id",key:"id",title:"id",width:"100px"},{dataIndex:"filter",key:"filter",title:"服务器",width:"70px"},{dataIndex:"bg_img",key:"bg_img",title:"签到背景图",width:"150px"},{dataIndex:"month",key:"month",title:"月份",width:"70px"},{dataIndex:"status",key:"status",title:"状态",width:"80px"},{dataIndex:"update_by",key:"update_by",title:"更新人",width:"120px"},{dataIndex:"action",key:"action",title:"操作",width:"180px",fixed:"right",align:"center"}],pe=[{dataIndex:"day",key:"day",title:"奖励天数",width:"80px"},{dataIndex:"gift_type",key:"gift_type",title:"奖励类型",width:"110px"},{dataIndex:"pkg_id",key:"pkg_id",title:"礼包 id",width:"100px"},{dataIndex:"gift_name",key:"gift_name",title:"礼包名称",width:"150px"},{dataIndex:"vip_restriction",key:"vip_restriction",title:"奖励等级需求",width:"90px"},{dataIndex:"resigning_consumption",key:"resigning_consumption",title:"补签消耗",width:"90px"}],E=[{value:1,label:"1月"},{value:2,label:"2月"},{value:3,label:"3月"},{value:4,label:"4月"},{value:5,label:"5月"},{value:6,label:"6月"},{value:7,label:"7月"},{value:8,label:"8月"},{value:9,label:"9月"},{value:10,label:"10月"},{value:11,label:"11月"},{value:12,label:"12月"}],ce=N({__name:"Form",props:["editId"],emits:["close","refresh"],setup(F,{emit:u}){const y=F,h=k(!0),v=k(!1),w=()=>{v.value=!0,z(y.editId).then(i=>{l.value=i}).finally(()=>v.value=!1)};y.editId&&w();const l=k({id:0,month:void 0,bg_img:"",filter:{server_min:1,server_max:99999}}),p=k([]),g=k(!1),D=k(),c=()=>{D.value.validate().then(()=>{g.value=!0;const i=new FormData;i.append("month",`${l.value.month}`),i.append("bg_img",l.value.bg_img),i.append("server_min",`${l.value.filter.server_min}`),i.append("server_max",`${l.value.filter.server_max}`),p.value.length&&i.append("file",p.value[0]),l.value.id&&i.append("id",`${l.value.id}`),H(i).then(()=>{u("close"),u("refresh")}).catch(()=>{}).finally(()=>{g.value=!1})}).catch(()=>{})},d=i=>(p.value=[...p.value||[],i],!1),x=async i=>p.value.length||l.value.id||l.value.file?Promise.resolve():Promise.reject("请上传奖品配置文件"),K=i=>{const o=l.value.filter.server_max||0,O=l.value.filter.server_min||0;l.value.filter.server_max=o<O?i:l.value.filter.server_max};return(i,o)=>{const O=n("a-select"),b=n("a-form-item"),M=n("SelectImg"),I=n("upload-outlined"),R=n("a-button"),s=n("a-upload"),C=n("ExclamationCircleFilled"),$=n("a-typography-text"),P=n("a-input-number"),m=n("a-flex"),B=n("a-form"),G=n("a-spin"),q=n("a-drawer");return f(),L(q,{open:a(h),"onUpdate:open":o[5]||(o[5]=_=>ae(h)?h.value=_:null),title:y.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:o[6]||(o[6]=_=>!_&&u("close"))},{default:t(()=>[e(G,{spinning:a(v)},{default:t(()=>[e(B,{model:a(l),name:"basic",ref_key:"formRef",ref:D,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:t(()=>[e(b,{label:"月份",name:"month",rules:[{required:!0,message:"请输入月份"}]},{default:t(()=>[e(O,{value:a(l).month,"onUpdate:value":o[0]||(o[0]=_=>a(l).month=_),options:a(E),placeholder:"请选择月份"},null,8,["value","options"])]),_:1}),e(b,{label:"背景图",name:"bg_img"},{default:t(()=>[e(M,{value:a(l).bg_img,"onUpdate:value":o[1]||(o[1]=_=>a(l).bg_img=_),"width-height":[750,208]},null,8,["value"])]),_:1}),e(b,{label:"奖品配置",rules:[{validator:x}]},{default:t(()=>[e(s,{"file-list":a(p),"before-upload":d,onRemove:o[2]||(o[2]=()=>p.value=[]),"max-count":1,accept:"'.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'"},{default:t(()=>[e(R,null,{default:t(()=>[e(I),r(" 点击上传 ")]),_:1})]),_:1},8,["file-list"]),e($,{type:"warning",style:{"font-size":"12px"}},{default:t(()=>[e(C),r(" 新增时必须上传奖励配置 ")]),_:1})]),_:1},8,["rules"]),e(b,{label:"服务器",required:""},{default:t(()=>[e(m,null,{default:t(()=>[e(b,{name:["filter","server_min"]},{default:t(()=>[e(P,{style:{width:"150px"},value:a(l).filter.server_min,"onUpdate:value":o[3]||(o[3]=_=>a(l).filter.server_min=_),min:0,onChange:K,placeholder:"最小服务器"},null,8,["value"])]),_:1}),e($,{style:{margin:"0 5px 24px","line-height":"32px"}},{default:t(()=>[r("~")]),_:1}),e(b,{name:["filter","server_max"]},{default:t(()=>[e(P,{style:{width:"150px"},value:a(l).filter.server_max,"onUpdate:value":o[4]||(o[4]=_=>a(l).filter.server_max=_),min:a(l).filter.server_min,placeholder:"最大服务器"},null,8,["value","min"])]),_:1})]),_:1})]),_:1}),e(b,{"wrapper-col":{offset:6,span:16}},{default:t(()=>[e(R,{type:"primary",onClick:c,loading:a(g)},{default:t(()=>[r("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),me=N({__name:"Index",setup(F){le.extend(ne);const u=j({editVisible:!1,editId:0}),y=oe(),h=k(),v=()=>h.value.requestTableData(!0),w=(c,d)=>{u.editVisible=c,u.editId=d||0},l=(c,d)=>V(`确定要删除${d?"选中的":"此条"}数据吗？`,W,{id:c}).then(()=>v()),p=c=>V("确定要复制此条数据并生成一份新数据吗？",X,{id:c}).then(()=>v()),g=k([]),D=(c,d,x)=>{g.value[x]=!0,d.status=1-c,re.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{g.value[x]=!1,Q({id:d.id,status:c}).finally(()=>{v()})},onCancel:()=>{g.value[x]=!1}})};return(c,d)=>{const x=n("PlusOutlined"),K=n("a-button"),i=n("CloudDownloadOutlined"),o=n("a-space"),O=n("a-image"),b=n("a-switch"),M=n("a-divider"),I=n("a-typography-link"),R=n("CustomTable");return f(),U(S,null,[e(R,{ref_key:"RefCustomTable",ref:h,"data-api":a(Y),params:{},columns:a(ue)},{leftTool:t(()=>[e(o,null,{default:t(()=>[e(K,{type:"primary",onClick:d[0]||(d[0]=s=>w(!0))},{icon:t(()=>[e(x)]),default:t(()=>[r(" 新增 ")]),_:1}),e(K,{type:"primary",onClick:d[1]||(d[1]=s=>a(J)("checkin"))},{icon:t(()=>[e(i)]),default:t(()=>[r(" 下载奖品模板 ")]),_:1})]),_:1})]),bodyCell:t(({record:s,column:C,index:$})=>{var P;return[C.key==="bg_img"?(f(),U(S,{key:0},[s.bg_img?(f(),L(O,{key:0,src:s.bg_img,height:60},null,8,["src"])):(f(),U(S,{key:1},[r("-")],64))],64)):T("",!0),C.key==="filter"?(f(),U(S,{key:1},[r(A(s.filter.server_min)+"-"+A(s.filter.server_max),1)],64)):T("",!0),C.key==="month"?(f(),U(S,{key:2},[r(A((P=a(E).filter(m=>s.month===m.value)[0])==null?void 0:P.label),1)],64)):T("",!0),C.key==="status"?(f(),L(b,{key:3,checked:s.status,"onUpdate:checked":m=>s.status=m,checkedValue:10,unCheckedValue:1,"checked-children":"开启","un-checked-children":"关闭",loading:a(g)[$],onClick:m=>D(m,s,$)},null,8,["checked","onUpdate:checked","loading","onClick"])):T("",!0),C.key==="action"?(f(),L(o,{key:4},{split:t(()=>[e(M,{type:"vertical",style:{margin:"0"}})]),default:t(()=>[e(I,{type:"success",onClick:m=>p(s.id)},{default:t(()=>[r("复制")]),_:2},1032,["onClick"]),e(I,{onClick:m=>w(!0,s.id)},{default:t(()=>[r("编辑")]),_:2},1032,["onClick"]),e(I,{onClick:m=>a(y).push(`/dailyCheck/detail/${s.id}`)},{default:t(()=>[r("详情")]),_:2},1032,["onClick"]),e(I,{type:"danger",danger:"",onClick:m=>l(s.id,!1)},{default:t(()=>[r("删除")]),_:2},1032,["onClick"])]),_:2},1024)):T("",!0)]}),_:1},8,["data-api","columns"]),a(u).editVisible?(f(),L(ce,{key:0,"edit-id":a(u).editId,onClose:d[2]||(d[2]=s=>w(!1)),onRefresh:v},null,8,["edit-id"])):T("",!0)],64)}}}),ye=Object.freeze(Object.defineProperty({__proto__:null,default:me},Symbol.toStringTag,{value:"Module"})),_e=N({__name:"Detail",setup(F){const u=ie(),y=j({id:u.params.id&&Number(u.params.id)||0}),h=k(),v=()=>h.value.requestTableData(!0);return(w,l)=>{const p=n("UploadBtn"),g=n("CustomTable"),D=se("has");return f(),L(g,{ref_key:"RefCustomTable",ref:h,"data-api":a(te),params:{id:a(y).id},columns:a(pe),pagination:!1},{rightTool:t(()=>[de(e(p,{ref:"uploadBtn",onUploadSuccess:v,downloadApi:a(Z),uploadData:{id:a(y).id},downloadData:{id:a(y).id},fileType:"checkin",page:a(ee).DAILY_CHECK},null,8,["downloadApi","uploadData","downloadData","page"]),[[D,"Operation"]])]),_:1},8,["data-api","params","columns"])}}}),he=Object.freeze(Object.defineProperty({__proto__:null,default:_e},Symbol.toStringTag,{value:"Module"}));export{he as D,ye as I};
