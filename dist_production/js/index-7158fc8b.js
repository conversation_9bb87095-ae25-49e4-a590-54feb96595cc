import{d as D,m as J,bz as E,K as H,a as s,u as g,o as u,c as h,w as l,b as p,l as M,_ as P,h as q,y as W,a9 as ge,v as y,q as _e,k as b,F as z,s as G,t as I,bF as Oe,r as K,bC as he,e as O,z as ve,A as ye,j as $e,i as Ie,bE as oe,a0 as xe,f as R,g as be,bG as De,bH as Fe,bI as Le,bJ as Te,bK as ze,bL as je,bd as Me,bM as Pe,ak as Ue,ad as Re,ap as Ee,bN as He,bO as Be,bP as Ae,bQ as Ke,bR as Ve,bp as Ge,ai as qe,bq as Ne,bS as Ye,bm as We,ar as Qe,am as Je,aq as Xe,bT as Ze,aU as et,bU as tt,bV as ot,bW as nt,bg as at,bX as lt,bY as st,bZ as it,b_ as rt,b$ as ct,c0 as ut,c1 as dt,ae as pt,c2 as _t,c3 as mt,c4 as ft,c5 as gt,c6 as ht,c7 as vt,aG as yt,c8 as bt,c9 as wt,aJ as St,ca as kt,cb as Ct,ah as Ot,cc as $t,cd as It,ce as xt,cf as Dt,cg as Ft,ch as Lt,ci as Tt,cj as zt,ck as jt,cl as Mt,cm as Y,aN as Pt,x as Ut,cn as Rt,co as N,cp as Et,a5 as re,cq as Ht,cr as Bt,M as At,O as Kt,cs as Vt,p as ce,a8 as ue,ab as Q,bl as Gt,ct as qt,Z as Nt}from"./vendor-70efaa3a.js";import{m as Z,z as Yt,A as Wt,i as Qt,a as Jt,B as Xt,b as Zt,c as eo,C as to,d as oo,e as no,f as ao,g as lo,h as so,D as io,j as ro,k as co,l as uo,n as po,o as _o,E as mo,p as fo,F as de,I as go,q as ho,r as vo,s as yo,t as bo,M as wo,P as So,u as ko,R as Co,G as Oo,v as $o,w as Io,x as xo,V as Do,S as Fo,y as Lo,H as To,J as zo,K as jo,T as Mo,L as Po,N as Uo,O as Ro,Q as Eo,U as Ho,W as Bo,X as Ao,Y as Ko}from"./antd-016b48d6.js";import{ai as ee,aj as V,ak as ne,al as Vo,am as we,an as Go,z as pe,ao as qo,ap as No,d as Yo,aq as Wo,ar as Qo,as as Jo,at as Xo,au as Zo,B as Se,av as en,aw as tn,ax as on,ay as nn,az as an}from"./common-72b741fb.js";const ln={components:{Button:{paddingContentHorizontal:10},Table:{colorBorderSecondary:"#e8e8e8",colorFillAlter:"#f7f7f7"},Menu:{colorItemBgHover:"rgba(255, 255, 255, .1)"},Breadcrumb:{colorBgTextHover:"transparent"}},token:{fontFamily:'"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif'}},sn="/png/fp-logo-1690b683.png",rn=D({__name:"Help",setup(e){const t=J(),o=()=>{window.open(_.value)},{userState:n}=E(ee()),_=H(()=>{if(t.meta.help){if(typeof t.meta.help=="string")return t.meta.help;if(n.value.crt_game&&t.meta.help[n.value.crt_game.game_project])return t.meta.help[n.value.crt_game.game_project]}else return"";return""});return(d,i)=>{const r=s("QuestionCircleOutlined"),a=s("a-button"),c=s("a-tooltip");return g(_)?(u(),h(c,{key:0,title:"帮助"},{default:l(()=>[p(a,{type:"text",onClick:o,class:"tools-item"},{icon:l(()=>[p(r)]),_:1})]),_:1})):M("",!0)}}});const cn=P(rn,[["__scopeId","data-v-ff9a49c8"]]),un=["onClick"],dn={key:0,class:"no-redirect"},pn=["onClick"],_n=D({__name:"Index",setup(e){const t=J(),o=i=>{const{params:r}=t;return Oe(i)(r)},n=q({breadcrumbs:[],getBreadcrumb:()=>{const i=t.matched.filter(r=>r.meta&&r.meta.title);n.breadcrumbs=i.filter(r=>r.meta&&r.meta.title&&r.meta.breadcrumb!==!1)},isDashboard(i){const r=i&&i.name;return r?r.toString().trim().toLocaleLowerCase()==="Dashboard".toLocaleLowerCase():!1},handleLink(i){const{redirect:r,path:a}=i;if(r){V.push(r).catch(c=>{console.warn(c)});return}V.push(o(a)).catch(c=>{console.warn(c)})}});W(()=>t.path,i=>{i.startsWith("/redirect/")||n.getBreadcrumb()});const{permissionState:_}=E(ne()),d=()=>{V.push(_.value.routes.filter(i=>!i.meta||!i.meta.hidden)[0].path)};return ge(()=>{n.getBreadcrumb()}),(i,r)=>{const a=s("home-outlined"),c=s("a-breadcrumb-item"),m=s("a-breadcrumb");return u(),h(m,{class:"app-breadcrumb"},{default:l(()=>[p(c,null,{default:l(()=>[y("a",{onClick:_e(d,["prevent"])},[p(a)],8,un)]),_:1}),(u(!0),b(z,null,G(g(n).breadcrumbs,(f,C)=>(u(),h(c,{key:f.path},{default:l(()=>[f.redirect==="noredirect"||C===g(n).breadcrumbs.length-1?(u(),b("span",dn,I(f.meta.title),1)):(u(),b("a",{key:1,onClick:_e(w=>g(n).handleLink(f),["prevent"])},I(f.meta.title),9,pn))]),_:2},1024))),128))]),_:1})}}});const mn=P(_n,[["__scopeId","data-v-68c3f351"]]),fn=["innerHTML"],gn={class:"pagination"},hn=D({__name:"LogDrawer",setup(e){const t=q({visible:!1,containerH:window.innerHeight-206,tableData:[],loading:!1,page:1,pagesize:20,total:0,showRoute:["/tool/propQuery"]}),o=[{title:"日期",dataIndex:"created_at",key:"created_at",width:"160px"},{title:"操作人员",dataIndex:"user_name",key:"user_name",width:"100px"},{title:"事件",dataIndex:"op_content",key:"op_content",width:"100px"},{title:"内容",dataIndex:"op_remark",key:"op_remark",ellipsis:!0}],n=J(),_=H(()=>{var c;return`用户操作日志 - ${(c=n.meta)==null?void 0:c.title}`});W(()=>t.visible,a=>{a?d():i()});const d=async()=>{const a={page:t.page,page_size:t.pagesize};t.loading=!0;try{const c=await Vo(a);t.tableData=c.data,t.total=c.total}catch(c){console.log("日志数据获取异常",c)}t.loading=!1},i=()=>{t.tableData=[],t.page=1,t.total=0},r=a=>{t.page=a,d()};return(a,c)=>{const m=s("FileTextOutlined"),f=s("a-button"),C=s("a-tooltip"),w=s("a-table"),$=s("a-pagination"),L=s("a-drawer");return u(),b(z,null,[g(t).showRoute.includes(g(n).path)?M("",!0):(u(),h(C,{key:0,title:"日志"},{default:l(()=>[p(f,{type:"text",onClick:c[0]||(c[0]=T=>g(t).visible=!0),class:"tools-item"},{icon:l(()=>[p(m)]),_:1})]),_:1})),p(L,{open:g(t).visible,"onUpdate:open":c[3]||(c[3]=T=>g(t).visible=T),title:g(_),placement:"right",width:"700px"},{default:l(()=>[p(w,{"data-source":g(t).tableData,columns:o,size:"small",pagination:!1,loading:g(t).loading,scroll:{y:g(t).containerH}},{bodyCell:l(({column:T,text:S})=>[T.dataIndex==="op_remark"?(u(),b("div",{key:0,class:"log-content",innerHTML:S,style:{"padding-right":"4px","white-space":"break-spaces"}},null,8,fn)):M("",!0)]),_:1},8,["data-source","loading","scroll"]),y("div",gn,[p($,{size:"small",current:g(t).page,"onUpdate:current":c[1]||(c[1]=T=>g(t).page=T),"page-size":g(t).pagesize,"onUpdate:pageSize":c[2]||(c[2]=T=>g(t).pagesize=T),total:g(t).total,onChange:r,layout:"total, prev, pager, next, sizes"},null,8,["current","page-size","total"])])]),_:1},8,["open","title"])],64)}}});const vn=P(hn,[["__scopeId","data-v-17dc3b45"]]);function yn(){const e=K(Math.abs(window.screen.height-window.document.documentElement.clientHeight)<=17),t=()=>{if(e.value)document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():Z.error({content:"请升级浏览器!",duration:3}),e.value=!1;else{var o=document.documentElement;o.requestFullscreen?o.requestFullscreen():o.mozRequestFullScreen?o.mozRequestFullScreen():o.webkitRequestFullScreen?o.webkitRequestFullScreen():o.msRequestFullscreen?o.msRequestFullscreen():Z.error({content:"请升级浏览器!",duration:3}),e.value=!0}};return document.addEventListener("fullscreenchange",function(){document.fullscreenElement?(console.log("进入全屏模式"),e.value=!0):(console.log("退出全屏模式"),e.value=!1)}),window.onresize=function(){e.value=Math.abs(window.screen.height-window.document.documentElement.clientHeight)<=17},window.addEventListener("keydown",function(o){o=o||window.event,(o.keyCode===122||o.code==="F11")&&!e.value&&(o.preventDefault(),t())}),[e,t]}const bn=D({__name:"GameList",setup(e){const t=ee(),{userState:o}=E(t),n=_=>{const d=localStorage.getItem("crtGame");console.log("item === ",_,d),_.game_project!==d&&(localStorage.setItem("crtGame",_.game_project),window.location.reload())};return(_,d)=>{var w;const i=s("a-avatar"),r=s("CaretDownFilled"),a=s("a-button"),c=s("a-space"),m=s("a-menu-item"),f=s("a-menu"),C=s("a-dropdown");return(w=g(o).crt_game)!=null&&w.game_name?(u(),h(C,{key:0,placement:"bottom",arrow:"",class:"tools-item"},{overlay:l(()=>[p(f,{class:"game-list"},{default:l(()=>[(u(!0),b(z,null,G(g(o).game_infos,($,L)=>(u(),h(m,{key:L,onClick:T=>n($),class:he({"game-item":!0,active:g(o).crt_game.game_project===$.game_project})},{default:l(()=>[p(c,null,{default:l(()=>[p(i,{src:$.game_icon_url,size:"small"},null,8,["src"]),O(" "+I($.game_name),1)]),_:2},1024)]),_:2},1032,["onClick","class"]))),128))]),_:1})]),default:l(()=>[p(a,{type:"text"},{default:l(()=>[p(i,{src:g(o).crt_game.game_icon_url,size:"small",style:{"margin-right":"5px"}},null,8,["src"]),O(" "+I(g(o).crt_game.game_name)+" ",1),p(r,{style:{"font-size":"12px","margin-left":"5px"}})]),_:1})]),_:1})):M("",!0)}}});const wn=P(bn,[["__scopeId","data-v-6e892279"]]),ke=e=>(ve("data-v-7db46132"),e=e(),ye(),e),Sn=ke(()=>y("img",{src:sn,alt:"",height:"34"},null,-1)),kn=[Sn],Cn={class:"tools-item"},On=ke(()=>y("div",{class:"menu-list"},null,-1)),$n=D({__name:"header",setup(e){const t=we(),{setAsideStatus:o}=t,{systemState:n}=E(t),[_,d]=yn(),i=J(),r=()=>{V.replace(`/redirect${i.fullPath}`)},a=ee(),{logout:c}=a,{userState:m}=E(a),{permissionState:f}=E(ne()),C=()=>{V.push(f.value.dynamicRoutes[0].path)};return(w,$)=>{const L=s("MenuUnfoldOutlined"),T=s("MenuFoldOutlined"),S=s("a-button"),B=s("ReloadOutlined"),k=s("a-tag"),v=s("a-space"),x=s("CompressOutlined"),F=s("ExpandOutlined"),U=s("a-tooltip"),A=s("CaretDownFilled"),le=s("PoweroffOutlined"),j=s("a-menu-item"),X=s("a-menu"),se=s("a-dropdown"),ie=s("a-layout-header");return u(),h(ie,{class:"header"},{default:l(()=>[y("div",{class:"logo",onClick:C},kn),p(v,{size:1},{default:l(()=>[p(S,{class:"tools-item",type:"text",onClick:g(o)},{icon:l(()=>[g(n).asideStatus?(u(),h(L,{key:0})):(u(),h(T,{key:1}))]),_:1},8,["onClick"]),p(S,{class:"tools-item",type:"text",onClick:r},{icon:l(()=>[p(B)]),_:1}),y("div",Cn,[p(k,{color:"#ff4900",style:{"font-weight":"500",margin:"0"}},{default:l(()=>[O(I(g(n).env),1)]),_:1})]),p(mn)]),_:1}),On,p(v,{size:1},{default:l(()=>[p(cn),p(vn),p(U,null,{title:l(()=>[O(I(g(_)?"退出全屏":"全屏"),1)]),default:l(()=>[p(S,{class:"tools-item",type:"text",onClick:g(d)},{icon:l(()=>[g(_)?(u(),h(x,{key:0})):(u(),h(F,{key:1}))]),_:1},8,["onClick"])]),_:1}),p(wn),p(se,{placement:"bottom",arrow:"",class:"tools-item"},{overlay:l(()=>[p(X,null,{default:l(()=>[p(j,null,{default:l(()=>[p(v,null,{default:l(()=>[p(le),y("a",{href:"javascript:;",onClick:$[0]||($[0]=(...Ce)=>g(c)&&g(c)(...Ce))},"退出登录")]),_:1})]),_:1})]),_:1})]),default:l(()=>[p(S,{type:"text"},{default:l(()=>[O(I(g(m).userInfo.username)+" ",1),p(A,{style:{"font-size":"12px","margin-left":"5px"}})]),_:1})]),_:1})]),_:1})]),_:1})}}});const In=P($n,[["__scopeId","data-v-7db46132"]]),xn={class:"layout-sider-content"},Dn={class:"menu-scroller"},Fn=D({__name:"asider",setup(e){const{systemState:t}=E(we()),{permissionState:o}=E(ne()),n=f=>f.meta&&f.meta.hidden,_=q({openKeys:[],preOpenKeys:[],filterRoutes:[]}),d=J(),i=$e(),r=K([]),a=()=>{d.matched.length>0&&(!t.value.asideStatus&&(_.openKeys=[d.matched[0].path]),r.value=[],d.meta.activeMenu?r.value.push(d.meta.activeMenu||d.path):(d.matched.forEach(f=>r.value.push(f.path)),r.value.push(d.path)))};W(()=>_.openKeys,(f,C)=>{_.preOpenKeys=C});const c=f=>{if(localStorage.getItem("crtGame")==="funplus_zone"){const w=$=>$.filter(L=>{var T;return L.children&&(L.children=w(L.children)),!((T=L.meta)!=null&&T.not_funplus_zone)});return w(f)}else return f},m=H(()=>c(o.value.routes));return W(d,f=>a()),a(),(f,C)=>{const w=s("a-menu-item"),$=s("a-sub-menu"),L=s("a-menu"),T=s("a-layout-sider");return u(),h(T,{collapsed:g(t).asideStatus,"onUpdate:collapsed":C[1]||(C[1]=S=>g(t).asideStatus=S),trigger:null,collapsible:"","collapsed-width":"50"},{default:l(()=>[y("div",xn,[y("div",Dn,[p(L,{selectedKeys:g(r),"onUpdate:selectedKeys":C[0]||(C[0]=S=>Ie(r)?r.value=S:null),openKeys:g(_).openKeys,class:"header-menu",mode:"inline",theme:"dark"},{default:l(()=>[(u(!0),b(z,null,G(g(m),S=>{var B;return u(),b(z,{key:S.path},[n(S)?M("",!0):(u(),b(z,{key:0},[S.children&&S.children.filter(k=>{var v;return!((v=k.meta)!=null&&v.hidden)}).length>1?(u(),h($,{key:S.path,title:(B=S.meta)==null?void 0:B.title},{icon:l(()=>{var k;return[(k=S.meta)!=null&&k.icon?(u(),h(oe(S.meta.icon),{key:0})):M("",!0)]}),default:l(()=>[(u(!0),b(z,null,G(S.children.filter(k=>{var v;return!((v=k.meta)!=null&&v.hidden)}),k=>(u(),h(w,{key:k.path,onClick:v=>g(i).push(k.path)},{default:l(()=>{var v,x;return[(v=k.meta)!=null&&v.icon?(u(),h(oe(k.meta.icon),{key:0})):M("",!0),O(" "+I((x=k.meta)==null?void 0:x.title),1)]}),_:2},1032,["onClick"]))),128))]),_:2},1032,["title"])):(u(),b(z,{key:1},[n(S)?M("",!0):(u(),h(w,{key:S.path,onClick:k=>g(i).push(S.path)},{default:l(()=>{var k,v;return[(k=S.meta)!=null&&k.icon?(u(),h(oe(S.meta.icon),{key:0})):M("",!0),y("span",null,I((v=S.meta)==null?void 0:v.title),1)]}),_:2},1032,["onClick"]))],64))],64))],64)}),128))]),_:1},8,["selectedKeys","openKeys"])])])]),_:1},8,["collapsed"])}}});const Ln=P(Fn,[["__scopeId","data-v-792e2c95"]]),Tn={class:"home-wrap"},zn=D({__name:"Index",setup(e){return(t,o)=>{const n=s("a-layout-content"),_=s("router-view"),d=s("a-layout");return u(),b("div",Tn,[p(d,null,{default:l(()=>[p(In),p(d,{class:"content-wrap"},{default:l(()=>[p(Ln),p(_,null,{default:l(({Component:i,route:r})=>[p(xe,{name:"fade-transform",mode:"out-in"},{default:l(()=>[(u(),h(n,{class:"page-content",key:r.fullPath},{default:l(()=>[(u(),b("div",{class:"main-wrap",key:r.fullPath},[(u(),h(oe(i),{key:r.fullPath}))]))]),_:2},1024))]),_:2},1024)]),_:1})]),_:1})]),_:1})])}}});const jn=P(zn,[["__scopeId","data-v-9b01a85c"]]),Mn=D({__name:"App",setup(e){R.extend(be).locale("zh-cn");const t=["/login","/","/403"],o=J(),n=ee(),{userState:_}=E(n);return(d,i)=>{const r=s("router-view"),a=s("a-watermark"),c=s("a-config-provider");return u(),h(c,{theme:g(ln),locale:g(Yt)},{default:l(()=>[p(a,De({content:g(_).userInfo.email},{font:{color:"rgba(0,0,0,0.03)"},gap:[50,50]}),{default:l(()=>[t.indexOf(g(o).path)>-1?(u(),h(r,{key:0})):(u(),h(jn,{key:1})),p(Go)]),_:1},16,["content"])]),_:1},8,["theme","locale"])}}}),Pn=Object.freeze(Object.defineProperty({__proto__:null,Affix:Wt,Alert:Qt,Avatar:Jt,Badge:Xt,Breadcrumb:Zt,Button:eo,Card:to,Checkbox:oo,CheckboxGroup:no,Col:ao,Collapse:lo,ConfigProvider:so,DatePicker:io,Descriptions:ro,DescriptionsItem:co,Divider:uo,Drawer:po,Dropdown:_o,Empty:mo,Flex:fo,Form:de,Image:go,Input:ho,InputNumber:vo,Layout:yo,Menu:bo,Modal:wo,Pagination:So,Popover:ko,Radio:Co,RadioGroup:Oo,RangePicker:$o,Result:Io,Row:xo,Select:Do,SelectOptGroup:Fo,SelectOption:Lo,Space:To,Spin:zo,Switch:jo,TabPane:Mo,Table:Po,Tabs:Uo,Tag:Ro,Tooltip:Eo,Tree:Ho,Typography:Bo,Upload:Ao,Watermark:Ko},Symbol.toStringTag,{value:"Module"})),Un=Object.freeze(Object.defineProperty({__proto__:null,AccountBookOutlined:Fe,ApiOutlined:Le,AppstoreOutlined:Te,ArrowDownOutlined:ze,ArrowUpOutlined:je,CaretDownFilled:Me,CaretRightOutlined:Pe,CheckCircleFilled:Ue,CloseCircleFilled:Re,CloseCircleOutlined:Ee,CloudDownloadOutlined:He,CloudUploadOutlined:Be,ClusterOutlined:Ae,CompressOutlined:Ke,DatabaseOutlined:Ve,DeleteOutlined:Ge,DownOutlined:qe,DownloadOutlined:Ne,DropboxOutlined:Ye,EditOutlined:We,EllipsisOutlined:Qe,ExclamationCircleFilled:Je,ExclamationCircleOutlined:Xe,ExpandOutlined:Ze,EyeOutlined:et,FileTextOutlined:tt,FireOutlined:ot,FlagOutlined:nt,FolderOpenOutlined:at,ForkOutlined:lt,FormOutlined:st,GiftOutlined:it,GoldOutlined:rt,GroupOutlined:ct,HomeOutlined:ut,InboxOutlined:dt,LoadingOutlined:pt,LogoutOutlined:_t,MenuFoldOutlined:mt,MenuUnfoldOutlined:ft,MessageOutlined:gt,NodeIndexOutlined:ht,PayCircleOutlined:vt,PlusOutlined:yt,PoweroffOutlined:bt,ProjectOutlined:wt,QuestionCircleOutlined:St,ReloadOutlined:kt,RightSquareOutlined:Ct,SearchOutlined:Ot,SettingOutlined:$t,SlidersOutlined:It,SolutionOutlined:xt,TagsOutlined:Dt,TeamOutlined:Ft,ToolOutlined:Lt,TrophyOutlined:Tt,UploadOutlined:zt,UsergroupAddOutlined:jt,WarningOutlined:Mt},Symbol.toStringTag,{value:"Module"})),me=Un,fe=Pn;function Rn(e){for(const t in fe){const o=fe[t];o.install&&e.use(o)}for(const t in me)e.component(t,me[t])}const En=Object.freeze(Object.defineProperty({__proto__:null,default:Rn},Symbol.toStringTag,{value:"Module"}));function Hn(e){const t=Object.assign({"./antd/index.ts":En});for(const o in t){const n=t[o].default;typeof n=="function"&&n(e)}}Y.configure({showSpinner:!1});V.beforeEach(async(e,t,o)=>{if(e.query.game_project&&localStorage.setItem("crtGame",e.query.game_project),e.path==="/"&&!localStorage.getItem("ticket")){o({path:"login",replace:!0});return}if(e.name==="Login"){if(e.query.ticket)localStorage.setItem("ticket",e.query.ticket);else if(!localStorage.getItem("ticket"))return o()}if(e.name==="Error"&&t.name==="Error")return o();const n=ee(),{setTicket:_,FETCH_PERMISSION:d,FETCH_GAME_PERMISSION:i}=n,{userState:r}=E(n),a=ne(),{permissionState:c}=E(a),{SET_ROUTES:m,setBtnPromise:f}=a,{FETCH_GLOBAL_CONFIG:C}=pe();if(Y.start(),localStorage.getItem("ticket")&&_(localStorage.getItem("ticket")),r.value.isLogin&&e.path!=="/")return Y.done(),o();if(qo.indexOf(e.path)>-1&&!localStorage.getItem("ticket"))return Y.done(),o();try{await i(),await d();const w=r.value.userInfo.permission_list;m(w),f(w),c.value.routes.forEach($=>{V.addRoute($)}),Y.done(),c.value.dynamicRoutes.length===0?o({path:"/403",replace:!0}):(C(),e.path==="/login"||e.path==="/"?o({path:c.value.dynamicRoutes[0].path}):o({...e,replace:!0}))}catch(w){w==="no game infos"?o({path:"/403"}):(localStorage.removeItem("ticket"),o({path:"/login"})),Y.done()}});V.afterEach(e=>{Y.done();const t=e.matched.filter(o=>{var n,_;return((n=o.meta)==null?void 0:n.breadcrumb)!==!1&&((_=o.meta)==null?void 0:_.title)}).map(o=>{var n;return(n=o.meta)==null?void 0:n.title}).reverse();document.title=(t.length?t.join(" - ")+" - ":"")+"私域管理平台"});const Bn={class:"box-card-content"},An={key:0,class:"custom-table-top"},Kn={class:"tools",ref:"tools"},Vn={class:"left"},Gn={class:"right"},qn={key:0,style:{"font-weight":"400",color:"#777","font-size":"13px"}},Nn={key:1,class:"custom-table-footer"},Yn={name:"CustomTable"},Wn=D({...Yn,props:{height:{default:"auto"},bottomPadding:{default:0},dataApi:{type:Function,default:()=>Promise.resolve()},params:{default:()=>({})},tableModel:{default:"FillLeaveHeight"},rowKey:{},selection:{type:Boolean},columns:{default:null},noCard:{type:Boolean,default:!1},pagination:{type:Boolean,default:!0}},emits:["changeInfo","selection-change"],setup(e,{expose:t,emit:o}){const n=e,_=K(),d=K([]),i=H(()=>d.value.length>0?[...d.value,...n.columns||[]]:n.columns),r=K(!1),a=q({pageIndex:1,pageSize:15,total:1,list:[],sort_field:"",sort_type:"",selectedRowKeys:[]});function c(v=!1){if(console.log("requestTableData resetPage",v),!n.dataApi)return console.error("params dataApi err！");v&&(a.pageIndex=1);const x={...n.params,sort_field:a.sort_field,sort_type:a.sort_type};n.pagination&&(x.page=a.pageIndex,x.page_size=a.pageSize),r.value=!0,n.dataApi(x).then(async F=>{!d.value.length&&F.fields&&(d.value=[]),m(),a.list=F.data.map((U,A)=>F.data.length===1?{...U,first:!0,last:!0}:A===0?{...U,first:!0}:A===F.data.length-1?{...U,last:!0}:U),a.pageIndex=Number(F.current_page),a.total=F.total,a.pageSize=F.per_page,await re(),o("changeInfo",F)}).catch(F=>{console.error(F)}).finally(()=>{r.value=!1})}const m=()=>{a.selectedRowKeys=[],o("selection-change",[])},f=v=>{a.selectedRowKeys.push(v.id),o("selection-change",a.list.filter(x=>a.selectedRowKeys.includes(x.id)))},C=v=>{a.pageIndex=v,c(),m()},w=K(200),$=K("customTable"),L=Pt(async()=>{var v,x,F,U,A;await re(),await re(),w.value=((v=_.value)==null?void 0:v.clientHeight)||0,w.value-=((F=(x=_.value)==null?void 0:x.querySelector(".ant-table-header"))==null?void 0:F.clientHeight)||40,k(["leftTool","rightTool"])&&((U=_.value)!=null&&U.querySelector(".ant-table-title"))&&(w.value-=((A=_.value)==null?void 0:A.querySelector(".ant-table-title")).offsetHeight)},300),T=(v,x,F)=>{var U;a.sort_field=F.columnKey||F.field,a.sort_type=(U=F.order)==null?void 0:U.replace("end",""),a.pageIndex=1,c()},S=async()=>{n.tableModel==="FillLeaveHeight"&&(L(),window.removeEventListener("resize",L),window.addEventListener("resize",L))};Ut(()=>{c(),S()}),t({requestTableData:c,renderHeight:S,customTable:$,clearSelection:m,toggleRowSelection:f});const B=Rt(),k=v=>{if(typeof v=="string")return!!B[v];for(let x=0;x<v.length;x++)if(B[v[x]])return!0;return!1};return(v,x)=>{const F=s("a-tooltip"),U=s("a-table"),A=s("a-pagination"),le=s("a-card");return u(),h(le,{class:he(["box-card mw-child-h-auto",{"no-card":v.noCard}]),border:!1},{default:l(()=>[y("div",Bn,[k("top")?(u(),b("div",An,[N(v.$slots,"top",{},void 0,!0)])):M("",!0),y("div",{class:"custom-table-wrap",id:"custom-table-wrap",ref_key:"customTabelWrap",ref:_},[p(U,{loading:r.value,id:"table",ref_key:"customTable",ref:$,"data-source":a.list,"row-selection":v.selection?{fixed:!0,selectedRowKeys:a.selectedRowKeys,onChange:(j,X)=>{a.selectedRowKeys=j,o("selection-change",X)}}:null,pagination:!1,scroll:{x:"100%",y:`${w.value}px`},rowKey:v.rowKey||"id",onChange:T,columns:g(i),size:"small"},Et({bodyCell:l(({text:j,record:X,index:se,column:ie})=>[N(v.$slots,"bodyCell",{column:ie,record:X,index:se,text:j},void 0,!0)]),headerCell:l(({column:j})=>[N(v.$slots,"headerCell",{column:j},()=>[j.desc?(u(),h(F,{key:0,placement:"top",class:"th-over-hidden"},{title:l(()=>[y("span",null,I(j.desc),1)]),default:l(()=>[y("span",null,I(j.title),1)]),_:2},1024)):(u(),b(z,{key:1},[j.child?(u(),b("span",qn,I(j.title),1)):(u(),b(z,{key:1},[O(I(j.title),1)],64))],64))],!0)]),default:l(()=>[N(v.$slots,"default",{},void 0,!0)]),_:2},[k(["leftTool","rightTool"])?{name:"title",fn:l(()=>[y("div",Kn,[y("div",Vn,[N(v.$slots,"leftTool",{},void 0,!0)]),y("div",Gn,[N(v.$slots,"rightTool",{},void 0,!0)])],512)]),key:"0"}:void 0]),1032,["loading","data-source","row-selection","scroll","rowKey","columns"])],512),n.pagination?(u(),b("div",Nn,[p(A,{size:"small",current:a.pageIndex,"onUpdate:current":x[0]||(x[0]=j=>a.pageIndex=j),"page-size":a.pageSize,"onUpdate:pageSize":x[1]||(x[1]=j=>a.pageSize=j),total:a.total,"page-size-options":["15","30","50","100"],onChange:C,"show-quick-jumper":"","show-size-changer":"",layout:"total, prev, pager, next, sizes"},null,8,["current","page-size","total"])])):M("",!0)])]),_:3},8,["class"])}}});const Qn=P(Wn,[["__scopeId","data-v-3668bf36"]]),Jn=Object.freeze(Object.defineProperty({__proto__:null,default:Qn},Symbol.toStringTag,{value:"Module"}));const Xn=D({name:"Editor",components:{WangEditor:Ht,Toolbar:Bt},props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},height:{type:Number,default:300}},emits:["update:value","change"],setup(e,{emit:t}){const o=At(),n=H({get:()=>e.value,set:a=>{t("update:value",a)}}),_={toolbarKeys:["headerSelect","fontSize","bold","underline","italic","through","clearStyle","color","bgColor","sup","sub","bulletedList","numberedList","todo"]},d={placeholder:"请输入内容..."};return W(()=>e.disabled,a=>{o.value&&(a?o.value.disable():o.value.enable())}),Kt(()=>{const a=o.value;a!=null&&a.destroy()}),{editorRef:o,valueHtml:n,mode:"default",toolbarConfig:_,editorConfig:d,handleCreated:a=>{o.value=a},onChange:a=>{a.getText()||t("update:value","")}}}});const Zn={class:"custom-editor"};function ea(e,t,o,n,_,d){const i=s("Toolbar"),r=s("WangEditor");return u(),b("div",Zn,[p(i,{style:{"border-bottom":"1px solid #f0f0f0"},editor:e.editorRef,defaultConfig:e.toolbarConfig,mode:e.mode},null,8,["editor","defaultConfig","mode"]),p(r,{style:Vt(`height: ${e.height}px; overflow-y: hidden;`),modelValue:e.valueHtml,"onUpdate:modelValue":t[0]||(t[0]=a=>e.valueHtml=a),defaultConfig:e.editorConfig,mode:e.mode,onOnCreated:t[1]||(t[1]=a=>e.editorRef=a),onOnChange:e.onChange},null,8,["style","modelValue","defaultConfig","mode","onOnChange"])])}const ta=P(Xn,[["render",ea],["__scopeId","data-v-2edbefed"]]),oa=Object.freeze(Object.defineProperty({__proto__:null,default:ta},Symbol.toStringTag,{value:"Module"})),na={class:"prop-icon"},aa=["src"],la=["src"],sa=["src"],ia={name:"PropIcon"},ra=D({...ia,props:{propDetail:{}},setup(e){const t=e;return(o,n)=>(u(),b("div",na,[ce(y("img",{src:t.propDetail.image_quality},null,8,aa),[[ue,t.propDetail.image_quality]]),ce(y("img",{src:t.propDetail.image},null,8,la),[[ue,t.propDetail.image]]),ce(y("img",{src:t.propDetail.image_chip},null,8,sa),[[ue,t.propDetail.image_chip]])]))}});const ca=P(ra,[["__scopeId","data-v-fd5e0ee4"]]),ua=Object.freeze(Object.defineProperty({__proto__:null,default:ca},Symbol.toStringTag,{value:"Module"})),te=e=>(ve("data-v-8c01742a"),e=e(),ye(),e),da={class:"content-box"},pa=te(()=>y("div",{class:"title"},"1.下载导入模板",-1)),_a=te(()=>y("div",{class:"desc"},"请先下载导入模板，按模板要求填写数据；为避免导入失败，填写过程请勿修改表头",-1)),ma={class:"btns-wrap"},fa={class:"content-box"},ga=te(()=>y("div",{class:"title"},"2.导入文件",-1)),ha={class:"desc"},va=te(()=>y("br",null,null,-1)),ya={class:"btns-wrap"},ba={class:"ant-upload-drag-icon"},wa=te(()=>y("p",{class:"ant-upload-text"},"点击或拖拽文件至此区域即可上传",-1)),Sa={name:"UploadBtn"},ka=D({...Sa,props:{uploadData:{default:()=>({})},page:{default:""},accept:{default:".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"},downloadApi:{type:Function,default:()=>{}},fileType:{default:""},downloadData:{default:()=>({})},hideUploadBtn:{type:Boolean,default:!1},hideDownloadBtn:{type:Boolean,default:!1}},emits:["uploadSuccess"],setup(e,{emit:t}){const o=e,n=q({visibleUpload:!1,uploadLoading:!1,fileList:[],file:null});function _(){n.visibleUpload=!1,n.uploadLoading=!1,n.fileList=[]}const d=c=>(n.fileList=[c],!1);function i(c){n.fileList=[c.file]}function r(){if(n.fileList.length===0)return Z.error("未选择文件！");n.uploadLoading=!0;const c=new FormData;c.append("file",n.fileList[0]);for(const m in o.uploadData)c.append(m,o.uploadData[m]);No(o.page,c).then(m=>{t("uploadSuccess",m),_()}).catch(()=>(n.uploadLoading=!1,Z.error("请按照模版上传")))}W(()=>n.visibleUpload,(c,m)=>{c===!1&&m===!0&&(n.uploadLoading=!1,n.fileList=[])});const a=c=>{c?Yo(o.fileType):o.downloadApi({...o.downloadData})};return(c,m)=>{const f=s("a-button"),C=s("CloudDownloadOutlined"),w=s("a-card"),$=s("inbox-outlined"),L=s("a-upload-dragger"),T=s("CloudUploadOutlined"),S=s("a-modal"),B=s("a-space");return u(),h(B,null,{default:l(()=>[o.hideUploadBtn?M("",!0):(u(),h(f,{key:0,onClick:m[0]||(m[0]=k=>n.visibleUpload=!0)},{default:l(()=>[O("批量更新")]),_:1})),o.hideDownloadBtn?M("",!0):(u(),h(f,{key:1,type:"primary",link:"",onClick:m[1]||(m[1]=k=>a(!1))},{default:l(()=>[O("导出配置")]),_:1})),p(S,{open:n.visibleUpload,"onUpdate:open":m[5]||(m[5]=k=>n.visibleUpload=k),title:"批量更新","mask-closable":!1},{footer:l(()=>[p(f,{link:"",onClick:_,disabled:n.uploadLoading},{default:l(()=>[O("取消")]),_:1},8,["disabled"]),p(f,{type:"primary",loading:n.uploadLoading,onClick:r},{icon:l(()=>[p(T)]),default:l(()=>[O(" 上传 ")]),_:1},8,["loading"])]),default:l(()=>[p(w,null,{default:l(()=>[y("div",da,[pa,_a,y("div",ma,[p(f,{type:"primary",onClick:m[2]||(m[2]=k=>a(!0))},{icon:l(()=>[p(C)]),default:l(()=>[O(" 下载导入模板 ")]),_:1})])])]),_:1}),p(w,null,{default:l(()=>[y("div",fa,[ga,y("div",ha,[N(c.$slots,"tip",{},()=>[O(" * 请按照模板格式准备需要导入的数据，更新后，已配置数据将被覆盖"),va,O(" * 文件小于2M，上传后即开始导入 ")],!0)]),y("div",ya,[p(L,{fileList:n.fileList,"onUpdate:fileList":m[3]||(m[3]=k=>n.fileList=k),name:"file",multiple:!1,action:"/",accept:o.accept,onChange:i,onRemove:m[4]||(m[4]=k=>n.fileList=[]),"before-upload":d},{default:l(()=>[y("p",ba,[p($)]),wa]),_:1},8,["fileList","accept"])])])]),_:3})]),_:3},8,["open"])]),_:3})}}});const Ca=P(ka,[["__scopeId","data-v-8c01742a"]]),Oa=Object.freeze(Object.defineProperty({__proto__:null,default:Ca},Symbol.toStringTag,{value:"Module"}));function $a(e){const t=Object.assign({"../packages/customTable/Index.vue":Jn,"../packages/editor/Index.vue":oa,"../packages/pictrue/Index.vue":Wo,"../packages/propIcon/Index.vue":ua,"../packages/uploadBtn/Index.vue":Oa});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const Ia={install:$a};R.extend(be);const xa=D({name:"SelectDateTime",props:{value:{type:Array,default:()=>[]},isDisabledDate:{type:Boolean,default:!1}},emits:["update:value","change"],setup(e,t){const o=H({get:()=>{if(e.value)return e.value.length===0?[]:e.value.map(d=>d&&R.utc(d*1e3).format("YYYY-MM-DD HH:mm:ss")||void 0)},set:d=>{let i;d!=null&&d.length&&(i=[R.utc(d[0]).valueOf()/1e3,R.utc(d[1]).valueOf()/1e3]),t.emit("update:value",i),t.emit("change",i)}}),n=d=>e.isDisabledDate?d&&d<R().startOf("day"):!1,_=()=>{const d=!!e.isDisabledDate,i=R().hour(),r=R().minute(),a=R().second(),c=e.isDisabledDate?[R().hour(i).minute(r).second(a),R().hour(i).minute(r).second(a)]:[R().hour(0).minute(0).second(0),R().hour(i).minute(r).second(a)];return{hideDisabledOptions:d,defaultValue:c}};return{...Q(e),modelValue:o,disabledDate:n,showTime:_,dayjs:R}}});function Da(e,t,o,n,_,d){const i=s("a-range-picker");return u(),h(i,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=r=>e.modelValue=r),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":e.disabledDate,"show-time":e.showTime()},null,8,["value","disabled-date","show-time"])}const Fa=P(xa,[["render",Da]]),La=Object.freeze(Object.defineProperty({__proto__:null,default:Fa},Symbol.toStringTag,{value:"Module"})),Ta=D({name:"SelectImgGallery",props:{value:{type:String,default:""},preview:{type:String,default:""}},emits:["update:value","change"],setup(e,{emit:t}){const o=de.useInjectFormItemContext(),n=(c,m={})=>{_.url=m.showUrl,t("update:value",c),t("change",m),o.onFieldChange()},_=q({url:"",isFirst:!0,previewVisible:!1}),d=()=>{Qo.$emit("showPhotoGallery",{callback:c=>{n(c.img_key,c)}})},i=()=>{if(!_.isFirst||!e.value)return;const c={search:e.value};Jo(c).then(m=>{n(m.img_key,m.preview_img||"")}).finally(()=>{_.isFirst=!1})};ge(()=>{i()});const r=c=>{c===!1&&(_.previewVisible=!1)},a=()=>{Z.error("选择的图片已不存在，请重新选择！"),n("")};return{...Q(_),chooseImg:d,onVisibleChange:r,imgLoadError:a,triggerChange:n}}});const za={class:"custom-select-img"};function ja(e,t,o,n,_,d){const i=s("EyeOutlined"),r=s("DeleteOutlined"),a=s("a-space"),c=s("a-image"),m=s("PlusOutlined");return u(),b("div",za,[e.url||e.preview?(u(),h(c,{key:0,src:e.url||e.preview,style:{width:"auto","max-width":"100%","max-height":"100%"},preview:{visible:e.previewVisible,onVisibleChange:e.onVisibleChange},onError:e.imgLoadError},{previewMask:l(()=>[p(a,{align:"center"},{default:l(()=>[p(i,{onClick:t[0]||(t[0]=f=>e.previewVisible=!0)}),p(r,{onClick:t[1]||(t[1]=f=>e.triggerChange(""))})]),_:1})]),_:1},8,["src","preview","onError"])):(u(),b("div",{key:1,class:"img-plus",onClick:t[2]||(t[2]=(...f)=>e.chooseImg&&e.chooseImg(...f))},[p(m)]))])}const Ma=P(Ta,[["render",ja],["__scopeId","data-v-0be816df"]]),Pa=Object.freeze(Object.defineProperty({__proto__:null,default:Ma},Symbol.toStringTag,{value:"Module"})),Ua=D({name:"SelectImg",props:{value:{type:String,default:""},widthHeight:{type:Array,default:()=>[]},tips:{type:String,default:""}},emits:["update:value","change"],setup(e,{emit:t}){const o=de.useInjectFormItemContext(),n=f=>{t("update:value",f),t("change",f),o.onFieldChange()},_=q({loading:!1,previewVisible:!1}),d=H({get:()=>e.value,set:f=>{n(f)}}),i=H(()=>d.value?[{uid:Math.floor(Math.random()*100),name:"image.png",status:"done",url:d.value}]:[]),r=async f=>new Promise((C,w)=>{const $=new FormData;$.append("image",f),Xo($).then(L=>{d.value=L.url,C("")}).catch(()=>{w("")})}),a=async f=>{if(!f)return!1;try{_.loading=!0,await r(f)}catch{}return _.loading=!1,!1},c=f=>{console.log("info=====",f)},m=f=>{_.previewVisible=f};return{...Q(e),...Q(_),imageUrl:d,fileList:i,triggerChange:n,beforeUpload:a,handleChange:c,setVisible:m}}}),Ra={key:0},Ea=y("div",{class:"ant-upload-text"},"Upload",-1),Ha={style:{display:"none"}};function Ba(e,t,o,n,_,d){const i=s("loading-outlined"),r=s("plus-outlined"),a=s("a-upload"),c=s("ExclamationCircleFilled"),m=s("a-typography-text"),f=s("a-image");return u(),b(z,null,[p(a,{style:{height:"110px"},"file-list":e.fileList,"onUpdate:fileList":t[0]||(t[0]=C=>e.fileList=C),action:"#","list-type":"picture-card",accept:"image/gif,image/jpeg,image/jpg,image/png,image/svg","before-upload":e.beforeUpload,onPreview:t[1]||(t[1]=()=>e.setVisible(!0)),onRemove:t[2]||(t[2]=C=>e.imageUrl="")},{default:l(()=>[e.imageUrl?M("",!0):(u(),b("div",Ra,[e.loading?(u(),h(i,{key:0})):(u(),h(r,{key:1})),Ea]))]),_:1},8,["file-list","before-upload"]),e.widthHeight.length>0?(u(),h(m,{key:0,type:"warning",style:{"font-size":"12px"}},{default:l(()=>[p(c),O(" "+I((e.tips?`${e.tips} `:"前台展示图片，建议尺寸比例: ")+`${e.widthHeight[0]} * ${e.widthHeight[1]}`),1)]),_:1})):M("",!0),y("div",Ha,[p(f,{width:200,preview:{visible:e.previewVisible,onVisibleChange:e.setVisible},src:e.value},null,8,["preview","src"])])],64)}const Aa=P(Ua,[["render",Ba]]),Ka=Object.freeze(Object.defineProperty({__proto__:null,default:Aa},Symbol.toStringTag,{value:"Module"})),Va=D({name:"SelectLang",props:{value:{type:String,default:void 0}},emits:["update:value","updateLabel","initLabel"],setup(e,t){const o=q({loading:!1,options:[]});W(()=>e.value,(i,r)=>{i&&!r&&!o.options.length&&Se({key:i}).then(a=>{o.options.push({label:a.zh_cn||a.en,value:i})})});const n=H({get:()=>{var i;return e.value&&t.emit("initLabel",(i=o.options.find(r=>r.value===e.value))==null?void 0:i.label),e.value},set:i=>{var r;t.emit("update:value",i),t.emit("updateLabel",(r=o.options.find(a=>a.value===i))==null?void 0:r.label)}}),_=i=>{if(!i){o.options=[];return}o.loading=!0,Zo({key:i}).then(r=>{o.options=r}).finally(()=>{o.loading=!1})},d=i=>{t.emit("update:value",i)};return{...Q(o),modelValue:n,remoteMethod:_,handleChange:d}}});const Ga={class:"lang-select-wrap"},qa={class:"item"},Na={class:"t"},Ya={class:"b"};function Wa(e,t,o,n,_,d){const i=s("a-spin"),r=s("a-select");return u(),b("div",Ga,[p(r,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=a=>e.modelValue=a),allowClear:"",showSearch:"",placeholder:"请输入多语言key","filter-option":!1,"not-found-content":e.loading?void 0:null,"option-label-prop":"label",onSearch:e.remoteMethod,onChange:e.handleChange,style:{"min-width":"200px"},options:e.options},{notFoundContent:l(()=>[p(i,{size:"small"})]),option:l(({value:a,label:c})=>[y("div",qa,[y("div",Na,I(c),1),y("div",Ya,I(a),1)])]),_:1},8,["value","not-found-content","onSearch","onChange","options"])])}const Qa=P(Va,[["render",Wa],["__scopeId","data-v-e7d0c585"]]),Ja=Object.freeze(Object.defineProperty({__proto__:null,default:Qa},Symbol.toStringTag,{value:"Module"})),Xa=D({name:"SelectWithAllComp",components:{SelectWithAll:en},props:{value:{type:String,default:""},placeholder:{type:String,default:"请选择操作系统"},type:{type:String,default:"platform",validate:e=>["platform","langs","channels"].includes(e)}},emits:["update:value","change"],setup(e,t){const{configState:o}=E(pe()),n=H({get:()=>e.value,set:_=>{t.emit("update:value",_),t.emit("change",_)}});return{...Q(e),modelValue:n,configState:o}}});function Za(e,t,o,n,_,d){const i=s("SelectWithAll");return u(),h(i,{value:e.modelValue,"onUpdate:value":t[0]||(t[0]=r=>e.modelValue=r),placeholder:e.placeholder,options:e.configState[e.type]},null,8,["value","placeholder","options"])}const el=P(Xa,[["render",Za]]),tl=Object.freeze(Object.defineProperty({__proto__:null,default:el},Symbol.toStringTag,{value:"Module"})),ol=D({name:"UploadFile",props:{value:{type:String,default:""}},setup(e,t){return{}}});function nl(e,t,o,n,_,d){return null}const al=P(ol,[["render",nl]]),ll=Object.freeze(Object.defineProperty({__proto__:null,default:al},Symbol.toStringTag,{value:"Module"}));function sl(e){const t=Object.assign({"./select-date-time/Index.vue":La,"./select-img-gallery/Index.vue":Pa,"./select-img/Index.vue":Ka,"./select-lang/Index.vue":Ja,"./select-with-all-comp/Index.vue":tl,"./select-with-all/Index.vue":tn,"./upload-file/Index.vue":ll});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const il={install:sl},rl=D({name:"FilterCell"}),cl=D({...rl,props:{record:{type:Object,default:()=>({})}},setup(e){const t=e,{getConfItem:o}=pe();return(n,_)=>{const d=s("a-tag"),i=s("a-descriptions-item"),r=s("a-descriptions"),a=s("a-typography-link"),c=s("a-tooltip");return t.record.is_filter===g(on).CLOSE?(u(),b(z,{key:0},[O("关闭")],64)):(u(),h(c,{key:1,placement:"left",color:"white",destroyTooltipOnHide:"",overlayStyle:{"max-width":"350px"}},{title:l(()=>[p(r,{column:1,size:"small",bordered:"",labelStyle:{width:"88px","white-space":"nowrap"}},{default:l(()=>[p(i,{label:"操作系统"},{default:l(()=>[t.record.f_os==="all"?(u(),b(z,{key:0},[O("ALL")],64)):(u(!0),b(z,{key:1},G(t.record.f_os.split("|").filter(m=>m),m=>(u(),h(d,{key:m},{default:l(()=>{var f;return[O(I((f=g(o)("platform",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),p(i,{label:"语种"},{default:l(()=>[t.record.lang==="all"?(u(),b(z,{key:0},[O("ALL")],64)):(u(!0),b(z,{key:1},G(t.record.lang.split("|").filter(m=>m),m=>(u(),h(d,{key:m},{default:l(()=>{var f;return[O(I((f=g(o)("langs",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),p(i,{label:"渠道"},{default:l(()=>[t.record.f_channel==="all"?(u(),b(z,{key:0},[O("ALL")],64)):(u(!0),b(z,{key:1},G(t.record.f_channel.split("|").filter(m=>m),m=>(u(),h(d,{key:m},{default:l(()=>{var f;return[O(I((f=g(o)("channels",m))==null?void 0:f.label),1)]}),_:2},1024))),128))]),_:1}),p(i,{label:"服务器"},{default:l(()=>[O(I(e.record.f_s_ids),1)]),_:1}),p(i,{label:"城堡等级"},{default:l(()=>[O(I(e.record.f_lv_ids),1)]),_:1}),e.record.uids!==void 0?(u(),h(i,{key:0,label:"uid 白名单"},{default:l(()=>[O(I(e.record.uids||"-"),1)]),_:1})):M("",!0)]),_:1})]),default:l(()=>[p(a,null,{default:l(()=>[O("开启")]),_:1})]),_:1}))}}}),ul=Object.freeze(Object.defineProperty({__proto__:null,default:cl},Symbol.toStringTag,{value:"Module"})),dl=D({name:"LangKey"}),pl=D({...dl,props:{langKey:{default:""},i18n_name:{default:()=>[]}},setup(e){const t=e,o=K({}),n=K(!1),_=()=>{JSON.stringify(o.value)==="{}"&&(n.value=!0,Se({key:t.langKey}).then(d=>{o.value=d}).finally(()=>n.value=!1))};return(d,i)=>{const r=s("a-spin"),a=s("a-typography-text"),c=s("a-descriptions-item"),m=s("a-descriptions"),f=s("ExclamationCircleOutlined"),C=s("a-popover");return u(),b(z,null,[O(I((t.i18n_name.find(w=>w.key===t.langKey)||{}).content||t.langKey||"-")+" ",1),t.langKey?(u(),h(C,{key:0,placement:"rightTop",onOpenChange:_,overlayInnerStyle:{"max-height":"500px","overflow-y":"auto"}},{content:l(()=>[g(n)?(u(),h(r,{key:0})):!g(n)&&JSON.stringify(g(o))==="[]"?(u(),h(a,{key:1,type:"secondary"},{default:l(()=>[O("无数据")]),_:1})):(u(),h(m,{key:2,column:1,size:"small",bordered:"",labelStyle:{width:"88px","white-space":"nowrap"},contentStyle:{"max-width":"400px"}},{default:l(()=>[(u(!0),b(z,null,G(g(o),(w,$)=>(u(),h(c,{key:$,label:$},{default:l(()=>[O(I(w),1)]),_:2},1032,["label"]))),128))]),_:1}))]),default:l(()=>[p(f)]),_:1})):M("",!0)],64)}}}),_l=Object.freeze(Object.defineProperty({__proto__:null,default:pl},Symbol.toStringTag,{value:"Module"}));function ml(e){const t=Object.assign({"./filter-cell/Index.vue":ul,"./lang-key/Index.vue":_l});for(const o in t){const n=t[o].default;n.name&&e.component(n.name,n)}}const fl={install:ml},gl={name:"has",beforeMount(e,t,o){var d;const n=t.value;nn.checkBtnPromission(n)?o.type==="template"&&e.replaceWith(...e.children):e.parentNode&&((d=e.parentNode)==null?void 0:d.removeChild).call(d,e)},mounted(){},beforeUpdate(){},updated(){},beforeUnmount(){},unmounted(){}},hl=Object.freeze(Object.defineProperty({__proto__:null,default:gl},Symbol.toStringTag,{value:"Module"})),vl=e=>{const t=Object.assign({"./CommonPermission.ts":hl});for(const o in t){const{name:n,beforeMount:_}=t[o].default;e.directive(n,_)}},yl={install:vl};let ae=null;ae=Gt({render:()=>Nt(Mn)});Hn(ae);console.log("发布",20241231);ae.use(an).use(V).use(yl).use(Ia).use(il).use(fl).use(qt);ae.mount("#app");
