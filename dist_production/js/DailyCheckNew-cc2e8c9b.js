import{M as j,D as A}from"./shared/DailyCheckNew/DailyCheckNewDetail-c47bc7fd.js";import{g as B,u as z,a as H,d as G,b as J,m as S,s as Q,c as W,e as X}from"./common-72b741fb.js";import{d as q,r as y,a,o as p,c as x,w as t,b as e,u as n,e as d,i as Y,f as Z,g as ee,h as te,j as ae,k as P,F as R,l as D,t as E}from"./vendor-70efaa3a.js";import{M as ne}from"./antd-016b48d6.js";const oe=q({__name:"Form",props:["editId"],emits:["close","refresh"],setup(M,{emit:f}){const L=M,b=y(!0),v=y(!1),$=()=>{v.value=!0,B(L.editId).then(i=>{o.value=i}).finally(()=>v.value=!1)};L.editId&&$();const o=y({id:0,month:void 0,bg_img:"",f_s_ids:""}),_=y([]),g=y(!1),I=y(),c=()=>{I.value.validate().then(()=>{g.value=!0;const i=new FormData;i.append("month",`${o.value.month}`),i.append("bg_img",o.value.bg_img||""),i.append("f_s_ids",`${o.value.f_s_ids}`),_.value.length&&i.append("file",_.value[0]),o.value.id&&i.append("id",`${o.value.id}`),(o.value.id?z:H)(i).then(()=>{f("close"),f("refresh")}).catch(()=>{}).finally(()=>{g.value=!1})}).catch(()=>{})},s=i=>(_.value=[..._.value||[],i],!1),k=async i=>_.value.length||o.value.id||o.value.file?Promise.resolve():Promise.reject("请上传奖品配置文件");return(i,u)=>{const N=a("a-select"),h=a("a-form-item"),U=a("SelectImg"),V=a("upload-outlined"),T=a("a-button"),w=a("a-upload"),F=a("ExclamationCircleFilled"),l=a("a-typography-text"),C=a("a-textarea"),K=a("a-form"),O=a("a-spin"),r=a("a-drawer");return p(),x(r,{open:n(b),"onUpdate:open":u[4]||(u[4]=m=>Y(b)?b.value=m:null),title:L.editId?"编辑":"新增",maskClosable:!1,width:600,onAfterOpenChange:u[5]||(u[5]=m=>!m&&f("close"))},{default:t(()=>[e(O,{spinning:n(v)},{default:t(()=>[e(K,{model:n(o),name:"basic",ref_key:"formRef",ref:I,"label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:t(()=>[e(h,{label:"月份",name:"month",rules:[{required:!0,message:"请输入月份"}]},{default:t(()=>[e(N,{value:n(o).month,"onUpdate:value":u[0]||(u[0]=m=>n(o).month=m),options:n(j),placeholder:"请选择月份"},null,8,["value","options"])]),_:1}),e(h,{label:"背景图",name:"bg_img"},{default:t(()=>[e(U,{value:n(o).bg_img,"onUpdate:value":u[1]||(u[1]=m=>n(o).bg_img=m),"width-height":[750,208]},null,8,["value"])]),_:1}),e(h,{label:"奖品配置",rules:[{validator:k}]},{default:t(()=>[e(w,{"file-list":n(_),"before-upload":s,onRemove:u[2]||(u[2]=()=>_.value=[]),"max-count":1,accept:"'.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'"},{default:t(()=>[e(T,null,{default:t(()=>[e(V),d(" 点击上传 ")]),_:1})]),_:1},8,["file-list"]),e(l,{type:"warning",style:{"font-size":"12px"}},{default:t(()=>[e(F),d(" 新增时必须上传奖励配置 ")]),_:1})]),_:1},8,["rules"]),e(h,{label:"服务器",name:"f_s_ids",rules:[{required:!0,message:"请输入服务器ID"}]},{default:t(()=>[e(C,{value:n(o).f_s_ids,"onUpdate:value":u[3]||(u[3]=m=>n(o).f_s_ids=m),placeholder:"请输入服务器ID，例如1,2-4,10,20-30","allow-clear":""},null,8,["value"])]),_:1}),e(h,{"wrapper-col":{offset:6,span:16}},{default:t(()=>[e(T,{type:"primary",onClick:c,loading:n(g)},{default:t(()=>[d("保存")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),_:1},8,["open","title"])}}}),de=q({__name:"Index",setup(M){Z.extend(ee);const f=te({editVisible:!1,editId:0}),L=ae(),b=y(),v=()=>b.value.requestTableData(!0),$=(c,s)=>{f.editVisible=c,f.editId=s||0},o=(c,s)=>S(`确定要删除${s?"选中的":"此条"}数据吗？`,W,{id:c}).then(()=>v()),_=c=>S("确定要复制此条数据并生成一份新数据吗？",X,{id:c}).then(()=>v()),g=y([]),I=(c,s,k)=>{g.value[k]=!0,s.status=1-c,ne.confirm({title:"提示",content:"确定要切换此条数据状态吗？",okText:"确定",cancelText:"取消",onOk:()=>{g.value[k]=!1,Q({id:s.id,status:c}).finally(()=>{v()})},onCancel:()=>{g.value[k]=!1}})};return(c,s)=>{const k=a("PlusOutlined"),i=a("a-button"),u=a("CloudDownloadOutlined"),N=a("a-space"),h=a("a-image"),U=a("a-typography-text"),V=a("a-switch"),T=a("a-divider"),w=a("a-typography-link"),F=a("CustomTable");return p(),P(R,null,[e(F,{ref_key:"RefCustomTable",ref:b,"data-api":n(J),params:{},columns:n(A)},{leftTool:t(()=>[e(N,null,{default:t(()=>[e(i,{type:"primary",onClick:s[0]||(s[0]=l=>$(!0))},{icon:t(()=>[e(k)]),default:t(()=>[d(" 新增 ")]),_:1}),e(i,{type:"primary",onClick:s[1]||(s[1]=l=>n(G)("checkin-v2"))},{icon:t(()=>[e(u)]),default:t(()=>[d(" 下载奖品模板 ")]),_:1})]),_:1})]),bodyCell:t(({record:l,column:C,index:K})=>{var O;return[C.key==="bg_img"?(p(),P(R,{key:0},[l.bg_img?(p(),x(h,{key:0,src:l.bg_img,height:60},null,8,["src"])):(p(),P(R,{key:1},[d("-")],64))],64)):D("",!0),C.key==="filter"?(p(),x(U,{key:1,code:!0},{default:t(()=>[d(E(l.filter.f_s_ids),1)]),_:2},1024)):D("",!0),C.key==="month"?(p(),P(R,{key:2},[d(E((O=n(j).filter(r=>l.month===r.value)[0])==null?void 0:O.label),1)],64)):D("",!0),C.key==="status"?(p(),x(V,{key:3,checked:l.status,"onUpdate:checked":r=>l.status=r,checkedValue:10,unCheckedValue:1,"checked-children":"开启","un-checked-children":"关闭",loading:n(g)[K],onClick:r=>I(r,l,K)},null,8,["checked","onUpdate:checked","loading","onClick"])):D("",!0),C.key==="action"?(p(),x(N,{key:4},{split:t(()=>[e(T,{type:"vertical",style:{margin:"0"}})]),default:t(()=>[e(w,{type:"success",onClick:r=>_(l.id)},{default:t(()=>[d("复制")]),_:2},1032,["onClick"]),e(w,{onClick:r=>$(!0,l.id)},{default:t(()=>[d("编辑")]),_:2},1032,["onClick"]),e(w,{onClick:r=>n(L).push(`/dailyCheck/new/detail/${l.id}`)},{default:t(()=>[d("详情")]),_:2},1032,["onClick"]),e(w,{type:"danger",danger:"",onClick:r=>o(l.id,!1)},{default:t(()=>[d("删除")]),_:2},1032,["onClick"])]),_:2},1024)):D("",!0)]}),_:1},8,["data-api","columns"]),n(f).editVisible?(p(),x(oe,{key:0,"edit-id":n(f).editId,onClose:s[2]||(s[2]=l=>$(!1)),onRefresh:v},null,8,["edit-id"])):D("",!0)],64)}}});export{de as default};
