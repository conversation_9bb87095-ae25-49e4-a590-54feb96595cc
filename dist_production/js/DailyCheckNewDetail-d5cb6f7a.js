import{f as d,P as c,h as l}from"./common-72b741fb.js";import{a as m}from"./shared/DailyCheckNew/DailyCheckNewDetail-c47bc7fd.js";import{d as u,m as _,h as f,r as C,a as s,n as D,o as h,c as w,w as v,p as T,b,u as a}from"./vendor-70efaa3a.js";import"./antd-016b48d6.js";const K=u({__name:"Detail",setup(N){const o=_(),e=f({id:o.params.id&&Number(o.params.id)||0}),t=C(),n=()=>t.value.requestTableData(!0);return(L,g)=>{const i=s("UploadBtn"),r=s("CustomTable"),p=D("has");return h(),w(r,{ref_key:"RefCustomTable",ref:t,"data-api":a(l),params:{id:a(e).id},columns:a(m),pagination:!1},{rightTool:v(()=>[T(b(i,{ref:"uploadBtn",onUploadSuccess:n,downloadApi:a(d),uploadData:{id:a(e).id},downloadData:{id:a(e).id},fileType:"checkin-v2",page:a(c).DAILY_CHECK_NEW},null,8,["downloadApi","uploadData","downloadData","page"]),[[p,"Operation"]])]),_:1},8,["data-api","params","columns"])}}});export{K as default};
