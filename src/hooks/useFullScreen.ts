import { message } from 'ant-design-vue';
import { ref } from 'vue';

export function useFullScreen() {
  const fullScreen = ref<boolean>(Math.abs(window.screen.height-window.document.documentElement.clientHeight) <= 17)

  const toggleFullScreen = () => {
    if (!fullScreen.value) {
      var docElm = document.documentElement
      if (docElm.requestFullscreen) {
        docElm.requestFullscreen()
      } else if (docElm.mozRequestFullScreen) {
        docElm.mozRequestFullScreen()
      } else if (docElm.webkitRequestFullScreen) {
        docElm.webkitRequestFullScreen()
      } else if (docElm.msRequestFullscreen) {
        docElm.msRequestFullscreen()
      } else {
        message.error({
          content: '请升级浏览器!',
          duration: 3
        })
      }
      fullScreen.value = true
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitCancelFullScreen) {
        document.webkitCancelFullScreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      } else {
        message.error({
          content: '请升级浏览器!',
          duration: 3
        })
      }
      fullScreen.value = false
    }
  }

  document.addEventListener("fullscreenchange", function() {
    if (document.fullscreenElement) {
      // 全屏状态下的操作
      console.log("进入全屏模式");
      fullScreen.value = true
    } else {
      // 非全屏状态下的操作
      console.log("退出全屏模式");
      fullScreen.value = false
    }
  });

  //判断是否是全屏状态
  window.onresize = function() {
    fullScreen.value = Math.abs(window.screen.height-window.document.documentElement.clientHeight) <= 17;
  }

  // 阻止F11键默认事件，用HTML5全屏API代替
  window.addEventListener('keydown', function (e) {
    e = e || window.event
    if ((e.keyCode === 122 || e.code === 'F11') && !fullScreen.value) {
      e.preventDefault()
      toggleFullScreen()
    }
  })

  return [
    fullScreen,
    toggleFullScreen
  ];
}
