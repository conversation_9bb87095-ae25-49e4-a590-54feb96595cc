<script setup lang="ts">
import theme from '@/utils/antdTheme'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import Layout from '@/layout/Index.vue'
import { useRoute } from 'vue-router'
import PhotoGallery from './packages/pictrue/Index.vue';
import { useUserStore } from './store';
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
import 'dayjs/locale/zh-cn';
dayjs.extend(utc).locale('zh-cn');
const noLayoutList = ['/login', '/', '/403']
const route = useRoute()

const userStore = useUserStore()
const { userState } = storeToRefs(userStore)

</script>

<template>
  <a-config-provider :theme="theme" :locale="zhCN">
    <a-watermark :content="userState.userInfo.email" v-bind="{ font: { color: 'rgba(0,0,0,0.03)' }, gap: [50, 50] }">
      <router-view v-if="noLayoutList.indexOf(route.path) > -1"></router-view>
      <Layout v-else/>
  
      <PhotoGallery></PhotoGallery>
    </a-watermark>
  </a-config-provider>
</template>

<style lang="scss">
</style>
