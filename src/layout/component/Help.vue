<script setup lang="ts">
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/store';

const route = useRoute()
const jump = () => {
  window.open(helpUrl.value)
}

const { userState } = storeToRefs(useUserStore())
const helpUrl = computed(() => {
  if (!route.meta.help) return ''
  else if (typeof route.meta.help === 'string') return route.meta.help
  else if (userState.value.crt_game && route.meta.help[userState.value.crt_game.game_project]) return route.meta.help[userState.value.crt_game.game_project]
  return ''
})
</script>

<template>
  <a-tooltip v-if="helpUrl" title="帮助">
    <a-button type="text" @click="jump" class="tools-item">
      <template #icon>
        <QuestionCircleOutlined />
      </template>
    </a-button>
  </a-tooltip>
</template>

<style lang="scss" scoped>
.log-wrap {
  cursor: pointer;
}
</style>