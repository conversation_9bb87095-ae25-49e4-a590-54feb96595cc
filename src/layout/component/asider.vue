<script setup lang="ts">
import { usePermissionStore, useSystemStore } from '@/store'
import { storeToRefs } from 'pinia'
import { useRoute, useRouter } from 'vue-router'
// Vue的ref, reactive, watch等函数由unplugin-auto-import自动导入，不需要手动导入

// 路由类型定义
interface RouteItem {
  path: string
  meta?: {
    title?: string
    icon?: string
    hidden?: boolean
    activeMenu?: string
    not_funplus_zone?: boolean
  }
  children?: RouteItem[]
  matched?: RouteItem[]
}

const { systemState } = storeToRefs(useSystemStore())
const { permissionState } = storeToRefs(usePermissionStore())
const isHide = (item: RouteItem) => item.meta && item.meta.hidden

const state = reactive({
  openKeys: [] as Array<string>,
  preOpenKeys: [] as Array<string>,
  filterRoutes: [] as Array<string>
})

const route = useRoute()
const router = useRouter()
const defaultActive = ref<string[]>([])

const setDefault = () => {
  if (route.matched.length > 0) {
    !systemState.value.asideStatus && (state.openKeys = [route.matched[0].path])
    defaultActive.value = []

    // 如果当前路由有activeMenu属性，使用activeMenu作为高亮菜单项
    if (route.meta.activeMenu) {
      defaultActive.value.push((route.meta.activeMenu as string) || route.path)
    } else {
      route.matched.forEach((item: any) => defaultActive.value.push(item.path))
      defaultActive.value.push(route.path)
    }
  }
}

watch(
  () => state.openKeys,
  (_newVal: string[], oldVal: string[]) => {
    state.preOpenKeys = oldVal
  }
)

// 对permissionState.routes过滤，判断crtGame是否为平台funplus_zone，如果是，则过滤掉不是funplus_zone的路由：item.meta?.not_funplus_zone
const filterRoutes = (routes: RouteItem[]) => {
  const crtGame = localStorage.getItem('crtGame')
  if (crtGame === 'funplus_zone') {
    // 递归过滤
    const filter = (routes: RouteItem[]) => {
      return routes.filter((item: RouteItem) => {
        if (item.children) {
          item.children = filter(item.children)
        }
        return !item.meta?.not_funplus_zone
      })
    }
    return filter(routes)
  } else {
    return routes
  }
}

// 计算过滤后的路由
const filteredRoutes = computed(() => {
  return filterRoutes(permissionState.value.routes)
})

watch(route, (_nv: any) => setDefault())
setDefault()
</script>

<template>
  <a-layout-sider v-model:collapsed="systemState.asideStatus"
                  :trigger="null"
                  collapsible
                  collapsed-width="50">
    <div class="layout-sider-content">
      <div class="menu-scroller">
        <a-menu v-model:selectedKeys="defaultActive"
                :openKeys="state.openKeys"
                class="header-menu"
                mode="inline"
                theme="dark">
          <template v-for="item in filteredRoutes"
                    :key="item.path">
            <template v-if="!isHide(item)">
              <template v-if="item.children && item.children.filter(x => !x.meta?.hidden).length > 1">
                <a-sub-menu :key="item.path"
                            :title="item.meta?.title">
                  <template #icon>
                    <component v-if="item.meta?.icon"
                               :is="item.meta.icon"></component>
                  </template>
                  <a-menu-item v-for="itemChild in item.children.filter(x => !x.meta?.hidden)"
                               :key="itemChild.path"
                               @click="router.push(itemChild.path)">
                    <component v-if="itemChild.meta?.icon"
                               :is="itemChild.meta.icon"></component>
                    {{ itemChild.meta?.title }}
                  </a-menu-item>
                </a-sub-menu>
              </template>
              <template v-else>
                <a-menu-item v-if="!isHide(item)"
                             :key="item.path"
                             @click="router.push(item.path)">
                  <component v-if="item.meta?.icon"
                             :is="item.meta.icon"></component>
                  <span>{{ item.meta?.title }}</span>
                </a-menu-item>
              </template>
            </template>
          </template>
        </a-menu>
      </div>
    </div>
  </a-layout-sider>
</template>

<style lang="scss" scoped>
.ant-layout-sider {
  position: relative;
  z-index: 10;
}
.layout-sider-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .menu-scroller {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .menu-bottom {
    height: 40px;
  }
}
</style>
