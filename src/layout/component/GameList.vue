<script setup lang="ts">
import { IGameConfig, useUserStore } from '@/store';
import { storeToRefs } from 'pinia';

const userStore = useUserStore()
const { userState } = storeToRefs(userStore)

const changeGame = (item: IGameConfig) => {
  const crtGame = localStorage.getItem('crtGame')
  console.log('item === ', item, crtGame)
  if (item.game_project === crtGame) return
  localStorage.setItem('crtGame', item.game_project)
  window.location.reload()
}
</script>

<template>
  <a-dropdown placement="bottom" arrow class="tools-item" v-if="userState.crt_game?.game_name">
    <a-button type="text">
      <a-avatar :src="userState.crt_game.game_icon_url" size="small" style="margin-right: 5px;" />
      {{ userState.crt_game.game_name }}
      <CaretDownFilled style="font-size: 12px; margin-left: 5px;" />
    </a-button>
    <template #overlay>
      <a-menu class="game-list">
        <a-menu-item v-for="(item, index) in userState.game_infos" :key="index" @click="changeGame(item)" :class="{ 'game-item': true, active: userState.crt_game.game_project === item.game_project }">
          <a-space>
            <a-avatar :src="item.game_icon_url" size="small" />
            {{ item.game_name }}
          </a-space>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<style lang="scss" scoped>
.tools-item {
  display: flex;
  align-items: center;
}
.game-list {
  ::v-deep(.game-item) {
    &.active {
      color: #1677ff;
      background-color: #e6f4ff;
    }
  }
}
</style>