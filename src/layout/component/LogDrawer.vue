<script setup lang="ts">
import { getLog } from '@/api/common';
import { useRoute } from 'vue-router';

const state = reactive({
  visible: false,
  containerH: window.innerHeight - 206,
  tableData: [],
  loading: false,

  page: 1,
  pagesize: 20,
  total: 0,
  showRoute: ['/tool/propQuery']
})
const colums = [
  { title: '日期', dataIndex: 'created_at', key: 'created_at', width: '160px' },
  { title: '操作人员', dataIndex: 'user_name', key: 'user_name', width: '100px' },
  { title: '事件', dataIndex: 'op_content', key: 'op_content', width: '100px' },
  { title: '内容', dataIndex: 'op_remark', key: 'op_remark', ellipsis: true },
]

const route = useRoute()
const title = computed(() => {
  const title = route.meta?.title
  return `用户操作日志 - ${title}`
})
watch(
  () => state.visible,
  (v) => {
    if (v) getData()
    else initState()
  }
)
const getData = async () => {
  const option = {
    page: state.page,
    page_size: state.pagesize
  }
  state.loading = true
  try {
    const res = await getLog(option) as any
    state.tableData = res.data
    state.total = res.total
  } catch (e) {
    console.log('日志数据获取异常', e)
  }
  state.loading = false
}

const initState = () => {
  state.tableData = []
  state.page = 1
  state.total = 0
}

const handleCurrentChange = (page: number) => {
  state.page = page
  getData()
}
</script>

<template>
  <a-tooltip title="日志" v-if="!(state.showRoute.includes(route.path))">
    <a-button type="text" @click="state.visible = true" class="tools-item">
      <template #icon><FileTextOutlined /></template>
    </a-button>
  </a-tooltip>
  <a-drawer
    v-model:open="state.visible"
    :title="title"
    placement="right"
    width="700px"
  >
    <a-table
      :data-source="state.tableData"
      :columns="colums"
      size="small"
      :pagination="false"
      :loading="state.loading"
      :scroll="{ y: state.containerH }"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'op_remark'">
          <div class="log-content" v-html="text" style="padding-right: 4px; white-space: break-spaces;"></div>
        </template>
      </template>
    </a-table>
    <div class="pagination">
      <a-pagination
        size="small"
        v-model:current="state.page"
        v-model:page-size="state.pagesize"
        :total="state.total"
        @change="handleCurrentChange"
        layout="total, prev, pager, next, sizes"
      />
    </div>
  </a-drawer>
</template>

<style lang="scss" scoped>
.log-icon {
  cursor: pointer;
}
.log-content {
  ::v-deep(a) {
    color: #1677ff;
  }
}
.pagination {
  display: flex;
  justify-content: flex-end;
  margin: 10px 0;
}
.pagination-sizes {
  height: 100%;
}
</style>
<style lang="scss">
</style>