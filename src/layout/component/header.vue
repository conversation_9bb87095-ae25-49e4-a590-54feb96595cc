<script setup lang="ts">
import { useSystemStore } from '@/store/system';
import { storeToRefs } from 'pinia';
import Help from './Help.vue';
import BreadCrumb from '../bread-crumb/Index.vue'
import LogDrawer from './LogDrawer.vue'
import { useFullScreen } from '@/hooks';
import router from '@/router'
import { useRoute } from 'vue-router';
import { usePermissionStore, useUserStore } from '@/store';
import GameList from './GameList.vue';
const systemStore = useSystemStore()
const { setAsideStatus } = systemStore
const { systemState } = storeToRefs(systemStore)

const [fullScreen, toggleFullScreen] = useFullScreen()

const route = useRoute()
const refresh = () => {
  router.replace(`/redirect${route.fullPath}`)
}

const userStore = useUserStore()
const { logout } = userStore
const { userState } = storeToRefs(userStore)

const { permissionState } = storeToRefs(usePermissionStore())
const logoClick = () => {
  router.push(permissionState.value.dynamicRoutes[0].path)
}

</script>

<template>
  <a-layout-header class="header">
    <div class="logo" @click="logoClick">
      <img src="@/assets/img/fp-logo.png" alt="" height="34">
    </div>
    <a-space :size="1">
      <a-button class="tools-item" type="text" @click="setAsideStatus">
        <template #icon>
          <MenuUnfoldOutlined v-if="systemState.asideStatus"/>
          <MenuFoldOutlined v-else/>
        </template>
      </a-button>
      <a-button class="tools-item" type="text" @click="refresh">
        <template #icon><ReloadOutlined /></template>
      </a-button>
      <div class="tools-item">
        <a-tag color="#ff4900" style="font-weight: 500;margin: 0;">{{ systemState.env }}</a-tag>
      </div>
      <BreadCrumb />
    </a-space>
    <div class="menu-list"></div>
    <a-space :size="1">
      <Help />
      <LogDrawer />
      <a-tooltip>
        <template #title>
          {{ fullScreen ? '退出全屏' : '全屏' }}
        </template>
        <a-button class="tools-item" type="text" @click="toggleFullScreen">
          <template #icon>
            <CompressOutlined v-if="fullScreen" />
            <ExpandOutlined v-else />
          </template>
        </a-button>
      </a-tooltip>
      <GameList />
      <a-dropdown placement="bottom" arrow class="tools-item">
        <a-button type="text">
          {{ userState.userInfo.username }}
          <CaretDownFilled style="font-size: 12px; margin-left: 5px;" />
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <a-space>
                <PoweroffOutlined />
                <a href="javascript:;" @click="logout">退出登录</a>
              </a-space>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </a-space>
  </a-layout-header>
</template>

<style lang="scss" scoped>
.header {
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: stretch;
  background-color: #fff;
  line-height: 38px;
  height: $nav_h;
  z-index: 100;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
  &>div {
    flex-shrink: 0;
  }
  .menu-list {
    flex: 1;
  }
  .logo {
    cursor: pointer;
    padding: 0 12px 0 0;
  }
  ::v-deep(.tools-item) {
    height: 38px;
    padding: 0 12px;
    width: auto;
  }
}
</style>