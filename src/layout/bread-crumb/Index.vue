<script setup lang="ts" name="BreadCrumb">
import router from '@/router';
import { usePermissionStore } from '@/store';
import { compile } from 'path-to-regexp'
import { storeToRefs } from 'pinia';
import { useRoute, RouteLocationMatched } from 'vue-router';

const currentRoute = useRoute()
const pathCompile = (path: string) => {
  const { params } = currentRoute
  const toPath = compile(path)
  return toPath(params)
}
const state = reactive({
  breadcrumbs: [] as Array<RouteLocationMatched>,
  getBreadcrumb: () => {
    const matched = currentRoute.matched.filter((item) => item.meta && item.meta.title)
    state.breadcrumbs = matched.filter((item) => {
      return item.meta && item.meta.title && item.meta.breadcrumb !== false
    })
  },
  isDashboard (route: RouteLocationMatched) {
    const name = route && route.name
    if (!name) {
      return false
    }
    return name.toString().trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()
  },
  handleLink (item: any) {
    const { redirect, path } = item
    if (redirect) {
      router.push(redirect).catch((err) => {
        console.warn(err)
      })
      return
    }
    router.push(pathCompile(path)).catch((err) => {
      console.warn(err)
    })
  }
})
watch(() => currentRoute.path, (path) => {
  if (path.startsWith('/redirect/')) {
    return
  }
  state.getBreadcrumb()
})

const { permissionState } = storeToRefs(usePermissionStore())
const goHome = () => {
  router.push(permissionState.value.routes.filter(item => (!item.meta || !item.meta.hidden))[0].path)
}

onBeforeMount(() => {
  state.getBreadcrumb()
})

</script>

<template>
  <a-breadcrumb class="app-breadcrumb">
    <a-breadcrumb-item>
      <a @click.prevent="goHome"><home-outlined /></a>
    </a-breadcrumb-item>
    <a-breadcrumb-item v-for="(item, index) in state.breadcrumbs" :key="item.path" >
      <span v-if="item.redirect === 'noredirect' || index === state.breadcrumbs.length - 1" class="no-redirect" >{{ item.meta.title }}</span>
      <a v-else @click.prevent="state.handleLink(item)" >{{ item.meta.title }}</a>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<style lang="scss" scoped>
.app-breadcrumb {
  margin-left: 15px;
}
</style>