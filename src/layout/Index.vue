<script setup lang="ts">
import Header from './component/header.vue';
import Asider from './component/asider.vue';

</script>

<template>
  <div class="home-wrap">
    <a-layout>
      <Header />
      <a-layout class="content-wrap">
        <Asider />
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <a-layout-content class="page-content" :key="route.fullPath">
              <div class="main-wrap" :key="route.fullPath">
                <component :is="Component" :key="route.fullPath" />
              </div>
            </a-layout-content>
          </transition>
        </router-view>
      </a-layout>
    </a-layout>
  </div>
</template>

<style lang="scss" scoped>
.home-wrap{
  .content-wrap{
    height: calc(100vh - #{$nav_h});
    background-color: var(--fp-bg-color-page);
  }
  .page-content {
    overflow-y: auto;
    position: relative;
    height: 100%;
  }
  .main-wrap{
    padding: var(--fp-page-padding);
    height: 100%;
    flex-direction: column;
    :deep(.mw-child-h-auto) {
      flex: 1;
    }
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--fp-page-padding);
  }
}
</style>