<script setup lang="ts">
import { downloadProps, getPlatformPropsList, delPlatformProps, copyPlatformProps } from '@/api/resource'
import { PLATFORM_PROPS_COLUMNS_MAP } from '../const'
import PlatformPropsForm from './PlatformPropsForm.vue'
import { messageConfirm } from '@/utils'

const state = reactive({
  searchParams: {
    search_key: ''
  },
  editVisible: false,
  editId: 0,
  optionsType: [
    { label: '积分加倍卡', value: 1 },
    { label: '升级体验卡', value: 2 },
    { label: '充值无忧服务卡', value: 3 }
  ]
})

// 列表数据获取
const RefCustomTable = ref()
const search = () => RefCustomTable.value.requestTableData(true)

// 新增/编辑
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 删除
const batchDel = (id: string, isBatch: boolean) =>
  messageConfirm(`确定要删除${isBatch ? '选中的' : '此条'}数据吗？`, delPlatformProps, { id }).then(() => search())

// 复制
const copy = (id: number) => messageConfirm(`确定要复制此条数据并生成一份新数据吗？`, copyPlatformProps, { id }).then(() => search())
</script>

<template>
  <CustomTable ref="RefCustomTable"
               :data-api="getPlatformPropsList"
               :params="state.searchParams"
               :columns="PLATFORM_PROPS_COLUMNS_MAP"
               no-card>
    <template #leftTool>
      <a-space wrap>
        <a-button type="primary"
                  @click="showEdit">
          <template #icon>
            <PlusOutlined />
          </template>新增道具
        </a-button>
        <a-input v-model:value="state.searchParams.search_key"
                 clearable
                 placeholder="请输入item_id或道具名称"></a-input>
        <a-button type="primary"
                  @click="search">搜索</a-button>
        <a-button @click="() => { state.searchParams.search_key = ''; search() }">重置</a-button>
      </a-space>
    </template>
    <!-- <template #rightTool>
      <UploadBtn v-has="'Operation'" ref="uploadBtn" @uploadSuccess="search" :downloadApi="downloadProps"
        fileType="lang" :page="PAGE_CONF.PLATFORM_PROPS" />
    </template> -->
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'item_id'">
        {{ 'platform_item_' + record.id }}
      </template>
      <template v-if="column.key === 'item_type'">
        {{ state.optionsType.find(item => item.value === record.item_type)?.label }}
      </template>
      <template v-if="column.key === 'name_key'">
        {{ record.i18n_name[0]?.content }}
      </template>
      <template v-if="column.key === 'activation_type'">
        {{ record.activation_type === 1 ? '手动激活' : '自动激活' }}
      </template>
      <template v-if="column.key === 'description_key'">
        {{ record.i18n_description[0]?.content }}
      </template>
      <template v-if="column.key === 'icon'">
        <PropIcon :prop-detail="{ image: record.icon }"
                  :style="{ width: '50px', height: '50px' }" />
      </template>
      <template v-if="column.key === 'rank_limit'">
        {{ JSON.parse(record.rank_limit).min }} - {{ JSON.parse(record.rank_limit).max }}
      </template>
      <template v-if="column.key === 'effect_value'">
        {{ record.item_type === 1 ? `充值额外获取${record.effect_value}%积分` :
          '等级提升1级' }}
      </template>
      <template v-if="column.key === 'action'">
        <a-space :size="0">
          <a-button type="link"
                    @click="copy(record.id)">复制</a-button>
          <a-button type="link"
                    @click="showEdit(true, record.id)">编辑</a-button>
          <a-button type="link"
                    danger
                    @click="batchDel(record.id, false)">删除</a-button>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <PlatformPropsForm v-if="state.editVisible"
                     :edit-id="state.editId"
                     @close="state.editVisible = false"
                     @refresh="search" />
</template>

<style lang="scss" scoped></style>
