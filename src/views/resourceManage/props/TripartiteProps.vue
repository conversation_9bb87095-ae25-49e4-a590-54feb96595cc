<script setup lang="ts">
import { getTripartitePropsList, delTripartiteProps } from '@/api/resource';
import { TRIPARTITE_PROPS_COLUMNS_MAP } from '../const';
import Detail from './TripartiteDetail.vue';
import { messageConfirm } from '@/utils';

const state = reactive({
  searchParams: {
    search_key: '',
    type: 2
  },
  editVisible: false,
  editId: 0
})

// 列表数据获取
const RefCustomTable = ref()
// 搜索
const search = () => RefCustomTable.value.requestTableData(true)

// 新增/编辑
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 删除
const batchDel = (id: string, isBatch: boolean) => messageConfirm(
  `确定要删除${isBatch ? '选中的' : '此条'}数据吗？`,
  delTripartiteProps,
  { id }
).then(() => search())

</script>

<template>
  <a-row justify="space-between">
    <a-col :span="12">

    </a-col>
  </a-row>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getTripartitePropsList"
    :params="state.searchParams"
    :columns="TRIPARTITE_PROPS_COLUMNS_MAP"
    no-card
  >
    <template #top>
      <a-space wrap>
        <a-input
          v-model:value="state.searchParams.search_key"
          placeholder="请输入道具名称"
        ></a-input>
        <a-button type="primary" @click="search">搜索</a-button>
        <a-button @click="() => { (state.searchParams.search_key = ''); search() }">重置</a-button>
        <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增道具
      </a-button>
      </a-space>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'type'">
        {{ record.type === 1 ? '游戏道具' : '三方券' }}
      </template>
      <template v-if="column.key === 'image'">
        <PropIcon :prop-detail="record" :style="{ width: '50px', height: '50px' }" />
      </template>
      <template v-if="column.key === 'action'">
        <a-space :size="0">
          <a-button type="link" @click="showEdit(true, record.id)">编辑</a-button>
          <a-button type="link" danger @click="batchDel(record.id, false)">删除</a-button>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Detail v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Detail>
</template>

<style lang="scss" scoped>

</style>
