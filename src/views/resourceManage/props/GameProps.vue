<script setup lang="ts">
import { getPropsList } from '@/api/resource';
import { GMAE_PROPS_COLUMNS_MAP } from '../const';

const state = reactive({
  searchParams: {
    search_key: ''
  }
})

// 列表数据获取
const RefCustomTable = ref()
// 搜索
const search = () => RefCustomTable.value.requestTableData(true)

</script>

<template>
  <a-row justify="space-between">
    <a-col :span="12">
      
    </a-col>
  </a-row>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getPropsList"
    :params="state.searchParams"
    :columns="GMAE_PROPS_COLUMNS_MAP"
    no-card
  >
    <template #top>
      <a-space wrap>
        <a-input
          v-model:value="state.searchParams.search_key"
          placeholder="请输入item_id或道具名称"
        ></a-input>
        <a-button type="primary" @click="search">搜索</a-button>
        <a-button @click="() => { (state.searchParams.search_key = ''); search() }">重置</a-button>
      </a-space>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'image'">
        <PropIcon :prop-detail="record" :style="{ width: '50px', height: '50px' }" />
      </template>
      <!-- <template v-if="column.type === 'lang'">
        <a-tooltip :title="record.descs[column.key]">
          {{ record.names[column.key] }}
        </a-tooltip>
      </template> -->
    </template>
  </CustomTable>
</template>

<style lang="scss" scoped>

</style>