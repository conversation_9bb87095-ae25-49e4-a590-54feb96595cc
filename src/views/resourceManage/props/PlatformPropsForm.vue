<script setup lang="ts">
import { getPlatformPropsDetail, createPlatformProps, updatePlatformProps } from '@/api/resource'
import { message } from 'ant-design-vue'
import { ref, reactive, watch } from 'vue'

interface IFormState {
  id: number | undefined
  item_type: number
  name_key: string
  description_key: string
  effect_value: number | null
  activation_period: number | null
  effect_period: number | null
  rank_limit: string
  icon: string
  activation_type: number
  jump_url: string
  is_stackable: number
  status: number
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getPlatformPropsDetail(props.editId)
    .then((res: any) => {
      console.log('res', res)
      formState.value = res
      // 更新rankLimit
      const resRankLimit = JSON.parse(res.rank_limit)
      console.log('resRankLimit', resRankLimit)
      rankLimit.min = resRankLimit.min || 1
      rankLimit.max = resRankLimit.max || 7
    })
    .finally(() => (modalLoading.value = false))
}
if (props.editId) getDetail()

const formState = ref<IFormState>({
  id: undefined,
  item_type: 1,
  name_key: '',
  description_key: '',
  effect_value: null,
  activation_period: null,
  effect_period: null,
  rank_limit: '{"min":1,"max":7}',
  icon: '',
  activation_type: 1,
  jump_url: 'test',
  is_stackable: 0, // 是否可叠加 0: 不可叠加 1: 可叠加
  status: 1 // 状态 状态 1:启用 0:禁用
})

const state = reactive({
  optionsType: [
    { label: '积分加倍卡', value: 1 },
    { label: '升级体验卡', value: 2 },
    { label: '充值无忧服务卡', value: 3 }
  ],
  optionsActivationType: [
    { label: '手动激活', value: 1 },
    { label: '自动激活', value: 2 }
  ]
})

const open = ref(true)

const submitLoading = ref(false)
const formRef = ref()
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      const { id, ...data } = formState.value
      // 校验等级限制
      const limit = JSON.parse(formState.value.rank_limit)
      console.log('limit', limit)
      if (!limit.min || !limit.max) return message.error('请输入等级限制')
      if (limit.min > limit.max) return message.error('最高等级不能小于最低等级')

      submitLoading.value = true
      if (formState.value.item_type === 3) {
        data.effect_period = 0
      }
      const method = props.editId ? updatePlatformProps : createPlatformProps
      method({ ...data, id: id || undefined })
        .then(() => {
          emits('close')
          emits('refresh')
        })
        .finally(() => {
          submitLoading.value = false
        })
    })
    .catch((error: any) => {
      console.error('验证失败:', error)
    })
}

const rankLimit = reactive({
  min: 1,
  max: 7
})

watch(rankLimit, (val) => {
  formState.value.rank_limit = JSON.stringify(val)
})

watch(
  () => formState.value.item_type,
  (val) => {
    if (val === 2) {
      formState.value.effect_value = 1
    } else if (val === 3) {
      formState.value.effect_value = 1
    }
  }
)
</script>

<template>
  <a-drawer v-model:open="open"
            title="编辑规则"
            :maskClosable="false"
            :width="600"
            @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-form :model="formState"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            ref="formRef">
      <a-form-item label="道具类型"
                   required
                   name="item_type"
                   :rules="[{ required: true, message: '请选择道具类型' }]">
        <a-select style="width: 100%;"
                  v-model:value="formState.item_type"
                  :options="state.optionsType" />
      </a-form-item>
      <a-form-item label="道具icon"
                   required
                   name="icon"
                   :rules="[{ required: true, message: '请上传图标' }]">
        <SelectImg v-model:value="formState.icon"
                   :width-height="[60, 60]"></SelectImg>
      </a-form-item>
      <a-form-item label="道具名称"
                   required
                   name="name_key">
        <SelectLang v-model:value="formState.name_key" />
      </a-form-item>
      <a-form-item label="道具说明"
                   required
                   name="description_key">
        <SelectLang v-model:value="formState.description_key" />
      </a-form-item>
      <a-form-item label="参数设置"
                   required
                   name="effect_value"
                   :rules="[{ required: true, message: '请输入参数值' }, { type: 'number', message: '必须为数字' }]">
        <template v-if="formState.item_type === 1">
          <a-space align="center">
            <a-typography-text>充值额外获取</a-typography-text>
            <a-input-number v-model:value="formState.effect_value"
                            :min="1"
                            :step="1"
                            placeholder="充值额外获取积分"
                            addon-after="%积分" />
          </a-space>
        </template>
        <template v-else-if="formState.item_type === 2">
          <a-space align="center">
            <a-typography-text>提升等级数</a-typography-text>
            <a-input-number v-model:value="formState.effect_value"
                            :disabled="true"
                            :min="1"
                            :precision="0"
                            placeholder="提升等级数"
                            addon-after="级" />
          </a-space>
        </template>
        <template v-else-if="formState.item_type === 3">
          <a-space align="center">
            <a-input-number v-model:value="formState.effect_value"
                            :min="1"
                            :disabled="true"
                            addon-after="次提工单服务" />
          </a-space>
        </template>
      </a-form-item>
      <!-- 激活方式-单选下拉列表 -->
      <a-form-item label="激活方式"
                   required
                   name="activation_type">
        <a-select v-model:value="formState.activation_type"
                  :options="state.optionsActivationType" />
      </a-form-item>
      <a-form-item label="有效期"
                   required
                   name="activation_period">
        <a-space align="center">
          <a-typography-text>获取后</a-typography-text>
          <a-input-number v-model:value="formState.activation_period"
                          :min="1"
                          addon-after="天有效"
                          placeholder="获取后有效天数" />
        </a-space>
      </a-form-item>
      <a-form-item v-if="formState.item_type !== 3"
                   label="使用时效"
                   required
                   name="effect_period">
        <a-space align="center">
          <a-typography-text>激活后</a-typography-text>
          <a-input-number v-model:value="formState.effect_period"
                          :min="1"
                          addon-after="h有效"
                          placeholder="激活后有效时长" />
        </a-space>
      </a-form-item>
      <a-form-item label="使用等级限制"
                   required
                   name="rank_limit">
        <a-space align="center">
          <a-input-number v-model:value="rankLimit.min"
                          :min="1"
                          placeholder="最低等级" />
          <span class="self-center">-</span>
          <a-input-number v-model:value="rankLimit.max"
                          :min="rankLimit.min || 1"
                          :max="7"
                          placeholder="最高等级" />
        </a-space>
      </a-form-item>
      <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
        <a-button type="primary"
                  @click="submit"
                  :loading="submitLoading">保存</a-button>
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<style lang="scss" scoped></style>
