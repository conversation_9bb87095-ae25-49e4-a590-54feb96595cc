<script setup lang="ts">
import { createTripartiteProps, updateTripartiteProps, getTripartitePropsDetail } from '@/api/resource';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

interface FormState {
  id: number
  name_key: string | undefined
  item_id: string
  image: string

  [key: string]: any
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getTripartitePropsDetail(props.editId)
    .then((res: any) => {
      formState.value = res
      if (res.online_at && res.offline_at) {
        formState.value.is_forever = 0
        formState.value.times = [res.online_at, res.offline_at]
      } else {
        formState.value.is_forever = 1
        formState.value.times = undefined
      }
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  name_key: undefined,
  item_id: '',
  image: '',
  type: 2,
})

const state = reactive({
  optionsVip: [
    { label: '三方券', value: 2 },
  ]
})

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      if (props.editId) {
        updateTripartiteProps(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        createTripartiteProps(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
    .catch(() => {})
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑' : '新增'" :maskClosable="false" :width="600" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="道具类型" name="type" :rules="[{ required: true, message: '请选择道具类型' }]">
          <a-select style="width: 100%;" v-model:value="formState.type" :disabled="true" :options="state.optionsVip" />
        </a-form-item>
        <a-form-item label="三方券id" name="item_id" :rules="[{ required: true, message: '请输入三方券id' }]">
          <a-input v-model:value="formState.item_id" placeholder="请输入三方券id"></a-input>
        </a-form-item>
        <a-form-item label="道具名称" name="name_key" :rules="[{ required: true, message: '请选择道具名称多语言' }]">
          <SelectLang v-model:value="formState.name_key" />
        </a-form-item>
        <a-form-item label="道具icon" name="image" :rules="[{ required: true, message: '请上传道具icon' }]">
          <SelectImg v-model:value="formState.image" :width-height="[60, 60]"></SelectImg>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>

</style>
