<script lang="ts">
import PlatformProps from './PlatformProps.vue'
import GameProps from './GameProps.vue'
import TripartiteProps from './TripartiteProps.vue'

export default {
  components: {
    PlatformProps,
    GameProps,
    TripartiteProps
  }
}
</script>
<script setup lang="ts">
const TABS_CONFIG = [
  { label: '游戏道具', key: 'GameProps' },
  { label: '三方道具', key: 'TripartiteProps' },
  { label: '平台道具', key: 'PlatformProps' },
]

const activeKey = ref(TABS_CONFIG[0].key)

// 仅平台空间下显示平台道具
const isPlatform = computed(() => {
  return localStorage.getItem('crtGame') === 'funplus_zone'
})

</script>

<template>
  <a-card class="mw-child-h-auto">
    <a-tabs v-model:activeKey="activeKey" class="tabs-wrap">
      <a-tab-pane v-for="item in TABS_CONFIG.filter(item => !isPlatform ? item.key !== 'PlatformProps' : true)"
        :tab="item.label" :key="item.key" class="tab-pane">
        <keep-alive>
          <component :is="item.key" v-if="item.key === activeKey"></component>
        </keep-alive>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<style lang="scss" scoped>
.tab-pane {
  height: calc(100vh - 160px);
  display: flex;
  flex-direction: column;
}

.tabs-wrap {
  margin-top: -15px;

  & :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
}
</style>
