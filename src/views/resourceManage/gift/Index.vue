<script setup lang="ts">
import { delGiftPkg, getGiftPkgList, exportGiftPkg } from '@/api/resource';
import { GIFT_COLUMNS_MAP } from '../const';
import { messageConfirm } from '@/utils';
import { PAGE_CONF } from '@/enum';
import Form from './Form.vue'
import Item from './Item.vue'

const state = reactive({
  editVisible: false,
  editId: 0,
})

// 列表数据获取
const RefCustomTable = ref()
const search = () => RefCustomTable.value.requestTableData(true)
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}// 删除
const del = (id: string) => messageConfirm(
  `确定要删除此条数据吗？`,
  delGiftPkg,
  { id }
).then(() => search())
</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getGiftPkgList" :params="{}" :columns="GIFT_COLUMNS_MAP">
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">道具打包</a-button>
      <UploadBtn style="margin-left: 30px;" v-has="'Operation'" ref="uploadBtn" @uploadSuccess="search"
        fileType="gift-package" :page="PAGE_CONF.GIFTCONFIG" :downloadData="{}" :hideDownloadBtn="true" />
      <a-button style="margin-left: 30px;" type="primary" @click="exportGiftPkg()">
        <template #icon>
          <PlusOutlined />
        </template>
        导出礼包
      </a-button>
    </template>
    <template #bodyCell="{ record, column }">
      <template v-if="column.key === 'desc_key'">
        <LangKey :lang-key="record.desc_key" :i18n_name="record.i18n_name"></LangKey>
      </template>
      <template v-if="column.key === 'icon_url'">
        <a-image v-if="record.icon_url" :src="record.icon_url" :height="60" />
        <template v-else>-</template>
      </template>
      <template v-if="column.key === 'item_list'">
        <template v-if="record.item_list.length >= 3">
          <Item v-for="(item, i) in record.item_list.slice(0, record.show_all ? record.item_list.length : 2)"
            style="margin-top: 5px;" :item="item" :key="i"></Item>
          <a-button v-if="!record.show_all" type="link" @click="record.show_all = !record.show_all">查看全部</a-button>
        </template>
        <Item v-else v-for="(item, i) in record.item_list" style="margin-top: 5px;" :item="item" :key="i"></Item>
      </template>
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0;" /></template>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="del(record.id)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search" />
</template>

<style lang="scss" scoped></style>
