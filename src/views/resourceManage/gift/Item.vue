<script setup lang="ts">
const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  },
  showNum: {
    type: Boolean,
    default: true
  }
})
</script>

<template>
  <div>
    <a-flex align="center">
      <div style="flex-shrink: 0;">
        <a-image v-if="props.item.gift_type === 1 || props.item.gift_type === 2" :src="props.item.image" width="50px" :preview="false"></a-image>
        <svg v-if="props.item.gift_type === 0" xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 83 83" fill="none" style="flex-shrink: 0;">
          <circle cx="41.5" cy="41.5" r="36.5" fill="#F1B545" stroke="#F5F070" strokeWidth="10" />
          <path
            d="M56.5652 53.8262L62.0435 29.1741L49.7174 37.3915L41.5 23.6958L33.2826 37.3915L20.9565 29.1741L27.8044 53.8262H56.5652Z"
            fill="#F7F473"
          />
        </svg>
      </div>
      <div style="margin: 0 5px; overflow: hidden;">
        <div class="t-name">{{ props.item.name }}</div>
        <div class="t-name">{{ props.item.item_id }}</div>
      </div>
      <div class="num" style="flex-shrink: 0;" v-if="props.showNum">X{{ props.item.num }}</div>
    </a-flex>
  </div>
</template>

<style lang="scss" scoped>
.t-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  & +.t-name {
    color: #999;
  }
}
</style>
