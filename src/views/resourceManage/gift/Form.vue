<script setup lang="ts">
import { createGiftPkg, getGiftPkgDetail, updateGiftPkg } from '@/api/resource';
import FormPropItem from './FormPropItem.vue'

export interface IItem {
  gift_type: 0 | 1 | 2
  game_project: string
  image: string
  image_quality: string
  internal_id: string
  item_id: string
  label: string
  lang: string
  name: string
  value: string
  num: number
}

interface IFormState {
  id: number
  gift_id: string
  name: string
  desc_key: string | undefined
  icon_url: string
  item_list: Array<IItem>
  gift_value: number
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const formState = ref<IFormState>({
  id: 0,
  gift_id: '',
  name: '',
  desc_key: undefined,
  icon_url: '',
  item_list: [],
  gift_value: 0,
})

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getGiftPkgDetail(props.editId)
    .then((res: any) => {
      formState.value = { ...formState.value, ...res }
      formState.value.item_list = JSON.parse(res.item_list)
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      data.item_list = JSON.stringify(data.item_list.map(item => ({
        internal_id: item.internal_id,
        item_id: item.item_id,
        num: item.num,
        gift_type: item.gift_type,
        game_project: item.game_project,
      }))) as any
      if (props.editId) {
        // 编辑
        updateGiftPkg(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        // 新增
        createGiftPkg(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
    .catch(() => {})
}
</script>

<template>
  <a-drawer v-model:open="open" title="编辑规则" :maskClosable="false" :width="600" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="gift_id" name="gift_id">
          <a-input v-model:value="formState.gift_id" placeholder="创建后自动生成，不支持修改" disabled />
        </a-form-item>
        <a-form-item label="礼包名称(后台)" name="name">
          <a-input v-model:value="formState.name" placeholder="仅做后台展示用" />
        </a-form-item>
        <a-form-item label="礼包名称(前台)" name="desc_key">
          <SelectLang v-model:value="formState.desc_key" placeholder="选择多语言key,不填写则默认使用道具名称" />
        </a-form-item>
        <a-form-item label="礼包icon" name="icon_url">
          <SelectImg v-model:value="formState.icon_url" :width-height="[72, 72]"></SelectImg>
        </a-form-item>
        <a-form-item label="礼包价值" name="gift_value" :rules="[{ required: true, message: '请输入礼包价值' }]">
          <a-input-number v-model:value="formState.gift_value" placeholder="请输入礼包价值" addon-after="积分"/>
          <a-typography-text type="warning"  style="font-size: 12px;display: block;">
            <ExclamationCircleFilled />
            会员专享礼包需填写，发生退款时扣除对应积分
          </a-typography-text>
        </a-form-item>
        <a-form-item label="添加道具" name="item_list" :rules="[{ type: 'array', required: true, message: '请添加道具' }]">
          <FormPropItem v-model:value="formState.item_list"></FormPropItem>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>

</style>