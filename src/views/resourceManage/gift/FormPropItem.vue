<template>
  <a-flex>
    <a-input-group compact>
      <a-select v-model:value="type" @change="change" placeholder="道具类型" :options="configState.prize_type"
        style="width: 100px;"></a-select>
      <a-select v-if="type === 1 || type === 2 || type === 3" v-model:value="itemKey" showSearch placement="bottomRight"
        placeholder="请输入多语言key" :filter-option="false" :not-found-content="loading ? undefined : null"
        option-label-prop="name" @search="remoteMethod" @select="select" style="width: 230px;" :options="options"
        label-in-value :dropdownStyle="{ 'width': '300px !important', color: 'red' }">
        <template #option="option">
          <div>{{ option.name }}</div>
          <div style="color: #999;">{{ option.item_id }}</div>
        </template>
      </a-select>
    </a-input-group>
    <a-button type="primary" @click="addItem">
      <template #icon>
        <PlusOutlined />
      </template>
    </a-button>

  </a-flex>
  <a-table style="margin-top: 5px;" size="small" :columns="GIFT_ITEM_COLUMNS_MAP" :data-source="value"
    :pagination="false">
    <template #bodyCell="{ record, index, column }">
      <Item v-if="column.key === 'item_id'" :item="record" :show-num="false"></Item>
      <template v-if="column.key === 'num'"><a-input-number v-model:value="value[index].num"
          style="width: 60px;"></a-input-number></template>
      <template v-if="column.key === 'action'">
        <a-typography-link type="danger" @click="() => value.splice(index, 1)">删除</a-typography-link>
      </template>
    </template>
  </a-table>

</template>

<script lang="ts">
import { getPropsOpts } from '@/api/resource';
import { useConfigStore } from '@/store';
import { Form, message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';
import { defineComponent } from 'vue'
import { IItem } from './Form.vue';
import { GIFT_ITEM_COLUMNS_MAP } from '../const';
import Item from './Item.vue'
export default defineComponent({
  name: 'FormPropItem',

  components: { Item },

  props: {
    value: {
      type: Array<IItem>,
      default: () => [],
    }
  },

  setup(_, { emit }) {
    const formItemContext = Form.useInjectFormItemContext();
    const triggerChange = (val: any[]) => {
      emit('update:value', val);
      formItemContext.onFieldChange();
    };
    const state = reactive({
      type: undefined,
      itemKey: undefined,
      loading: false,
      crtItem: {} as IItem,
      options: [] as any[]
    })

    const { configState } = storeToRefs(useConfigStore())

    watch(
      () => _.value,
      (val: any) => {
        if (val && val.length === 0) {
          formItemContext.onFieldChange();
        }
      },
      {
        deep: true
      }
    )

    const remoteMethod = (query: string) => {
      console.log('remoteMethod query', query)
      if (!query) return
      state.loading = true
      getPropsOpts({ search_key: query, type: state.type, game_project: localStorage.getItem('crtGame') || 'ss_global' }).then(res => {
        state.options = res.map((item: any) => ({
          ...item,
          value: item.item_id,
          label: item.name,
        }))
        console.log(state.options)
      }).finally(() => {
        state.loading = false
      })
    }

    const select = (_: any, option: any) => {
      console.log('select option', option)
      state.crtItem = option
    }

    const change = (val: any) => {
      console.log('change val', val)
      state.options = []
      state.itemKey = undefined
      state.crtItem = {} as IItem
    }

    const addItem = () => {
      // 积分道具
      if (state.type === 0) {
        if (_.value.find((item: any) => item.item_id === 'coin')) return message.info('已添加此道具，直接修改数量')
        triggerChange([..._.value, { num: 1, game_project: 'funplus_zone', item_id: 'coin', internal_id: 'coin', name: '积分', gift_type: state.type }])
      }
      // 游戏道具 / 三方道具 / 平台道具
      if (state.type === 1 || state.type === 2 || state.type === 3) {
        if (!state.crtItem.item_id) return
        if (_.value.find((item: any) => item.item_id === state.crtItem.item_id)) return message.info('已添加此道具，直接修改数量')
        triggerChange([..._.value, { ...state.crtItem, num: 1, gift_type: state.type, game_project: localStorage.getItem('crtGame') }])
      }

      console.log('state.crtItem', state.crtItem)

      state.crtItem = {} as IItem
      state.itemKey = undefined
    }

    return {
      configState,
      ...toRefs(state),
      remoteMethod,
      select,
      change,
      addItem,
      GIFT_ITEM_COLUMNS_MAP
    }
  }
})
</script>

<style lang="scss" scoped>
.t-name {
  width: 170px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &+.t-name {
    color: #999;
  }
}
</style>
