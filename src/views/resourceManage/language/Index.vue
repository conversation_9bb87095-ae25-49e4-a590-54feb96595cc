<template>
  <a-card class="filter" :border="false">
    <a-row justify="space-between">
      <a-col :span="12">
        <a-input-search
          v-model:value="state.searchParams.key"
          placeholder="请输入搜索内容"
          style="width: 300px;"
          enter-button="查询"
          allowClear
          @search="search"
        ></a-input-search>
      </a-col>
      <UploadBtn
        v-has="'Operation'"
        ref="uploadBtn"
        @uploadSuccess="search"
        :downloadApi="downloadLangConf"
        fileType="lang"
        :page="PAGE_CONF.MULTILINGUAL"
      />
    </a-row>
  </a-card>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getTableData"
    :params="state.searchParams"
    :columns="COLUMNS"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'image'">
        <PropIcon :prop-detail="record" :style="{ width: '50px', height: '50px' }" />
      </template>
      <template v-if="column.type === 'lang'">
        <a-tooltip :title="record[column.dataIndex]" placement="topLeft">
          {{ record[column.dataIndex] }}
        </a-tooltip>
      </template>
    </template>
  </CustomTable>

</template>

<script lang="ts" setup>
import { LangGetData, downloadLangConf } from '@/api/resource'
import { ref, reactive } from 'vue'
import { LANG_COLUMNS_MAP } from '../const'
import { PAGE_CONF } from '@/enum';
import { storeToRefs } from 'pinia';
import { useConfigStore } from '@/store';
const state = reactive({
  searchParams: {
    key: ''
  }
})
// 列表数据获取
const getTableData = LangGetData
const RefCustomTable = ref()

const { configState } = storeToRefs(useConfigStore())
const COLUMNS = computed(() => {
  const langsConf = [...configState.value.langs.map(item => ({
    ellipsis: true,
    dataIndex: item.value,
    title: `${item.label}(${item.value})`,
    key: item.value,
    type: 'lang',
    width: '200px'
  })).sort((a, b) => a.dataIndex < b.dataIndex ? 1 : -1)]
  return [...LANG_COLUMNS_MAP, ...langsConf]
})
// 搜索
const search = () => RefCustomTable.value.requestTableData(true)
</script>
<style lang="scss" scoped>
</style>
