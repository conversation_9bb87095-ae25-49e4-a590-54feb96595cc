export const LANG_COLUMNS_MAP = [
  { dataIndex: 'key', key: 'key', title: '多语言key', width: '200px', fixed: 'left' },
  { dataIndex: 'category', key: 'category', title: '模块', width: '120px' }
]

export const GIFT_COLUMNS_MAP = [
  { dataIndex: 'id', key: 'id', title: 'id', width: '60px' },
  { dataIndex: 'gift_id', key: 'gift_id', title: 'gift_id', width: '140px' },
  { dataIndex: 'name', key: 'name', title: '礼包名称(后台)', width: '130px' },
  { dataIndex: 'desc_key', key: 'desc_key', title: '礼包名称(前台)', width: '130px' },
  { dataIndex: 'icon_url', key: 'icon_url', title: 'icon', width: '70px' },
  { dataIndex: 'item_list', key: 'item_list', title: '道具详情', width: '230px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '130px', fixed: 'right', align: 'center' }
]
export const GIFT_ITEM_COLUMNS_MAP = [
  { dataIndex: 'item_id', key: 'item_id', title: '道具', ellipsis: true },
  { dataIndex: 'num', key: 'num', title: '数量', width: '80px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '80px', align: 'center' }
]

export const GMAE_PROPS_COLUMNS_MAP = [
  { dataIndex: 'id', key: 'id', title: '道具ID', width: '60px', fixed: 'left', align: 'center' },
  { dataIndex: 'internal_id', key: 'internal_id', title: 'internal_id', width: '100px' },
  { dataIndex: 'item_id', key: 'item_id', title: 'item_id', width: '230px' },
  { dataIndex: 'image', key: 'image', title: '道具icon', width: '80px' },
  { dataIndex: 'name', key: 'name', title: '中文名', width: '130px' }
]

export const TRIPARTITE_PROPS_COLUMNS_MAP = [
  { dataIndex: 'id', key: 'id', title: 'ID', width: '60px', fixed: 'left', align: 'center' },
  { dataIndex: 'type', key: 'type', title: '道具类型', width: '100px' },
  { dataIndex: 'item_id', key: 'item_id', title: '三方券id', width: '230px' },
  { dataIndex: 'name', key: 'name', title: '道具名称', width: '130px' },
  { dataIndex: 'image', key: 'image', title: '道具icon', width: '80px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '130px', fixed: 'right', align: 'center' }
]

// 平台道具
export const PLATFORM_PROPS_COLUMNS_MAP = [
  {
    dataIndex: 'id',
    key: 'id',
    title: 'ID',
    width: '80px',
    fixed: 'left',
    align: 'center'
  },
  {
    dataIndex: 'item_id',
    key: 'id',
    title: '道具ID',
    width: '150px',
    fixed: 'left',
    align: 'center'
  },
  {
    dataIndex: 'item_type',
    key: 'item_type',
    title: '道具类型',
    width: '120px'
  },
  {
    dataIndex: 'icon',
    key: 'icon',
    title: '道具icon',
    width: '100px'
  },
  {
    dataIndex: 'name_key',
    key: 'name_key',
    title: '道具名称',
    width: '150px',
    ellipsis: true
  },
  {
    dataIndex: 'description_key',
    key: 'description_key',
    title: '道具说明',
    width: '200px',
    ellipsis: true
  },
  {
    dataIndex: 'activation_type',
    key: 'activation_type',
    title: '激活方式',
    width: '120px'
  },
  {
    dataIndex: 'effect_value',
    key: 'effect_value',
    title: '参数设置',
    width: '100px'
  },
  {
    dataIndex: 'activation_period',
    key: 'activation_period',
    title: '有效期(天)',
    width: '120px',
    align: 'right'
  },
  {
    dataIndex: 'effect_period',
    key: 'effect_period',
    title: '使用时效(小时)',
    width: '120px',
    align: 'right'
  },
  {
    dataIndex: 'rank_limit',
    key: 'rank_limit',
    title: '等级限制',
    width: '150px'
  },
  {
    dataIndex: 'action',
    key: 'action',
    title: '操作',
    width: '160px',
    fixed: 'right',
    align: 'center'
  }
]
