<script setup lang="ts">
import Form from './Form.vue';
import { getPropsList, updatePropsStatus } from '@/api/rightEntrance';
import { LangGetData,
  //  downloadLangConf
  } from '@/api/resource';
// import { PAGE_CONF } from '@/enum';
import { Modal } from 'ant-design-vue';

const GMAE_PROPS_COLUMNS_MAP = [
  { dataIndex: 'id', key: 'id', title: 'ID', width: '100px' },
  { dataIndex: 'rights_key', key: 'rights_key', title: '权益key', width: '150px' },
  { dataIndex: 'rights_type', key: 'rights_type', title: '权益分类', width: '100px' },
  { dataIndex: 'rights_name', key: 'rights_name', title: '权益名称', width: '130px' },
  // { dataIndex: 'rights_img', key: 'rights_img', title: '权益icon', width: '80px' },
  { dataIndex: 'rights_desc', key: 'rights_desc', title: '简介', width: '130px' },
  { dataIndex: 'dimension', key: 'dimension', title: '统计维度', width: '130px' },
  { dataIndex: 'ladder_configs', key: 'ladder_configs', title: '梯度配置', width: '130px' },
  { dataIndex: 'collection_method', key: 'collection_method', title: '领取方式', width: '130px' },
  { dataIndex: 'status', key: 'status', title: '状态', width: '130px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '130px', fixed: 'right', align: 'center' }
]

const state = reactive({
  editVisible: false,
  editId: 0,
  searchParams: {
    rights_type: null
  } as any,
  previewOpen: false,
  previewData: {},
  optionsRights: [
    { label: '平台特权', value: 1 },
    { label: '游戏特权', value: 2 },
  ],
  optionsDimension: [
    { label: '账户', value: 0 },
    { label: '角色', value: 1 },
  ],
  optionsCollection: [
    { label: '当前页面领取', value: 1 },
    { label: '跳转页面', value: 2 },
    { label: '无引导', value: 3 },
  ],
    // 权益key列表 1. 日礼包——rights_gift_day 2. 周礼包——rights_gift_week 3. 月礼包——rights_gift_month 4. 等级礼包——rights_gift_level 5. 特惠商城——rights_mall 6. 签到福利——rights_signin 7. 特惠活动——rights_activity
    optionsKeys: [
    { label: '日礼包', value: 'rights_gift_day' },
    { label: '周礼包', value: 'rights_gift_week' },
    { label: '月礼包', value: 'rights_gift_month' },
    { label: '等级礼包', value: 'rights_gift_level' },
    { label: '特惠商城', value: 'rights_mall' },
    { label: '签到福利', value: 'rights_signin' },
    { label: '特惠活动', value: 'rights_activity' },
  ],
    // 按钮文案: 1立即领取,2立即前往,3联系客服,4我知道了,5提交工单; 领取方式为1时设为1不可改，领取方式为2时设为2不可改，领取方式为3时为4不可改
    optionsBtnText: [
    { label: '立即领取', value: 1 },
    { label: '立即前往', value: 2 },
    { label: '联系客服', value: 3 },
    { label: '我知道了', value: 4 },
    { label: '提交工单', value: 5 },
  ],
  optionsGift: [
    { label: '每日礼包', value: 1 },
    { label: '每周礼包', value: 2 },
    { label: '每月礼包', value: 3 },
    { label: '每年礼包', value: 4 },
    { label: '等级礼包', value: 5 },
    { label: '活动礼包', value: 6 },
  ],
  optionsGift1: [
    { label: '日', value: 1 },
    { label: '周', value: 2 },
    { label: '月', value: 3 },
    { label: '年', value: 4 },
    { label: '等级', value: 5 },
    { label: '终身', value: 6 },
  ],
  optionsTwo: [] as any[],
})

const switchLoading = ref<boolean[]>([])

// 列表数据获取
const RefCustomTable = ref()
// 搜索
const search = () => RefCustomTable.value.requestTableData(true)

onMounted(() => {
  langGetDataFunc({page: 1, page_size: 1000})
})

const langGetDataFunc = (params: any) => {
  LangGetData(params)
    .then((res: any) => {
      if(res.data && res.data.length > 0) {
        state.optionsTwo = res.data.map((item: any) => ({
          label: item.zh_cn,
          value: item.key,
        }))
      }
    })
}

// 编辑
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 复制
// const copy = (id: number) => messageConfirm(
//   `确定要复制此条数据并生成一份新数据吗？`,
//   copyProps,
//   { id }
// ).then(() => search())

// 删除
// const batchDel = (id: string, isBatch: boolean) => messageConfirm(
//   `确定要删除${isBatch ? '选中的' : '此条'}数据吗？`,
//   delProps,
//   { petId: id }
// ).then(() => search())

const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true
  record.status = 1 - val
  const content = val !== 1 ? '关闭后前端将不展示该权益，即刻生效，请谨慎操作，是否确认关闭' : '关闭后前端将展示该权益，即刻生效，是否确认开启'

  Modal.confirm({
    title: '提示',
    content,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      switchLoading.value[index] = false
      updatePropsStatus(record.id, val).finally(() => search())
    },
    onCancel: () => {
      switchLoading.value[index] = false
    }
  })
}

</script>

<template>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getPropsList"
    :params="state.searchParams"
    :columns="GMAE_PROPS_COLUMNS_MAP"
  >
    <template #top>
      <a-space direction="vertical">
        <a-space wrap style="gap: 20px;">
          <a-select style="width: 215px;" allowClear v-model:value="state.searchParams.rights_type" :options="state.optionsRights" placeholder="请选择权益类型" />
          <a-button type="primary" @click="search">搜索</a-button>
          <a-button @click="() => { (state.searchParams.rights_type = ''); search() }">重置</a-button>
        </a-space>
        <a-space wrap style="padding: 20px 0;gap: 20px;">
          <!-- 一期隐藏 -->
          <!-- <UploadBtn
            v-has="'Operation'"
            ref="uploadBtn"
            @uploadSuccess="search"
            :downloadApi="downloadLangConf"
            fileType="vip-right-ladder-configs "
            :page="PAGE_CONF.LADDERCONFIGS"
          /> -->
          <!-- <a-button type="primary" @click="showEdit(true)">
            <template #icon><PlusOutlined /></template>
            新增消息
          </a-button> -->
        </a-space>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'rights_type'">
        {{ state.optionsRights.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'rights_name'">
        {{ state.optionsTwo.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <a-image v-if="column.key === 'rights_img'" :src="record.rights_img" :height="60" />
      <template v-if="column.key === 'rights_desc'">
        {{ state.optionsTwo.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'dimension'">
        {{ record[column.key] === 0 ? '账户' : '角色' }}
      </template>
      <template v-if="column.key === 'ladder_configs'">
        {{ record[column.key] && record[column.key].length > 0 ? '已上传' : '未上传' }}
      </template>
      <template v-if="column.key === 'collection_method'">
        {{ record[column.key] === 1 ? '当前页面领取' : record[column.key] === 2 ? '跳转页面' : '无引导'}}
      </template>
      <template v-if="column.key === 'status'">
        <a-switch v-model:checked="record.status" :checkedValue="1" :unCheckedValue="0" checked-children="已上线" un-checked-children="未上线" :loading="switchLoading[index]"
        @click="(val: number) => beforeSwitch(val, record, index)"
        ></a-switch>
      </template>
      <template v-if="column.key === 'action'">
        <a-space >
          <template #split><a-divider type="vertical" style="margin: 0;" /></template>
          <!-- <a-typography-link type="success" @click="copy(record.id)">复制</a-typography-link> -->
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <!-- <a-typography-link type="danger" danger @click="batchDel(record.id, false)">删除</a-typography-link> -->
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Form>
</template>

<style lang="scss" scoped>
.preview-modal {
  position: relative;
  .preview-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(55, 60, 82, 0.12);
  }
  .preview-title, .preview-date {
    font-size: 14px;
    color: #BEBEBE;
  }
  .preview-desc {
    color: #3D3D3D;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    margin: 20px 0;
  }
  .preview-btn {
    color: #1A1B1F;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 0;
    margin-top: 32px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    border-radius: 12px;
    border: 1px solid rgba(55, 60, 82, 0.18);
    cursor: pointer;
  }
}

</style>
