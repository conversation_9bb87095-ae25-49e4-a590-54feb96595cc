<script setup lang="ts">
import { createProps, getPropsDetail, updateProps, uploadConfigs, exportConfigs } from '@/api/rightEntrance';
import { onMounted } from 'vue';
import { LangGetData } from '@/api/resource'
import { downloadTemp } from '@/api/common'
import { UploadChangeParam, UploadFile, UploadProps, message } from 'ant-design-vue';

interface FormState {
  id: number
  rights_key: string
  rights_type: number | string
  rights_name: string
  rights_img: string
  rights_desc: string
  dimension: number | string
  ladder_configs: string
  collection_method: number | string
  button: number | string
  collection_url: string
  status: number | string
  // 默认配置
  name_key: string
  quantity: number
  is_filter: boolean
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getPropsDetail(props.editId)
    .then((res: any) => {
      formState.value = res
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  rights_key: '',
  rights_type: 1,
  rights_name: '',
  rights_img: '',
  rights_desc: '',
  dimension: '',
  ladder_configs: '{}',
  collection_method: '',
  button: '',
  collection_url: '',
  status: 1,
  // 默认配置
  name_key: 'en', // 名称多语言key
  quantity: 0, // 包裹数量
  is_filter: false, // 是否过滤
})

const state = reactive({
  uploadLoading: false,
  fileList: [] as UploadFile[],
  optionsRights: [
    { label: '平台特权', value: 1 },
    { label: '游戏特权', value: 2 },
  ],
  optionsDimension: [
    { label: '账户', value: 0 },
    { label: '角色', value: 1 },
  ],
  optionsCollection: [
    { label: '当前页面领取', value: 1 },
    { label: '跳转页面', value: 2 },
    { label: '无引导', value: 3 },
  ],
  // 权益key列表 1. 日礼包——rights_gift_day 2. 周礼包——rights_gift_week 3. 月礼包——rights_gift_month 4. 等级礼包——rights_gift_level 5. 特惠商城——rights_mall 6. 签到福利——rights_signin 7. 特惠活动——rights_activity
  optionsKeys: [
    { label: 'rights_gift_day', value: 'rights_gift_day' },
    { label: 'rights_gift_week', value: 'rights_gift_week' },
    { label: 'rights_gift_month', value: 'rights_gift_month' },
    { label: 'rights_gift_level', value: 'rights_gift_level' },
    { label: 'rights_mall', value: 'rights_mall' },
    { label: 'rights_signin', value: 'rights_signin' },
    { label: 'rights_activity', value: 'rights_activity' },
  ],
  // 按钮文案: 1立即领取,2立即前往,3联系客服,4我知道了,5提交工单; 领取方式为1时设为1不可改，领取方式为2时设为2不可改，领取方式为3时为4不可改
  optionsBtnText: [
    { label: '立即领取', value: 1 },
    { label: '立即前往', value: 2 },
    { label: '联系客服', value: 3 },
    { label: '我知道了', value: 4 },
    { label: '提交工单', value: 5 },
  ],
  optionsGift: [
    { label: '每日礼包', value: 1 },
    { label: '每周礼包', value: 2 },
    { label: '每月礼包', value: 3 },
    { label: '每年礼包', value: 4 },
    { label: '等级礼包', value: 5 },
    { label: '活动礼包', value: 6 },
  ],
  optionsOne: [
    { label: '系统通知', value: 1 },
  ],
  optionsTwo: [] as any[],
  optionsVip: [
    { label: 'LV1', value: 1 },
    { label: 'LV2', value: 2 },
    { label: 'LV3', value: 3 },
    { label: 'LV4', value: 4 },
    { label: 'LV5', value: 5 },
    { label: 'LV6', value: 6 },
    { label: 'LV7', value: 7 },
    // { label: 'LV8', value: 8 },
    // { label: 'LV9', value: 9 },
    // { label: 'LV10', value: 10 },
  ],
  jumpType: [
    { label: '无跳转', value: 1 },
    { label: '跳转', value: 2 },
  ],
  domains: [
    {
      value: '',
      key: Date.now(),
    },
  ],
  trigger1: [
    { label: '按账号', value: 1 },
    { label: '按角色', value: 2 },
  ],
  trigger2: [
    { label: '每日领取', value: 1 },
    { label: '每周领取', value: 2 },
    { label: '每月领取', value: 3 },
    { label: '每年领取', value: 4 },
    { label: '当前等级领取', value: 5 },
    { label: '终身领取', value: 6 },
  ],
  optionsRightsName: [] as any[],
  optionsRightsDesc: [] as any[],
})

const submitLoading = ref(false)
const formRef = ref();

onMounted(() => {
  langGetDataFunc({page: 1, page_size: 1000})
})

const langGetDataFunc = (params: any) => {
  LangGetData(params)
    .then((res: any) => {
      console.log('langGetDataFunc res', res)
      if(res.data && res.data.length > 0) {
        state.optionsTwo = res.data.map((item: any) => ({
          label: item.zh_cn,
          value: item.key,
        }))
        state.optionsRightsName = [...state.optionsTwo]
        state.optionsRightsDesc = [...state.optionsTwo]
      }
    })
}

const fetchRightsName = (value: string) => {
  // console.log('fetchRightsName', value)
  // 对fetchRightsName中的label进行过滤
  const filterData = state.optionsTwo.filter((item: any) => item.label?.indexOf(value) > -1)
  // console.log('filterData', filterData)
  state.optionsRightsName = filterData
}

const handleChangeRightsName = (value: any) => {
  // console.log('handleChangeRightsName', value)
  if(value === undefined) {
    state.optionsRightsName = state.optionsTwo
  }
}

const fetchRightsDesc = (value: string) => {
  // console.log('fetchRightsName', value)
  // 对fetchRightsName中的label进行过滤
  const filterData = state.optionsTwo.filter((item: any) => item.label?.indexOf(value) > -1)
  // console.log('filterData', filterData)
  state.optionsRightsDesc = filterData
}

const handleChangeRightsDesc = (value: any) => {
  // console.log('handleChangeRightsName', value)
  if(value === undefined) {
    state.optionsRightsDesc = state.optionsTwo
  }
}

const beforeUpload: UploadProps['beforeUpload'] = (file: UploadFile) => {
  state.fileList = [file];
  return false;
};

function fileChange (info: UploadChangeParam) {
  state.fileList = [info.file]
  console.log('state.fileList', state.fileList)
  console.log('state.fileList.length', state.fileList.length)
  if(state.fileList.length > 0) {
    submitUpload()
  }
}

function submitUpload () {
  console.log('submitUpload', 1)
  if (state.fileList.length === 0) {
    return message.error('未选择文件！')
  }
  state.uploadLoading = true

  const formData = new FormData()
  formData.append('file', (state.fileList[0] as any))
  // for (const key in props.uploadData) {
  //   formData.append(key, props.uploadData[key])
  // }
  uploadConfigs(formData)
    .then((res) => {
      console.log('uploadConfigs res', res)
      message.success('上传成功');
      formState.value.ladder_configs = JSON.stringify(res)
    })
    .catch(() => {
      state.uploadLoading = false
    })
}

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      if (props.editId) {
        updateProps(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => { })
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        createProps(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => { })
          .finally(() => {
            submitLoading.value = false
          })
      }
      setTimeout(() => {
        submitLoading.value = false
      }, 1000);
    })
    .catch(() => { })
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑权益' : '新增权益'" :maskClosable="false" :width="800"
    @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form :model="formState" name="basic" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }"
        autocomplete="off">
        <a-form-item label="权益key" name="rights_key" :rules="[{ required: true, message: '请选择权益key' }]">
          <a-select disabled style="width: 100%;" v-model:value="formState.rights_key" :options="state.optionsKeys" />
        </a-form-item>

        <a-form-item label="权益类型" name="rights_type" :rules="[{ required: true, message: '请选择权益类型' }]">
          <a-select style="width: 100%;" v-model:value="formState.rights_type" :options="state.optionsRights" />
        </a-form-item>

        <a-form-item label="权益名称" name="rights_name"
          :rules="[{ required: true, message: '请选择权益名称' }]">
          <a-select style="width: 100%;" v-model:value="formState.rights_name" :options="state.optionsRightsName" allowClear :filter-option="false" showSearch @search="fetchRightsName" @change="handleChangeRightsName"/>
        </a-form-item>
<!--
        <a-form-item label="权益icon" name="rights_img">
          <SelectImg v-model:value="formState.rights_img" :width-height="[200, 200]"></SelectImg>
        </a-form-item> -->

        <a-form-item label="权益简介" name="rights_desc" :rules="[{ required: true, message: '请选择权益简介' }]">
          <a-select style="width: 100%;" v-model:value="formState.rights_desc" :options="state.optionsRightsDesc" allowClear :filter-option="false" showSearch @search="fetchRightsDesc" @change="handleChangeRightsDesc" />
        </a-form-item>

        <a-form-item label="梯度配置" name="gift_value">
          <div class="btns-wrap">
            <a-upload-dragger
              v-model:fileList="state.fileList"
              name="file"
              :multiple="false"
              action="/"
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              @change="fileChange"
              @remove="state.fileList = []"
              :before-upload="beforeUpload"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined></inbox-outlined>
              </p>
              <p class="ant-upload-text">点击或拖拽文件至此区域即可上传</p>
            </a-upload-dragger>
          </div>
          <div style="margin-top: 10px;display: flex;align-items: center;gap: 30px;">
            <a-button type="primary" @click="downloadTemp('vip-right-ladder-configs')">下载模版</a-button>
            <a-button v-if="formState.ladder_configs && formState.ladder_configs.length > 0" type="primary" @click="exportConfigs({id: props.editId})">导出梯度配置</a-button>
          </div>
        </a-form-item>

        <a-form-item label="领取维度" name="dimension" :rules="[{ required: true, message: '请选择权益维度' }]">
          <a-select style="width: 100%;" v-model:value="formState.dimension" :options="state.optionsDimension" />
        </a-form-item>

        <a-form-item label="领取文案及跳转">
          <a-space nowrap class="space-wrapper" style="gap: 5px;">
            <a-select style="width: 100%;" v-model:value="formState.collection_method" :options="state.optionsCollection" />

            <a-select style="width: 100%;" disbaled v-model:value="formState.button" :options="state.optionsBtnText" />

            <a-input v-if="formState.collection_method === 2" style="width: 100%;" v-model:value="formState.collection_url" :placeholder="'请输入页面url'" />
          </a-space>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 10, span: 12 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.dynamic-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  margin: 0 5px;
  font-size: 24px;
  color: #1677ff;
  transition: all 0.3s;
}

.dynamic-button:hover {
  color: #4096ff;
}

.space-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;

  ::v-deep(.ant-space-item) {
    flex: 1;
    &:nth-child(4),
    &:nth-child(5),
    &:nth-child(6){
      flex: 0;
      width: 50px;
    }
  }
}
</style>
