<script setup lang="ts">
import Form from './Form.vue';
import { getPropsList, delProps, updatePropsStatus, copyProps, downloadConf } from '@/api/rightAndInterests';
import { getGiftPkgNumList } from '@/api/resource';
import { PAGE_CONF } from '@/enum';
import { messageConfirm } from '@/utils';
import { Modal } from 'ant-design-vue';

const GMAE_PROPS_COLUMNS_MAP = [
  { dataIndex: 'id', key: 'id', title: 'ID', width: '100px' },
  { dataIndex: 'cycle_type', key: 'cycle_type', title: '礼包类型', width: '100px' },
  { dataIndex: 'gift_packages_id', key: 'gift_packages_id', title: '礼包名称（后台）', width: '130px' },
  { dataIndex: 'vip', key: 'vip', title: 'VIP等级', width: '80px' },
  { dataIndex: 'game_growth', key: 'game_growth', title: '游戏成长值', width: '130px' },
  { dataIndex: 'role_growth', key: 'role_growth', title: '角色成长值', width: '130px' },
  { dataIndex: 'gift_value', key: 'gift_value', title: '礼包积分价值', width: '130px' },
  { dataIndex: 'cycle_shape', key: 'cycle_shape', title: '领取维度', width: '130px' },
  { dataIndex: 'cycle_type', key: 'cycle_type_1', title: '周期', width: '130px' },
  { dataIndex: 'cycle_times', key: 'cycle_times', title: '周期内次数', width: '130px' },
  { dataIndex: 'f_s_ids', key: 'f_s_ids', title: '服务器ID', width: '130px' },
  { dataIndex: 'status', key: 'status', title: '状态', width: '130px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '130px', fixed: 'right', align: 'center' }
]

const state = reactive({
  editVisible: false,
  editId: 0,
  searchParams: {
    cycle_type: null
  } as any,
  previewOpen: false,
  previewData: {},
  optionsGift: [
    { label: '每日礼包', value: 1 },
    { label: '每周礼包', value: 2 },
    { label: '每月礼包', value: 3 },
    { label: '每年礼包', value: 4 },
    { label: '等级礼包', value: 5 },
    { label: '活动礼包', value: 6 },
  ],
  optionsGift1: [
    { label: '日', value: 1 },
    { label: '周', value: 2 },
    { label: '月', value: 3 },
    { label: '年', value: 4 },
    { label: '等级', value: 5 },
    { label: '终身', value: 6 },
  ],
  optionsTwo: [] as any[],
})

const switchLoading = ref<boolean[]>([])

// 列表数据获取
const RefCustomTable = ref()
// 搜索
const search = (noUpdate?: boolean) => RefCustomTable.value.requestTableData(!noUpdate)

onMounted(() => {
  getGiftPkgListFunc()
})

const getGiftPkgListFunc = () => {
  getGiftPkgNumList()
    .then((res: any) => {
      console.log('getGiftPkgNumList res', res)
      if (res && res.length > 0) {
        state.optionsTwo = res
      }
    })
}

// 编辑
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 复制
const copy = (id: number) => messageConfirm(
  `确定要复制此条数据并生成一份新数据吗？`,
  copyProps,
  { id }
).then(() => search(true))

// 删除
const batchDel = (id: string, isBatch: boolean) => messageConfirm(
  `确定要删除${isBatch ? '选中的' : '此条'}数据吗？`,
  delProps,
  { petId: id }
).then(() => search())

// 预览
const showPreview = (record: any) => {
  state.previewOpen = true
  console.log('record', record)
  // 填充数据
  state.previewData = record
}

const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true
  record.status = 1 - val

  Modal.confirm({
    title: '提示',
    content: '确定要切换此条数据状态吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      switchLoading.value[index] = false
      updatePropsStatus(record.id, val).finally(() => search(true))
    },
    onCancel: () => {
      switchLoading.value[index] = false
    }
  })
}

</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getPropsList" :params="state.searchParams"
    :columns="GMAE_PROPS_COLUMNS_MAP">
    <template #top>
      <a-space direction="vertical">
        <a-space wrap style="gap: 20px;">
          <a-select style="width: 215px;" allowClear v-model:value="state.searchParams.cycle_type"
            :options="state.optionsGift" placeholder="请选择礼包类型" />
          <a-button type="primary" @click="search">搜索</a-button>
          <a-button @click="() => { (state.searchParams.cycle_type = ''); search() }">重置</a-button>
        </a-space>
        <a-space wrap style="padding: 20px 0;gap: 20px;">
          <UploadBtn v-has="'Operation'" ref="uploadBtn" @uploadSuccess="search" :downloadApi="downloadConf"
            fileType="member-gifts" :page="PAGE_CONF.RIGHTGIFT" />
          <a-button type="primary" @click="showEdit(true)">
            <template #icon>
              <PlusOutlined />
            </template>
            新增礼包
          </a-button>
        </a-space>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'gift_packages_id'">
        {{ state.optionsTwo.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'cycle_type'">
        {{ state.optionsGift.find(item => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'cycle_type_1'">
        {{ state.optionsGift1.find(item => item.value === record['cycle_type'])?.label }}
      </template>
      <template v-if="column.key === 'cycle_shape'">
        {{ record[column.key] === 1 ? '账户' : '角色' }}
      </template>
      <template v-if="column.key === 'status'">
        <a-switch v-model:checked="record.status" :checkedValue="1" :unCheckedValue="0" checked-children="已上线"
          un-checked-children="未上线" :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"></a-switch>
      </template>
      <template v-if="column.key === 'preview'">
        <a-typography-link @click="showPreview(record)">预览</a-typography-link>
      </template>
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0;" /></template>
          <a-typography-link type="success" @click="copy(record.id)">复制</a-typography-link>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" danger @click="batchDel(record.id, false)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Form>
</template>

<style lang="scss" scoped>
.preview-modal {
  position: relative;

  .preview-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(55, 60, 82, 0.12);
  }

  .preview-title,
  .preview-date {
    font-size: 14px;
    color: #BEBEBE;
  }

  .preview-desc {
    color: #3D3D3D;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    margin: 20px 0;
  }

  .preview-btn {
    color: #1A1B1F;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 0;
    margin-top: 32px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    border-radius: 12px;
    border: 1px solid rgba(55, 60, 82, 0.18);
    cursor: pointer;
  }
}
</style>
