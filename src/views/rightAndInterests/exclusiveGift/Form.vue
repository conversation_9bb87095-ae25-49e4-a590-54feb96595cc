<script setup lang="ts">
import { createProps, getPropsDetail, updateProps } from '@/api/rightAndInterests';
import { getGiftPkgNumList } from '@/api/resource';
import { onMounted } from 'vue';

interface FormState {
  id: number
  cycle_type: number | string
  gift_packages_id: null | number | string
  vip: number | string
  game_growth: number
  role_growth: number
  cycle_shape: number | string
  cycle_times: number
  gift_value: number
  f_s_ids: string
  // 默认配置
  name_key: string
  quantity: number
  is_filter: boolean
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getPropsDetail(props.editId)
    .then((res: any) => {
      formState.value = res
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  gift_packages_id: null,
  vip: 1,
  game_growth: 0,
  role_growth: 0,
  cycle_shape: 1,
  cycle_type: 1,
  cycle_times: 0,
  gift_value: 0,
  f_s_ids: '',
  // 默认配置
  name_key: 'en', // 名称多语言key
  quantity: 0, // 包裹数量
  is_filter: false, // 是否过滤
})

const state = reactive({
  uploadLoading: false,
  optionsGift: [
    { label: '每日礼包', value: 1 },
    { label: '每周礼包', value: 2 },
    { label: '每月礼包', value: 3 },
    { label: '每年礼包', value: 4 },
    { label: '等级礼包', value: 5 },
    { label: '活动礼包', value: 6 },
  ],
  optionsOne: [
    { label: '系统通知', value: 1 },
  ],
  optionsTwo: [] as any[],
  optionsVip: [
    { label: 'LV1', value: 1 },
    { label: 'LV2', value: 2 },
    { label: 'LV3', value: 3 },
    { label: 'LV4', value: 4 },
    { label: 'LV5', value: 5 },
    { label: 'LV6', value: 6 },
    { label: 'LV7', value: 7 },
    // { label: 'LV8', value: 8 },
    // { label: 'LV9', value: 9 },
    // { label: 'LV10', value: 10 },
  ],
  jumpType: [
    { label: '无跳转', value: 1 },
    { label: '跳转', value: 2 },
  ],
  domains: [
    {
      value: '',
      key: Date.now(),
    },
  ],
  trigger1: [
    { label: '按账号', value: 1 },
    { label: '按角色', value: 2 },
  ],
  trigger2: [
    { label: '每日领取', value: 1 },
    { label: '每周领取', value: 2 },
    { label: '每月领取', value: 3 },
    { label: '每年领取', value: 4 },
    { label: '当前等级领取', value: 5 },
    { label: '终身领取', value: 6 },
  ],
})

const submitLoading = ref(false)
const formRef = ref();

onMounted(() => {
  getGiftPkgListFunc()
})

const getGiftPkgListFunc = () => {
  getGiftPkgNumList()
    .then((res: any) => {
      console.log('getGiftPkgNumList res', res)
      if(res && res.length > 0) {
        state.optionsTwo = res
      }
    })
}

const handleGiftPackage = (value: any) => {
  const item = state.optionsTwo.find((item: any) => item.value === value)
  console.log('handleGiftPackage item', item)
  if (item) {
    formState.value.gift_value = item.gift_value
  }
}

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      if (props.editId) {
        updateProps(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => { })
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        createProps(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => { })
          .finally(() => {
            submitLoading.value = false
          })
      }
      setTimeout(() => {
        submitLoading.value = false
      }, 1000);
    })
    .catch(() => { })
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑礼包' : '新增礼包'" :maskClosable="false" :width="800"
    @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form :model="formState" name="basic" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }"
        autocomplete="off">
        <a-form-item label="礼包类型" name="cycle_type" :rules="[{ required: true, message: '请选择礼包类型' }]">
          <a-select style="width: 100%;" v-model:value="formState.cycle_type" :options="state.optionsGift" />
        </a-form-item>

        <a-form-item label="礼包名称（后台）" name="gift_packages_id"
          :rules="[{ required: true, message: '请选择礼包名称' }]">
          <a-select style="width: 100%;" v-model:value="formState.gift_packages_id" :options="state.optionsTwo" @change="handleGiftPackage" />
        </a-form-item>

        <a-form-item label="VIP等级" name="vip" :rules="[{ required: true, message: '请选择VIP等级' }]">
          <a-select style="width: 100%;" v-model:value="formState.vip" :options="state.optionsVip" />
        </a-form-item>

        <a-form-item label="游戏成长值" name="game_growth">
          <a-input-number style="width: 100%;" v-model:value="formState.game_growth" :min="0" :max="1500000" placeholder="请输入游戏成长值" />
        </a-form-item>


        <a-form-item label="角色成长值" name="role_growth">
          <a-input-number style="width: 100%;" v-model:value="formState.role_growth" :min="0" :max="1500000" placeholder="请输入角色成长值"></a-input-number>
        </a-form-item>

        <a-form-item label="领取维度及周期" name="cycle_times" :rules="[{ required: true, message: '请填写领取维度及周期' }]">
          <a-space nowrap class="space-wrapper" style="gap: 5px;">
            <a-select style="width: 100%;" v-model:value="formState.cycle_shape" :options="state.trigger1" />

            <a-select style="width: 100%;" disabled v-model:value="formState.cycle_type" :options="state.trigger2" />

            <a-input-number style="width: 100%;" v-model:value="formState.cycle_times" :min="0" :max="100000" placeholder="请填写次数" />次
          </a-space>
        </a-form-item>

        <a-form-item label="礼包价值" name="gift_value">
          <a-input style="width: 100px;margin-right: 10px;" readonly v-model:value="formState.gift_value" />积分（发生退款时扣除对应积分）
        </a-form-item>

        <a-form-item label="服务器" name="f_s_ids">
          <a-textarea v-model:value="formState.f_s_ids" placeholder="请输入服务器ID，例如1,2-4,10,20-30" allow-clear />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 10, span: 12 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.dynamic-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  margin: 0 5px;
  font-size: 24px;
  color: #1677ff;
  transition: all 0.3s;
}

.dynamic-button:hover {
  color: #4096ff;
}

.space-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;

  ::v-deep(.ant-space-item) {
    flex: 1;
    &:nth-child(4),
    &:nth-child(5),
    &:nth-child(6){
      flex: 0;
      width: 50px;
    }
  }
}
</style>
