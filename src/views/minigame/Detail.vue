<script setup lang="ts">
import { createMNG, getMNGDetail, updateMNG } from '@/api/minigame';
import { setDefaultFilter } from '@/utils';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

interface FormState {
  id: number
  name_key: string | undefined
  link_url: string
  img_id: string
  is_forever: 0 | 1
  times: number[] | undefined

  is_filter: 0 | 1
  f_os: string | undefined
  f_channel: string | undefined
  lang: string | undefined
  f_s_ids: string
  f_lv_ids: string
  [key: string]: any
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getMNGDetail(props.editId)
    .then((res: any) => {
      formState.value = res
      if (res.online_at && res.offline_at) {
        formState.value.is_forever = 0
        formState.value.times = [res.online_at, res.offline_at]
      } else {
        formState.value.is_forever = 1
        formState.value.times = undefined
      }
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  name_key: undefined,
  link_url: '',
  img_id: '',
  is_forever: 0,
  times: undefined,
  is_filter: 0,
  f_os: '',
  f_channel: '',
  lang: '',
  f_s_ids: '',
  f_lv_ids: ''
})

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      if (data.is_forever === 0 && data.times) {
        data.online_at = data.times[0]
        data.offline_at = data.times[1]
      } else {
        data.online_at = 0
        data.offline_at = ''
      }
      if (props.editId) {
        // 编辑
        updateMNG(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        // 新增
        createMNG(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
    .catch(() => {})
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑' : '新增'" :maskClosable="false" :width="600" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="名称" name="name_key" :rules="[{ required: true, message: '请选择多语言' }]">
          <SelectLang v-model:value="formState.name_key" />
        </a-form-item>
        <a-form-item label="跳转URL" name="link_url" :rules="[{ required: true, message: '请输入跳转URL' }]">
          <a-input v-model:value="formState.link_url" placeholder="请输入跳转URL"></a-input>
        </a-form-item>
        <a-form-item label="封面" name="img_id" :rules="[{ required: true, message: '请上传封面' }]">
          <SelectImg v-model:value="formState.img_id" :width-height="[200, 200]"></SelectImg>
        </a-form-item>

        <a-form-item label="活动时间" @change="formState.times = undefined">
          <a-radio-group v-model:value="formState.is_forever" :options="[{ label: '永久', value: 1 }, { label: '定时', value: 0 }]"/>
        </a-form-item>
        <template v-if="formState.is_forever === 0">
          <a-form-item :wrapper-col="{ offset: 6, span: 16 }" name="times" :rules="[{ required: true, message: '请选择起止时间' }]">
            <SelectDateTime v-model:value="formState.times"></SelectDateTime>
          </a-form-item>
        </template>

        <a-form-item label="筛选器" name="is_filter" :rules="[{ required: true, message: '请选择筛选器' }]">
          <a-radio-group v-model:value="formState.is_filter" @change="setDefaultFilter(formState, ['f_os', 'f_channel', 'lang', 'f_s_ids', 'f_lv_ids'])">
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
        <template v-if="formState.is_filter === 1">
          <a-form-item label="操作系统" name="f_os" :rules="[{ required: true, message: '请选择操作系统' }]">
            <SelectWithAllComp v-model:value="formState.f_os" placeholder="请选择操作系统" type="platform"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="语种" name="lang" :rules="[{ required: true, message: '请选择语种' }]">
            <SelectWithAllComp v-model:value="formState.lang" placeholder="请选择语种" type="langs"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="渠道" name="f_channel" :rules="[{ required: true, message: '请选择渠道' }]">
            <SelectWithAllComp v-model:value="formState.f_channel" placeholder="请选择渠道" type="channels"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="服务器" name="f_s_ids" :rules="[{ required: true, message: '请输入服务器ID' }]">
            <a-textarea v-model:value="formState.f_s_ids" placeholder="请输入服务器ID，例如1,2-4,10,20-30" allow-clear />
          </a-form-item>
          <a-form-item label="城堡等级" name="f_lv_ids" :rules="[{ required: true, message: '请输入城堡等级' }]">
            <a-textarea v-model:value="formState.f_lv_ids" placeholder="请输入城堡等级，例如1,7-9999" allow-clear />
          </a-form-item>
        </template>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>

</style>
