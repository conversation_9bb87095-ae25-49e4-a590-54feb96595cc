<script setup lang="ts">
import { MNG_COLUMN_MAP, MNG_STATUS_MAP } from './const';
import Detail from './Detail.vue';
import { messageConfirm } from '@/utils';
import { delMNG, getMNGList, switchMNGStatus } from '@/api/minigame';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
import { Modal } from 'ant-design-vue';

dayjs.extend(utc)

const state = reactive({
  editVisible: false,
  editId: 0,
})

// 列表数据获取
const RefCustomTable = ref()
const search = () => RefCustomTable.value.requestTableData(true)

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 删除
const batchDel = (id: string, isBatch: boolean) => messageConfirm(
  `确定要删除${isBatch ? '选中的' : '此条'}数据吗？`,
  delMNG,
  { id }
).then(() => search())

// 状态切换
const switchLoading = ref<boolean[]>([])
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true
  record.is_online = 1 - val
  Modal.confirm({
    title: '提示',
    content: '确定要切换此条数据状态吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      switchLoading.value[index] = false
      switchMNGStatus({ id: record.id, is_online: val }).finally(() => {
        search()
      })
    },
    onCancel: () => {
      switchLoading.value[index] = false
    }
  })
}
</script>

<template>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getMNGList"
    :params="{}"
    :columns="MNG_COLUMN_MAP"
  >
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'img_id'">
        <a-image :src="record.img_id" :height="60" />
      </template>
      <template v-if="column.key === 'times'">
        {{ record.offline_at ? `${dayjs.utc(record.online_at * 1000).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs.utc(record.offline_at * 1000).format('YYYY-MM-DD HH:mm:ss')}` : '永久' }}
      </template>
      <template v-if="column.key === 'name_key'">
        <LangKey :lang-key="record.name_key" :i18n_name="record.i18n_name"></LangKey>
      </template>
      <FilterCell v-if="column.key === 'filter'" :record="record" />
      <template v-if="column.key === 'status'">
        <a-tag :color="MNG_STATUS_MAP[record.status].type">{{ MNG_STATUS_MAP[record.status].label }}</a-tag>
      </template>
      <template v-if="column.key === 'is_online'">
        <a-switch v-model:checked="record.is_online" :checkedValue="1" :unCheckedValue="0" checked-children="开启" un-checked-children="关闭" :loading="switchLoading[index]" @click="(val: number) => beforeSwitch(val, record, index)"></a-switch>
      </template>
      <template v-if="column.key === 'action'">
        <a-space :size="0">
          <a-button type="link" @click="showEdit(true, record.id)">编辑</a-button>
          <a-button type="link" danger @click="batchDel(record.id, false)">删除</a-button>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Detail v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Detail>
</template>

<style lang="scss" scoped>

</style>
