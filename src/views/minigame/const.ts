export const MNG_COLUMN_MAP = [
  { dataIndex: 'id', key: 'id', title: 'id', width: '70px' },
  { dataIndex: 'name_key', key: 'name_key', title: '小游戏名称', width: '150px' },
  { dataIndex: 'img_id', key: 'img_id', title: '游戏封面', width: '100px' },
  { dataIndex: 'status', key: 'status', title: '活动状态', width: '80px' },
  { dataIndex: 'times', key: 'times', title: '活动时间', width: '160px' },
  { dataIndex: 'link_url', key: 'link_url', title: '跳转链接', width: '200px' },
  { dataIndex: 'filter', key: 'filter', title: '筛选器', width: '60px' },
  { dataIndex: 'updated_by', key: 'updated_by', title: '更新人', width: '140px' },
  { dataIndex: 'is_online', key: 'is_online', title: '上线状态', width: '140px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '120px', fixed: 'right', align: 'center' }
]

export const MNG_STATUS_MAP: Record<string, { type: string, label: string }> = {
  1: {
    type: 'success',
    label: '进行中'
  },
  2: {
    type: 'default',
    label: '未开始'
  },
  3: {
    type: 'error',
    label: '已结束'
  }
}
