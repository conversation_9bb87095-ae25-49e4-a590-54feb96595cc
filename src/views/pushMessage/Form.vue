<script setup lang="ts">
import { createProps, getPropsDetail, updateProps, uploadConfigs, exportConfigs } from '@/api/pushMessage';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
import { downloadTemp } from '@/api/common'
import { UploadChangeParam, UploadFile, UploadProps, message, Modal } from 'ant-design-vue';
dayjs.extend(utc)

interface FormState {
  id: number
  category: number
  category_sub: number
  push_type: number
  push_at: number | string
  push_time: string
  link_target: number
  link_url: string
  message_templates: any
  is_show_red_dot: number
  system_notice_key: string
  msg2: string
  push_range_type: number
  push_user_range: string
  validity_type: number
  expire_time: number
  expire_time_str: string
  game_project: string
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])
const crtGame = localStorage.getItem('crtGame') || ''
const FUNZONE = 'funplus_zone'

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getPropsDetail(props.editId)
    .then((res: any) => {
      console.log('pushMessage getPropsDetail res', res)

      formState.value = res
      formState.value.push_time = dayjs.utc(res.push_at * 1000).format('YYYY-MM-DD HH:mm:ss')
      formState.value.validity_type = res.validity_type || 1
      formState.value.expire_time = res.expire_time || 0
      formState.value.expire_time_str = res.expire_time ? dayjs.utc(res.expire_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''

      if(crtGame !== FUNZONE && formState.value.game_project === FUNZONE) {
        Modal.confirm({
          title: '提示',
          content: '当前空间下不支持修改平台消息',
          okText: '确定',
          cancelText: '',
          cancelButtonProps: {
            ghost: true,
          },
          onOk: () => {
          }
        })
      }
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  category: 1,
  category_sub: 1,
  push_type: 2,
  push_at: 0,
  push_time: '',
  link_target: 1,
  link_url: '',
  message_templates: '',
  is_show_red_dot: 1, // 是否消息红点,0不显示红点创建消息时设置成已读, 1显示红点
  system_notice_key: '',
  msg2: '',
  push_range_type: 1,
  push_user_range: '',
  validity_type: 1, // 1永久推送 2定时结束
  expire_time: 0,
  expire_time_str: '',
  game_project: '',
})

const state = reactive({
  uploadLoading: false,
  fileList: [] as UploadFile[],
  optionsOne: [
    { label: '系统通知', value: 1 },
  ],
  optionsTwo: [
    { label: '角色激活提醒', value: 1 },
    { label: '每日签到提醒', value: 2 },
    { label: '每日补签提醒', value: 3 },
    { label: '权益变更提醒', value: 4 },
  ],
  optionsType: [
    { label: '自动推送', value: 1 },
    { label: '定时推送', value: 2 },
    { label: '系统推送', value: 3 },
  ],
  jumpType: [
    { label: '无跳转', value: 1 },
    { label: '跳转', value: 2 },
  ],
  domains: [
    {
      value: '',
      key: Date.now(),
    },
  ],
  trigger1: [
  { label: '每天', value: 1 },
  { label: '每周', value: 2 },
  { label: '每月', value: 3 },
  { label: '每年', value: 4 },
  ],
  trigger2: [
    { label: '发布动态', value: 1 },
    { label: '浏览动态', value: 2 },
  ],
  trigger3: [
    { label: '大于', value: 1 },
    { label: '等于', value: 2 },
    { label: '小于', value: 3 },
  ],
})

const submitLoading = ref(false)
const formRef = ref();

const disabledDate = (current: any) => {
  // Can not select days before today and today
  return current && current < dayjs().startOf('day')
}

const removeDomain = (item: any) => {
  const index = state.domains.indexOf(item);
  if (index !== -1) {
    state.domains.splice(index, 1);
  }
};
const addDomain = () => {
  state.domains.push({
    value: '',
    key: Date.now(),
  });
};

const beforeUpload: UploadProps['beforeUpload'] = (file: UploadFile) => {
  state.fileList = [file];
  return false;
};

function fileChange (info: UploadChangeParam) {
  state.fileList = [info.file]
  console.log('state.fileList', state.fileList)
  console.log('state.fileList.length', state.fileList.length)
  if(state.fileList.length > 0) {
    submitUpload()
  }
}

function submitUpload () {
  if (state.fileList.length === 0) {
    return message.error('未选择文件！')
  }
  state.uploadLoading = true

  const formData = new FormData()
  formData.append('file', (state.fileList[0] as any))
  // for (const key in props.uploadData) {
  //   formData.append(key, props.uploadData[key])
  // }
  uploadConfigs(formData)
    .then((res) => {
      console.log('pushMessage uploadConfigs res', res)
      formState.value.message_templates = JSON.stringify(res)
      message.success('上传成功');
      // 清除form表单中message_templates的校验错误
      formRef.value.clearValidate(['message_templates'])
    })
    .catch(() => {
      state.uploadLoading = false
    })
}

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      data.push_at = dayjs.utc(data.push_time).unix()
      data.expire_time = data.expire_time_str ? dayjs.utc(data.expire_time_str).unix() : 0
      if (props.editId) {
        updateProps(id, data)
          .then(() => {
            emits('close')
            emits('refresh', true)
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        createProps(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      }
      setTimeout(() => {
        submitLoading.value = false
      }, 1000);
    })
    .catch(() => {})
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑消息' : '新增消息'" :maskClosable="false" :width="800" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="推送类型" name="push_type" :rules="[{ required: true, message: '请选择推送类型' }]">
          <a-select disabled v-model:value="formState.push_type" :options="state.optionsType" />
        </a-form-item>

        <!-- 选择推送类型为主动推送时展示 -->
        <a-form-item v-if="formState.push_type === 2" label="推送时间" name="push_time" :rules="[{ required: true, message: '请选择推送时间' }]">
          <a-date-picker style="width: 100%" v-model:value="formState.push_time" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"  :show-now="false" show-time />
        </a-form-item>

        <!-- 选择推送类型为自动推送时展示 -->
        <!-- 触发条件-动态增减表单项 -->
        <a-form-item v-if="formState.push_type === 4" label="触发条件">
          <a-form-item
            v-for="(domain, index) in state.domains"
            :key="domain.key"
            :name="['触发条件', index, 'value']"
            :rules="{
              required: true,
              message: '触发条件不能为空',
              trigger: 'change',
            }"
          >
            <a-space nowrap class="space-wrapper" style="gap: 5px;">
              <a-select v-model:value="formState.msg2" :options="state.trigger1" />

              <a-select v-model:value="formState.msg2" :options="state.trigger2" />

              <a-select v-model:value="formState.msg2" :options="state.trigger3" />

              <a-input-number
                v-model:value="domain.value"
                :min="0"
                :max="100"
                placeholder="请填写触发条件"
              />

              <PlusCircleOutlined
                class="dynamic-button dynamic-add-button"
                @click="addDomain"
              />
              <MinusCircleOutlined
                v-if="state.domains.length > 1"
                class="dynamic-button dynamic-delete-button"
                @click="removeDomain(domain)"
              />
            </a-space>
          </a-form-item>
        </a-form-item>

        <a-form-item label="跳转类型" name="link_target" :rules="[{ required: true, message: '请选择跳转类型' }]">
          <a-select v-model:value="formState.link_target" :options="state.jumpType" />
        </a-form-item>

        <a-form-item class="link-target" v-if="formState.link_target === 2" label="跳转链接" name="link_url" :rules="[{ required: true, message: '请输入跳转链接' }]">
          <div class="link-tips">
            <a-popover title="Tips">
              <template #content>
                <p style="color: gray;">如需跳转社区文章详情，则参考以下链接配置规则：</p>
                <p>/communityDetail/?community-app=article/文章id</p>
                <p>例如：</p>
                <p>/communityDetail/articleDetail?community-app=article/1385</p>
              </template>
              <QuestionCircleOutlined />
            </a-popover>
          </div>
          <a-input style="width: 100%;" v-model:value="formState.link_url" placeholder="请输入跳转链接" />
        </a-form-item>

        <a-form-item label="消息文案" name="message_templates"
        :rules="[{ required: true, message: '请上传消息文案' }]"
        >
          <div class="btns-wrap">
            <a-upload-dragger
              v-model:fileList="state.fileList"
              name="file"
              :multiple="false"
              action="/"
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              @change="fileChange"
              @remove="state.fileList = []"
              :before-upload="beforeUpload"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined></inbox-outlined>
              </p>
              <p class="ant-upload-text">点击或拖拽文件至此区域即可上传</p>
            </a-upload-dragger>
          </div>
          <div style="margin-top: 10px;display: flex;align-items: center;gap: 30px;">
            <a-button type="primary" @click="downloadTemp('message-template')">下载模版</a-button>
            <a-button v-if="formState.message_templates && formState.message_templates.length > 0" type="primary" @click="exportConfigs(formState.system_notice_key)">导出消息配置</a-button>
          </div>
        </a-form-item>

        <a-form-item label="推送用户群" :rules="[{ required: true, message: '请选择推送用户群' }]">
          <a-radio-group v-model:value="formState.push_range_type" :options="[{ label: '全体', value: 1 }, { label: '指定用户', value: 2 }]"/>
        </a-form-item>
        <template v-if="formState.push_range_type === 2">
          <a-form-item class="stocks-item" :wrapper-col="{ offset: 6, span: 16 }" name="push_user_range" :rules="[{ required: true, message: '请填写funplus_id' }]">
          <a-textarea width="100%" v-model:value="formState.push_user_range" placeholder="请输入funplus_id，多个id用英文逗号分隔" allow-clear />
          </a-form-item>
        </template>

        <a-form-item v-if="formState.push_type === 2" label="推送有效期" :rules="[{ required: true, message: '请选择推送有效期' }]">
          <a-radio-group v-model:value="formState.validity_type" :options="[{ label: '永久推送', value: 1 }, { label: '定时结束', value: 2 }]"/>
        </a-form-item>
        <template v-if="formState.validity_type === 2">
          <a-form-item class="stocks-item" :wrapper-col="{ offset: 6, span: 16 }" name="expire_time_str" :rules="[{ required: true, message: '请选择结束时间' }]">
            <a-date-picker style="width: 100%" v-model:value="formState.expire_time_str" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate"  :show-now="false" show-time />
          </a-form-item>
        </template>

        <a-form-item label="是否需要红点" name="is_show_red_dot">
          <a-radio-group v-model:value="formState.is_show_red_dot">
            <a-radio :value="1">需要</a-radio>
            <a-radio :value="0">不需要</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="(crtGame === FUNZONE && formState.game_project === FUNZONE) || crtGame !== FUNZONE && formState.game_project !== FUNZONE" :wrapper-col="{ offset: 10, span: 12 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.dynamic-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  margin: 0 5px;
  font-size: 24px;
  color: #1677ff;
  transition: all 0.3s;
}

.dynamic-button:hover {
  color: #4096ff;
}

.space-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;
  ::v-deep(.ant-space-item){
    flex: 1;
    &:nth-child(5), &:nth-child(6) {
      flex: 0;
      width: 50px;
    }
  }
}

.link-target {
  position: relative;
  .link-tips {
    position: absolute;
    left: -15px;
    top: -10px;
    z-index: 10;
  }
}
</style>
