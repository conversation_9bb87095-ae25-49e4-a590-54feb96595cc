<script setup lang="ts">
import Form from './Form.vue';
import { getPropsList, delProps, publish } from '@/api/pushMessage';
import { messageConfirm } from '@/utils';
import { downloadTemp } from '@/api/common'
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

const GMAE_PROPS_COLUMNS_MAP = [
  { dataIndex: 'id', key: 'id', title: 'ID', width: '60px', fixed: 'left', align: 'center' },
  { dataIndex: 'push_type', key: 'push_type', title: '推送类型', width: '100px' },
  { dataIndex: 'link_target', key: 'link_target', title: '消息类型', width: '100px' },
  // { dataIndex: 'category', key: 'category', title: '一级分类', width: '130px' },
  // { dataIndex: 'category_sub', key: 'category_sub', title: '二级分类', width: '130px' },
  // { dataIndex: 'event', key: 'event', title: '触发事件', width: '130px' },
  { dataIndex: 'preview', key: 'preview', title: '内容预览', width: '130px' },
  { dataIndex: 'updated_by', key: 'updated_by', title: '更新人', width: '130px' },
  { dataIndex: 'status', key: 'status', title: '状态', width: '130px' },
  { dataIndex: 'game_project', key: 'game_project', title: '类型', width: '130px' },
  { dataIndex: 'push_at', key: 'push_at', title: '推送时间', width: '200px' },
  { dataIndex: 'is_expire', key: 'is_expire', title: '过期状态', width: '130px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '130px', fixed: 'right', align: 'center' }
]

const state = reactive({
  editVisible: false,
  editId: 0,
  searchParams: {
    push_type: null
  } as any,
  previewOpen: false,
  previewData: {} as any,
  optionsGift: [
    { label: '自动推送', value: 1 },
    { label: '定时推送', value: 2 },
    { label: '系统推送', value: 3 },
  ],
  // 一级分类：1系统通知
  optionsOne: [
    { label: '系统通知', value: 1 },
  ],
  // 二级分类:1角色激活提醒,2每日签到提醒,3每日补签提醒,4权益变更提醒
  optionsTwo: [
    { label: '角色激活提醒', value: 1 },
    { label: '每日签到提醒', value: 2 },
    { label: '每日补签提醒', value: 3 },
    { label: '权益变更提醒', value: 4 },
  ],
  // 推送类型:1自动推送,2定时推送,3系统推送(系统默认不允许删除)
  optionsPushType: [
    { label: '自动推送', value: 1 },
    { label: '定时推送', value: 2 },
    { label: '系统推送', value: 3 },
  ],
  // 跳转类型: 1无需跳转, 2跳转链接	对应列表字段为消息推送：1消息 2文章
  optionsJumpType: [
    { label: '无跳转', value: 1 },
    { label: '有跳转', value: 2 },
  ],
})

const crtGame = localStorage.getItem('crtGame') || ''
const FUNZONE = 'funplus_zone'

// 列表数据获取
const RefCustomTable = ref()
// 搜索
const search = (noUpdate?: boolean) => RefCustomTable.value.requestTableData(!noUpdate)

// 发布
const handlePublish = (id: number) => messageConfirm(
  '确定发布并推送当前信息吗？',
    publish,
    id
  ).then(() => search(true))

// 编辑
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 删除
const batchDel = (id: string, isBatch: boolean) => messageConfirm(
  `确定要删除${isBatch ? '选中的' : '此条'}数据吗？`,
  delProps,
  { petId: id }
).then(() => search())

// 预览
const showPreview = (record: any) => {
  state.previewOpen = true
  console.log('record', record)
  // 填充数据
  state.previewData = record
}

const download = () => {
  downloadTemp('message-template')
}
</script>

<template>
  <a-row justify="space-between">
    <a-col :span="12">

    </a-col>
  </a-row>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getPropsList"
    :params="state.searchParams"
    :columns="GMAE_PROPS_COLUMNS_MAP"
  >
    <template #top>
      <a-space direction="vertical">
        <a-space wrap style="padding: 0 30px;gap: 20px;">
          <a-select style="width: 215px;" allowClear v-model:value="state.searchParams.push_type" :options="state.optionsGift" placeholder="请选择推送类型" />
          <a-button type="primary" @click="search">搜索</a-button>
          <a-button @click="() => { (state.searchParams.push_type = ''); search() }">重置</a-button>
        </a-space>
        <a-space wrap style="padding: 20px 30px;gap: 20px;">
          <a-button type="primary" @click="download()">
            <template #icon><PlusOutlined /></template>
            下载模版
          </a-button>
          <a-button type="primary" @click="showEdit(true)">
            <template #icon><PlusOutlined /></template>
            新增消息
          </a-button>
        </a-space>
      </a-space>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'push_type'">
        {{ state.optionsPushType.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'link_target'">
        {{ state.optionsJumpType.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'category'">
        {{ state.optionsOne.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'category_sub'">
        {{ state.optionsTwo.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'preview'">
        <a-typography-link @click="showPreview(record)">预览</a-typography-link>
      </template>
      <template v-if="column.key === 'status'">
        {{ record[column.key] === 1 ? '已发布' : '未发布' }}
      </template>
      <template v-if="column.key === 'game_project'">
        {{ record[column.key] === "funplus_zone" ? '平台' : '游戏' }}
      </template>
      <template v-if="column.key === 'push_at'">
        {{ record.expire_time ? `${dayjs.utc(record.push_at * 1000).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs.utc(record.expire_time * 1000).format('YYYY-MM-DD HH:mm:ss')}` : '永久' }}
      </template>
      <template v-if="column.key === 'is_expire'">
        {{ record['push_type'] === 2 ? (record[column.key] ? '已过期' : '生效中') : '-' }}
      </template>
      <template v-if="column.key === 'action'">
        <a-space >
          <template #split><a-divider type="vertical" style="margin: 0;" /></template>
          <a-typography-link :disabled="record.status === 1 || (crtGame === FUNZONE && record.game_project !== FUNZONE) || (crtGame !== FUNZONE && record.game_project === FUNZONE)" @click="handlePublish(record.id)">发布</a-typography-link>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link :disabled="record.push_type === 3" type="danger" danger @click="batchDel(record.id, false)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Form>

  <a-modal
    v-model:open="state.previewOpen"
    title="预览"
    :footer="null"
    class="preview-modal"
  >
    <div v-if="state.previewData.templates && state.previewData.templates.length > 0">
      <div v-for="(item, index) in state.previewData.templates" :key="index">
        <div class="preview-header">
          <p class="preview-title">{{ item.title }}</p>
          <p class="preview-date">{{ state.previewData.created_at ? dayjs.utc(state.previewData.created_at).format('YYYY-MM-DD HH:mm:ss') :  dayjs.utc(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss') }}</p>
        </div>
        <p class="preview-desc" v-html="item.content"></p>
        <div v-if="state.previewData.link_target == 2" class="preview-btn">{{ item.button_text }}</div>
      </div>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
.preview-modal {
  position: relative;
  .preview-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(55, 60, 82, 0.12);
  }
  .preview-title, .preview-date {
    font-size: 14px;
    color: #BEBEBE;
  }
  .preview-desc {
    color: #3D3D3D;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    margin: 20px 0;
  }
  .preview-btn {
    color: #1A1B1F;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 0;
    margin-top: 32px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    border-radius: 12px;
    border: 1px solid rgba(55, 60, 82, 0.18);
    cursor: pointer;
  }
}

</style>
