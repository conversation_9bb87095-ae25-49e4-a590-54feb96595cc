<script setup lang="ts" name="Login">
const onLogin = () => {
  location.href = import.meta.env.VITE_APP_ADMIN_CENTER_API + '/admin/loginSso?system=1&referer=' + encodeURIComponent(`${location.origin}/login`)
}
</script>

<template>
  <div class="wrapper darkAmber">
    <kinesis-container class="kinesis-wrap">
      <kinesis-element :strength="-8" class="kinesis-style kinesis-style-1"></kinesis-element>
      <kinesis-element :strength="10" class="kinesis-style kinesis-style-2"></kinesis-element>
      <kinesis-element :strength="-5" class="kinesis-style kinesis-style-3"></kinesis-element>
      <kinesis-element :strength="-8" class="kinesis-style kinesis-style-4"></kinesis-element>
      <kinesis-element :strength="10" class="kinesis-style kinesis-style-5"></kinesis-element>
      <kinesis-element :strength="-5" class="kinesis-style kinesis-style-6"></kinesis-element>
      <kinesis-element :strength="-8" class="kinesis-style kinesis-style-7"></kinesis-element>
      <kinesis-element :strength="10" class="kinesis-style kinesis-style-8"></kinesis-element>
      <kinesis-element :strength="5" class="kinesis-view">
        <div class="view">
          <img src="~@/assets/img/kit-risk-management-forecasting-and-assessment.gif" alt="">
        </div>
      </kinesis-element>
      <div class="glass-container kinesis-panel">
        <div class="panel-logo">
          <svg width="100px" height="25px" viewBox="0 0 300 75" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="w-100">
            <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g fill="#FF5A00">
                <path d="M55.8479648,42.9125049 L55.8479648,62.9227814 C55.8479648,64.4627289 54.6047234,65.7133712 53.0738887,65.7133712 L11.702407,65.7133712 C10.0757079,65.7133712 8.7965173,64.3090355 8.9403139,62.6786801 L12.0798731,26.5908697 C12.2056951,25.1503709 13.4040002,24.0443812 14.8419662,24.0443812 L37.0914951,24.0443812 C36.5462663,22.6641543 36.2466901,21.1573563 36.2466901,19.5812457 C36.2466901,18.0051351 36.5462663,16.4983371 37.0914951,15.1181102 L14.3326866,15.1181102 C8.58082239,15.1181102 3.78161069,19.542069 3.27832257,25.3070778 L0.0428989523,62.5099188 C-0.523300181,69.0343538 4.58747083,74.6456693 11.097263,74.6456693 L53.6281047,74.6456693 C59.7544393,74.6456693 64.7244094,69.6491274 64.7244094,63.4833102 L64.7244094,42.9125049 C63.3523502,43.4609793 61.8544689,43.7623389 60.287685,43.7623389 C58.7209012,43.7623389 57.2200241,43.4609793 55.8479648,42.9125049 Z" id="Path"></path>
                <path d="M73.9397415,13.4673005 L70.0663235,13.4673005 C68.027997,13.4673005 66.3752191,11.8145226 66.3752191,9.7761962 L66.3752191,5.9027782 C66.3752191,2.64205363 63.7331655,0 60.4724409,0 C57.2117164,0 54.5696627,2.64205363 54.5696627,5.9027782 L54.5696627,9.7761962 C54.5696627,11.8145226 52.9168848,13.4673005 50.8785584,13.4673005 L47.0051404,13.4673005 C43.7444158,13.4673005 41.1023622,16.1093542 41.1023622,19.3700787 C41.1023622,22.6308033 43.7444158,25.2728569 47.0051404,25.2728569 L50.8785584,25.2728569 C52.9168848,25.2728569 54.5696627,26.9256348 54.5696627,28.9639613 L54.5696627,32.8373793 C54.5696627,36.0981038 57.2117164,38.7401575 60.4724409,38.7401575 C63.7331655,38.7401575 66.3752191,36.0981038 66.3752191,32.8373793 L66.3752191,28.9639613 C66.3752191,26.9256348 68.027997,25.2728569 70.0663235,25.2728569 L73.9397415,25.2728569 C77.2004661,25.2728569 79.8425197,22.6308033 79.8425197,19.3700787 C79.8425197,16.1093542 77.2004661,13.4673005 73.9397415,13.4673005 Z" id="Path"></path>
                <path d="M290.974807,40.287923 C287.781185,38.5244652 285.229872,37.0819087 285.229872,34.5986722 C285.229872,32.2743869 286.986513,30.0310767 290.81647,30.0310767 C293.290108,30.0310767 296.002745,31.1527318 296.961727,31.9534856 L298.718369,26.1862588 C298.079047,25.6254313 294.649414,23.6220472 290.338472,23.6220472 C283.075895,23.6220472 277.88962,27.2269388 277.88962,34.1967958 C277.88962,40.0449977 281.322241,43.1700368 285.23286,45.0924457 C289.463139,47.1738058 292.815098,48.8562884 292.815098,51.9033516 C292.815098,55.349292 289.941137,57.2717009 286.272504,57.2717009 C283.398544,57.2717009 280.207909,55.5892183 278.770929,54.4675631 L276.377953,61.5183952 C279.96891,63.6807304 282.442548,64.7244094 286.75349,64.7244094 C294.655389,64.7244094 300,59.3560601 300,52.227252 C299.994025,45.5752973 295.444085,42.7711594 290.974807,40.287923 Z" id="Path"></path>
                <path d="M199.113788,24.0944882 L195.96498,24.0944882 L190.743157,24.0944882 L188.503937,24.0944882 L188.503937,64.7244094 L195.96498,64.7244094 L195.96498,50.9325182 L199.11681,50.9325182 C208.070667,50.9325182 213.543307,46.0443676 213.543307,37.5150128 C213.540285,28.9826388 208.067645,24.0944882 199.113788,24.0944882 Z M199.113788,43.4780131 L195.96498,43.4780131 L195.96498,31.5489933 L199.11681,31.5489933 C202.930434,31.5489933 205.251244,33.6201924 205.251244,37.5119936 C205.248223,41.406814 202.927412,43.4780131 199.113788,43.4780131 Z" id="Shape" fill-rule="nonzero"></path>
                <polygon id="Path" points="226.600431 24.0944882 219.212598 24.0944882 219.212598 64.7244094 221.839782 64.7244094 226.600431 64.7244094 240.472441 64.7244094 240.472441 57.6412709 226.600431 57.6412709"></polygon>
                <polygon id="Path" points="172.128253 48.822069 159.796076 24.0944882 151.653543 24.0944882 151.653543 64.7244094 159.055846 64.7244094 159.055846 39.9998479 171.388023 64.7244094 179.527559 64.7244094 179.527559 24.0944882 172.128253 24.0944882"></polygon>
                <path d="M135.249136,51.9500321 C135.249136,55.7694254 132.854357,58.0937472 128.976378,58.0937472 C125.098399,58.0937472 122.70362,55.7694254 122.70362,51.9500321 L122.70362,24.0944882 L115.275591,24.0944882 L115.275591,50.7485272 C115.275591,59.7159301 120.474308,65.1968504 128.976378,65.1968504 C137.478448,65.1968504 142.677165,59.7159301 142.677165,50.7485272 L142.677165,24.0944882 L135.249136,24.0944882 L135.249136,51.9500321 Z" id="Path"></path>
                <path d="M264.225514,51.9500321 C264.225514,55.7694254 261.830735,58.0937472 257.952756,58.0937472 C254.074777,58.0937472 251.679998,55.7694254 251.679998,51.9500321 L251.679998,24.0944882 L244.251969,24.0944882 L244.251969,50.7485272 C244.251969,59.7159301 249.450686,65.1968504 257.952756,65.1968504 C266.454826,65.1968504 271.653543,59.7159301 271.653543,50.7485272 L271.653543,24.0944882 L264.225514,24.0944882 L264.225514,51.9500321 Z" id="Path"></path>
                <polygon id="Path" points="88.9916386 24.0944882 86.9291339 24.0944882 86.9291339 64.7244094 94.3523468 64.7244094 94.3523468 47.931393 107.055091 47.931393 107.055091 40.8482545 94.3523468 40.8482545 94.3523468 32.4819387 107.716535 32.4819387 107.716535 24.0944882 94.3523468 24.0944882"></polygon>
              </g>
            </g>
          </svg>
          -
          <span class="sys-name">FunZone</span>
        </div>
        <div class="panel-style-logo"></div>
        <div class="panel-header">
          <p>私域管理平台</p>
        </div>
        <div class="panel-btn-main" @click="onLogin"><i class="icon-feishu"></i> - <span>登录</span></div>
      </div>
    </kinesis-container>
  </div>
</template>

<style lang="scss" scoped>
$colorFunPlus: #ff5a00;
$wrapperBorderRadius: 15px;
$wrapperPadding: 30px;
$wrapperColor: rgba(241, 39, 17, .5);
$wrapperColorSub: rgba(245, 175, 25, .5);
.wrapper{
  border-radius: $wrapperBorderRadius;
  position: fixed;
  top: $wrapperPadding;
  right: $wrapperPadding;
  bottom: $wrapperPadding;
  left: $wrapperPadding;
  background: $wrapperColor;  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, $wrapperColorSub, $wrapperColor);  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, $wrapperColorSub, $wrapperColor); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  display: flex;
  align-items: center;
  overflow: hidden;
}
.kinesis-wrap{
  width: 100%;
  height: 100%;
}
.kinesis-view{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  .view{
    width: 50%;
    border-radius: 45%;
    // background-color: rgba(219, 219, 219, 0.6);
    img{
      width: 100%;
      transform: scale(1.2);
    }
  }
}

// 登录面板
.kinesis-panel{
  width: 400px;
  height: 400px;
  max-height: calc(100vh - #{$wrapperPadding * 2});
  padding: $wrapperPadding;
  border-radius: #{$wrapperBorderRadius * 2};
  overflow: hidden;
  position: absolute;
  top: 50%;
  right: 8vw;
  transform: translateY(-50%);
  background-color: rgba($color: #fff, $alpha: 0.4);
}
.panel-logo{
  height: 30px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
  gap: 10px;
  font-size: 16px;
  color: $colorFunPlus;
  font-weight: bold;
  .sys-name{
    transform: scale(1, 1.3);
  }
}
.panel-header{
  height: 40px;
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  color: #614439;
  font-weight: bold;
  font-size: 34px;
}
.panel-btn-main{
  width: 120px;
  height: 42px;
  padding: 0 20px;
  border-radius: 6px;
  color: #fff;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: #000;
  position: absolute;
  right: #{$wrapperPadding};
  bottom: #{$wrapperPadding * 2};
  cursor: pointer;
  .icon-feishu{
    width: 24px;
    height: 24px;
    background: url(@/assets/img/icon-feishu.png) no-repeat center center;
    background-size: contain;
  }
}
.panel-style-logo{
  width: 360px;
  height: 340px;
  filter: blur(6px);
  opacity: 0.34;
  position: absolute;
  left: -120px;
  bottom: -100px;
  background: url(@/assets/img/fp-logo.png) no-repeat left center;
  background-size: auto 100%;
}

// 背景装饰
.kinesis-style{
  position: absolute;
  opacity: 0.35;
  background-color: $colorFunPlus;
  box-shadow: 40px -80vh 0 $colorFunPlus;
}
.kinesis-style-1{
  width: 30px;
  height: 180px;
  bottom: -40px;
  right: 30px;
}
.kinesis-style-2{
  width: 20px;
  height: 150px;
  bottom: 60px;
  right: 130px;
}
.kinesis-style-3{
  width: 36px;
  height: 200px;
  bottom: -50px;
  right: 240px;
}
.kinesis-style-4{
  width: 20px;
  height: 200px;
  bottom: -20px;
  right: 300px;
}
.kinesis-style-5{
  width: 30px;
  height: 120px;
  bottom: -20px;
  right: 400px;
}
.kinesis-style-6{
  width: 26px;
  height: 180px;
  bottom: -60px;
  right: 520px;
}
.kinesis-style-7{
  width: 18px;
  height: 220px;
  bottom: -25px;
  right: 600px;
}
</style>
