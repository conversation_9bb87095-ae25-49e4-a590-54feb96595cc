<script setup lang="ts">
const emits = defineEmits(['close'])
</script>

<template>
  <div class="video-wrap">
    <div class="content">
      <div class="close" @click="emits('close')">
        <CloseCircleOutlined />
      </div>
      <video id="myVideoTwo" controls autoplay>
        <source src="https://kg-web-cdn.akamaized.net/website_plugin/user-platform-web/kg-website/dist/static/fungrowth_v1.mp4" type="video/mp4" />
      </video>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  .content {
    width: 1140px;
    height: 642px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  video {
    width: 100%;
    height: 100%;
  }
  .close {
    position: absolute;
    right: 0;
    top: -40px;
    font-size: 30px;
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
  }
}
</style>