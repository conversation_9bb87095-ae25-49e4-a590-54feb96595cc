<script setup lang="ts">
import { createCombineRule, getCombineRuleDetail, updateCombineRule } from "@/api/platformConfig";
import { useConfigStore } from "@/store";
import { setDefaultFilter } from "@/utils";
import { storeToRefs } from "pinia";

interface FormState {
  id: number;
  game_project: string | undefined;
  is_filter: 0 | 1;
  status: 0 | 1;
  f_os: string | undefined;
  f_channel: string | undefined;
  lang: string | undefined;
  f_s_ids: string;
  f_lv_ids: string;
  uids: string;
}

const props = defineProps(["editId"]);
const emits = defineEmits(["close", "refresh"]);

const configStore = useConfigStore();
const { configState } = storeToRefs(configStore);
const { getGameChannel } = configStore;

const open = ref(true);
const modalLoading = ref(false);
const getDetail = () => {
  modalLoading.value = true;
  getCombineRuleDetail(props.editId)
    .then((res: any) => {
      formState.value = res;
    })
    .finally(() => (modalLoading.value = false));
};
if (props.editId) getDetail();

const formState = ref<FormState>({
  id: 0,
  game_project: undefined,
  status: 0,
  is_filter: 0,
  f_os: "",
  f_channel: "",
  lang: "",
  f_s_ids: "",
  f_lv_ids: "",
  uids: ""
});

const submitLoading = ref(false);
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true;
      console.log("formState.value", JSON.stringify(formState.value));
      if (props.editId) {
        const { id, ...data } = formState.value;
        data.uids = data.uids.replace(/\s+/g, "").replace(/，/g, ",").replace(/,+/g, ",");
        // 编辑
        updateCombineRule(id, data)
          .then(() => {
            emits("close");
            emits("refresh");
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false;
          });
      } else {
        // 新增
        createCombineRule(formState.value)
          .then(() => {
            emits("close");
            emits("refresh");
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false;
          });
      }
    })
    .catch(() => {});
};
</script>

<template>
  <a-drawer
    v-model:open="open"
    :title="props.editId ? '编辑' : '新增'"
    :maskClosable="false"
    :width="500"
    @afterOpenChange="(open: boolean) => !open && emits('close')"
  >
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        class="modal-form"
      >
        <a-form-item label="游戏" name="game_project" :rules="[{ required: true, message: '请选择游戏' }]">
          <a-select
            v-model:value="formState.game_project"
            placeholder="请选择游戏"
            :options="configState.game_projects"
            @change="() => (formState.f_channel = undefined)"
          >
            <template #option="{ value: val, label }">{{ label }}-{{ val }}</template>
          </a-select>
        </a-form-item>

        <a-form-item label="筛选器" name="is_filter" :rules="[{ required: true, message: '请选择筛选器' }]">
          <a-radio-group
            v-model:value="formState.is_filter"
            @change="setDefaultFilter(formState, ['f_os', 'f_channel', 'lang', 'f_s_ids', 'f_lv_ids'])"
          >
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
        <template v-if="formState.is_filter === 1">
          <a-form-item label="操作系统" name="f_os" :rules="[{ required: true, message: '请选择操作系统' }]">
            <SelectWithAllComp v-model:value="formState.f_os" placeholder="请选择操作系统" type="platform"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="语种" name="lang" :rules="[{ required: true, message: '请选择语种' }]">
            <SelectWithAllComp v-model:value="formState.lang" placeholder="请选择语种" type="langs"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="渠道" name="f_channel" :rules="[{ required: true, message: '请选择渠道' }]">
            <SelectWithAll
              v-model:value="formState.f_channel"
              placeholder="请选择渠道"
              :options="formState.game_project ? getGameChannel(formState.game_project || '') : []"
            ></SelectWithAll>
          </a-form-item>
          <a-form-item label="服务器" name="f_s_ids" :rules="[{ required: true, message: '请输入服务器ID' }]">
            <a-textarea v-model:value="formState.f_s_ids" placeholder="请输入服务器ID，例如1,2-4,10,20-30" allow-clear />
          </a-form-item>
          <a-form-item label="城堡等级" name="f_lv_ids" :rules="[{ required: true, message: '请输入城堡等级' }]">
            <a-textarea v-model:value="formState.f_lv_ids" placeholder="请输入城堡等级，例如1,7-9999" allow-clear />
          </a-form-item>
          <a-form-item label="uid白名单" name="uids">
            <a-textarea v-model:value="formState.uids" placeholder="请输入uid，多个uid以“,”隔开" allow-clear />
          </a-form-item>
        </template>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped></style>
