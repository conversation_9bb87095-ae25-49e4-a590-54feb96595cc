<script setup lang="ts">
import { delCombineRule, getCombineRuleList, switchCombineRuleStatus } from "@/api/platformConfig";
import { ENTRANCE_COLUMN_MAP } from "./const";
import Detail from "./Detail.vue";
import { messageConfirm } from "@/utils";
import { Modal } from "ant-design-vue";

const state = reactive({
  editVisible: false,
  editId: 0
});

// 列表数据获取
const RefCustomTable = ref();
const search = () => RefCustomTable.value.requestTableData(true);

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow;
  state.editId = id || 0;
};

// 删除
const batchDel = (id: string) => messageConfirm("删除后游戏内私域融合入口将消失，请谨慎操作", delCombineRule, { id }).then(() => search());

// 状态切换
const switchLoading = ref<boolean[]>([]);
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true;
  record.status = 1 - val;
  Modal.confirm({
    title: "提示",
    content:
      val === 0 ? "关闭后游戏内私域融合入口将消失，请谨慎操作，是否确认关闭？" : "开启后游戏内将展示私域合入口，实时生效，是否确认开启？",
    okText: "确定",
    cancelText: "取消",
    onOk: () => {
      switchLoading.value[index] = false;
      switchCombineRuleStatus(record.id, val).finally(() => search());
    },
    onCancel: () => {
      switchLoading.value[index] = false;
    }
  });
};
</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getCombineRuleList" :params="{}" :columns="ENTRANCE_COLUMN_MAP">
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'status'">
        <a-switch
          v-model:checked="record.status"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="开启"
          un-checked-children="关闭"
          :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"
        ></a-switch>
      </template>
      <FilterCell v-if="column.key === 'filter'" :record="record" />
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0" /></template>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="batchDel(record.id)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Detail v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Detail>
</template>

<style lang="scss" scoped></style>
