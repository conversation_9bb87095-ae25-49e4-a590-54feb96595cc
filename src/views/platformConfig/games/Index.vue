<script setup lang="ts">
import { delGP, getGPList, switchGPStatus } from '@/api/platformConfig';
import { GAMES_COLUMN_MAP } from './const';
import Detail from './Detail.vue';
import { messageConfirm } from '@/utils';
import { Modal } from 'ant-design-vue';
import { useConfigStore } from '@/store';

const state = reactive({
  editVisible: false,
  editId: 0,
})

// 列表数据获取
const RefCustomTable = ref()
const search = () => RefCustomTable.value.requestTableData(true)

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 删除
const batchDel = (id: string) => messageConfirm(
  `删除后将无法拉取到项目下的角色，请谨慎操作`,
  delGP,
  { id }
).then(() => search())

// 状态切换
const { FETCH_GLOBAL_CONFIG } = useConfigStore()
const switchLoading = ref<boolean[]>([])
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true
  record.status = 1 - val
  console.log(val)
  const msg = val === 0 ? '是否关闭当前项目，关闭后将无法拉到项目下的角色，请谨慎操作' : '是否开启当前项目'
  Modal.confirm({
    title: '提示',
    content: msg,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      switchLoading.value[index] = false
      switchGPStatus(record.id, val).finally(() => {
        search()
        FETCH_GLOBAL_CONFIG(true)
      })
    },
    onCancel: () => {
      switchLoading.value[index] = false
    }
  })
}
</script>

<template>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getGPList"
    :params="{}"
    :columns="GAMES_COLUMN_MAP"
  >
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'status'">
        <a-switch v-model:checked="record.status" :checkedValue="1" :unCheckedValue="0" checked-children="开启" un-checked-children="关闭" :loading="switchLoading[index]" @click="(val: number) => beforeSwitch(val, record, index)"></a-switch>
      </template>
      <LangKey v-if="column.key === 'game_title'" :lang-key="record.game_title" :i18n_name="record.i18n_name"></LangKey>
      <a-image v-if="column.key === 'icon_url'" :src="record.icon_url" :height="60" />
      <template v-if="column.key === 'game_ids'">
        {{ JSON.parse(record.game_ids).join('、') }}
      </template>
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0;" /></template>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="batchDel(record.id)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Detail v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Detail>
</template>

<style lang="scss" scoped>

</style>