<script setup lang="ts">
import { createGP, getGPDetail, updateGP } from '@/api/platformConfig';


interface FormState {
  id: number
  game_project: string | undefined
  game_title: string | undefined
  status: number
  icon_url: string
  game_ids: string[]
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getGPDetail(props.editId)
    .then((res: any) => {
      formState.value = res
      formState.value.game_ids = JSON.parse(res.game_ids)
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  game_project: undefined,
  game_title: undefined,
  status: 0,
  icon_url: '',
  game_ids: []
})

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      data.game_ids = JSON.stringify(data.game_ids) as any
      if (props.editId) {
        // 编辑
        updateGP(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        // 新增
        createGP(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
    .catch(() => {})
}
</script>

<template>
  <a-drawer v-model:open="open" title="接入游戏" :maskClosable="false" :width="500" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        class="modal-form"
      >
        <a-form-item label="game_project" name="game_project" :rules="[{ required: true, message: '请输入项目名称' }]">
          <a-input v-model:value="formState.game_project" placeholder=" 请输入项目名称"></a-input>
        </a-form-item>
        <a-form-item label="game_name" name="game_title" :rules="[{ required: true, message: '请选择多语言' }]">
          <SelectLang v-model:value="formState.game_title"></SelectLang>
        </a-form-item>
        <a-form-item label="icon" name="icon_url" :rules="[{ required: true, message: '请上传通道ICON' }]">
          <SelectImg v-model:value="formState.icon_url" :width-height="[72, 72]"></SelectImg>
        </a-form-item>
        <a-form-item label="game_ids" name="game_ids" :rules="[{ required: true, message: '请选择游戏' }]">
          <a-select
            v-model:value="formState.game_ids"
            mode="tags"
            style="width: 100%"
            placeholder="请输入 game_ids 并回车创建"
          ></a-select>
        </a-form-item>


        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>

</style>