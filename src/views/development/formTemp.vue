<script setup lang="ts">
interface FormState {}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])
console.log(emits)

const open = ref(true)
console.log(open)

const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  setTimeout(() => {
    modalLoading.value = false
  }, 1000);
}
if (props.editId) getDetail()

const formState = ref<FormState>({})

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      setTimeout(() => {
        submitLoading.value = false
      }, 1000);
    })
    .catch(() => {})
}
</script>

<template>
  <a-spin :spinning="modalLoading">
    <a-form
      :model="formState"
      name="basic"
      ref="formRef"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      class="modal-form"
    >
      <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
        <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<style lang="scss" scoped>

</style>