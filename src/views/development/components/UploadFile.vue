<script setup lang="ts">
import { PAGE_CONF } from '@/enum';
import { message } from 'ant-design-vue';

const uploadSuccess = () => {
  message.success('上传成功');
}

const downloadApi = (params: any) => new Promise(() => {
  console.log('下载文件', params)
});
</script>

<template>
  <UploadBtn
    v-has="'Operation'"
    class="btn"
    ref="uploadBtn"
    @uploadSuccess="uploadSuccess"
    :downloadApi="downloadApi"
    file-type="entrance"
    :page="PAGE_CONF.DAILY_CHECK"
  />
</template>

<style lang="scss" scoped>

</style>