<script setup lang="ts" name="CustomTable">

const state = reactive({
  searchParams: {}
})
const getTableData = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: [
          { name: '1', age: 18, mode: 'ui_game_sector_entrance_signin_gift', sex: '男' },
          { name: '2', age: 19, mode: 'ui_game_sector_entrance_signin_gift', sex: '女' },
          { name: '3', age: 20, mode: 'ui_game_sector_entrance_signin_gift', sex: '自定义' },
        ],
        total: 100,
        current_page: params.page,
        per_page: params.page_size
      })
    }, 1000)
  })
}
const PROP_COLUMN_MAP = [
  { dataIndex: 'name', key: 'name', title: 'name', width: '100px' },
  { dataIndex: 'age', key: 'age', title: 'age', width: '100px' },
  { dataIndex: 'mode', key: 'mode', title: '模块', width: '100px' },
  { dataIndex: 'sex', key: 'sex', title: '性别', width: '100px' }
]

</script>

<template>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getTableData"
    :params="state.searchParams"
    :columns="PROP_COLUMN_MAP"
  >
    <template #top>title</template>
    <template #leftTool>
      <a-button>按钮</a-button>
    </template>
    <template #rightTool>
      <a-button>按钮</a-button>
    </template>
    <template #bodyCell="{ record, column }">
      <template v-if="column.key === 'mode'">
        <LangKey :lang-key="record.mode"></LangKey>
      </template>
    </template>
  </CustomTable>
</template>

<style lang="scss" scoped>

</style>