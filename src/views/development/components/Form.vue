<script setup lang="ts">
import { message } from 'ant-design-vue';

interface FormState {
  channel_key: string
  icon: string
  content: string
  times: number[] | undefined
  lang: string | undefined
}
const formState = ref<FormState>({
  channel_key: '',
  icon: '',
  content: '<div>Header 1</div>',
  times: undefined,
  lang: undefined
})

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  submitLoading.value = true
  formRef.value
    .validate()
    .then(() => {
      message.success('成功')
      submitLoading.value = false
    })
    .catch(() => {
      submitLoading.value = false
    })
}

const options = [
  {
    label: '选项1',
    value: '1',
  },
  {
    label: '选项2',
    value: '2',
  }
]
</script>

<template>
  <a-form
    :model="formState"
    name="basic"
    ref="formRef"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 16 }"
    autocomplete="off"
  >
    {{ formState }}
    <a-form-item
      label="通道ICON"
      name="icon"
      :rules="[{ required: true, message: '请上传通道ICON' }]"
    >
      <SelectImg v-model:value="formState.icon" :width-height="[1004, 986]"></SelectImg>
    </a-form-item>

    <a-form-item label="选择多语言" name="lang" :rules="[{ required: true, message: '请选择选项' }]">
      <SelectLang v-model:value="formState.lang"></SelectLang>
    </a-form-item>

    <a-form-item label="自动添加 all" name="channel_key" :rules="[{ required: true, message: '请选择选项' }]">
      <SelectWithAll v-model:value="formState.channel_key" :options="options"></SelectWithAll>
    </a-form-item>
  
    <a-form-item label="时间范围选择" name="times" :rules="[{ required: true, message: '请选择选项' }]">
      <SelectDateTime v-model:value="formState.times"></SelectDateTime>
      {{ formState.times }}
    </a-form-item>

    <a-form-item label="富文本" name="content" :rules="[{ required: true, message: '请填写内容' }]">
      <Editor v-model:value="formState.content"></Editor>
    </a-form-item>

    <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
      <a-space>
        <a-button>取消</a-button>
        <a-button type="primary" html-type="submit" @click="submit" :loading="submitLoading">保存</a-button>
      </a-space>
    </a-form-item>
  </a-form>
</template>

<style lang="scss" scoped>

</style>