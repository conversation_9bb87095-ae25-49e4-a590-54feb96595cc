<script setup lang="ts">
import { DLCK_COLUMN_MAP, MONTH_MAPS } from './const'
import Form from './Form.vue'
import { messageConfirm } from '@/utils'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { useRouter } from 'vue-router'
import { copyDLCKNew, delDLCKNew, getDLCKNewList, setDLCKNewStatus } from '@/api/dailycheck'
import { Modal } from 'ant-design-vue'
import { downloadTemp } from '@/api/common'
dayjs.extend(utc)

const state = reactive({
  editVisible: false,
  editId: 0
})
const router = useRouter()

// 列表数据获取
const RefCustomTable = ref()
const search = () => RefCustomTable.value.requestTableData(true)

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}

// 删除
const batchDel = (id: string, isBatch: boolean) =>
  messageConfirm(`确定要删除${isBatch ? '选中的' : '此条'}数据吗？`, delDLCKNew, { id }).then(() => search())
// 复制
const copy = (id: number) => messageConfirm(`确定要复制此条数据并生成一份新数据吗？`, copyDLCKNew, { id }).then(() => search())

// 状态切换
const switchLoading = ref<boolean[]>([])
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true
  record.status = 1 - val
  Modal.confirm({
    title: '提示',
    content: '确定要切换此条数据状态吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      switchLoading.value[index] = false
      setDLCKNewStatus({ id: record.id, status: val }).finally(() => {
        search()
      })
    },
    onCancel: () => {
      switchLoading.value[index] = false
    }
  })
}
</script>

<template>
  <CustomTable ref="RefCustomTable"
               :data-api="getDLCKNewList"
               :params="{}"
               :columns="DLCK_COLUMN_MAP">
    <template #leftTool>
      <a-space>
        <a-button type="primary"
                  @click="showEdit(true)">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
        <a-button type="primary"
                  @click="downloadTemp('checkin-v2')">
          <template #icon>
            <CloudDownloadOutlined />
          </template>
          下载奖品模板
        </a-button>
      </a-space>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'bg_img'">
        <a-image v-if="record.bg_img"
                 :src="record.bg_img"
                 :height="60" />
        <template v-else>-</template>
      </template>
      <template v-if="column.key === 'filter'">
        <a-typography-text :code="true">
          {{ record.filter.f_s_ids }}
        </a-typography-text>
      </template>
      <template v-if="column.key === 'month'">
        {{ MONTH_MAPS.filter(item => record.month === item.value)[0]?.label }}
      </template>
      <template v-if="column.key === 'status'">
        <a-switch v-model:checked="record.status"
                  :checkedValue="10"
                  :unCheckedValue="1"
                  checked-children="开启"
                  un-checked-children="关闭"
                  :loading="switchLoading[index]"
                  @click="(val: number) => beforeSwitch(val, record, index)"></a-switch>
      </template>
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical"
                       style="margin: 0;" /></template>
          <a-typography-link type="success"
                             @click="copy(record.id)">复制</a-typography-link>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link @click="router.push(`/dailyCheck/new/detail/${record.id}`)">详情</a-typography-link>
          <a-typography-link type="danger"
                             danger
                             @click="batchDel(record.id, false)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible"
        :edit-id="state.editId"
        @close="showEdit(false)"
        @refresh="search"></Form>
</template>

<style lang="scss" scoped>
</style>
