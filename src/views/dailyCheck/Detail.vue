<script setup lang="ts">
import { downloadDLCKGift, getDLCKGiftList } from '@/api/dailycheck';
import { DLCK_GIFT_COLUMN_MAP } from './const';
import { PAGE_CONF } from '@/enum';
import { useRoute } from 'vue-router';

const route = useRoute()
const state = reactive({
  id: route.params.id && Number(route.params.id) || 0
})

// 列表数据获取
const RefCustomTable = ref()
const search = () => RefCustomTable.value.requestTableData(true)

</script>

<template>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getDLCKGiftList"
    :params="{ id: state.id }"
    :columns="DLCK_GIFT_COLUMN_MAP"
    :pagination="false"
  >
    <template #rightTool>
      <UploadBtn
        v-has="'Operation'"
        ref="uploadBtn"
        @uploadSuccess="search"
        :downloadApi="downloadDLCKGift"
        :uploadData="{ id: state.id }"
        :downloadData="{ id: state.id }"
        fileType="checkin"
        :page="PAGE_CONF.DAILY_CHECK"
      />
    </template>
  </CustomTable>

</template>

<style lang="scss" scoped>

</style>