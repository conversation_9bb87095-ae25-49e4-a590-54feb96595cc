<script setup lang="ts">
import { MONTH_MAPS } from './const'
import { UploadProps } from 'ant-design-vue'
import { Rule } from 'ant-design-vue/es/form'
import { getDLCKWeeklyDetail, addDLCKWeekly, updateDLCKWeekly } from '@/api/dailycheck'

interface FormState {
  id: number
  month: number | undefined
  bg_img: string
  f_s_ids: string
  [key: string]: any
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getDLCKWeeklyDetail(props.editId)
    .then((res: any) => {
      formState.value = res
    })
    .finally(() => (modalLoading.value = false))
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  month: undefined,
  bg_img: '',
  f_s_ids: ''
})
const fileList = ref<any[]>([])

const submitLoading = ref(false)
const formRef = ref()
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const formData = new FormData()
      formData.append('month', `${formState.value.month}`)
      formData.append('bg_img', formState.value.bg_img)
      formData.append('f_s_ids', `${formState.value.f_s_ids}`)
      if (fileList.value.length) {
        formData.append('file', fileList.value[0])
      }
      if (formState.value.id) {
        formData.append('id', `${formState.value.id}`)
      }

      const api = formState.value.id ? updateDLCKWeekly : addDLCKWeekly

      api(formData)
        .then(() => {
          emits('close')
          emits('refresh')
        })
        .catch(() => {})
        .finally(() => {
          submitLoading.value = false
        })
    })
    .catch(() => {})
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  fileList.value = [...(fileList.value || []), file]
  return false
}

const validateFile = async (_rule: Rule) => {
  if (fileList.value.length) {
    return Promise.resolve()
  }
  if (formState.value.id) {
    return Promise.resolve()
  }
  if (formState.value.file) {
    return Promise.resolve()
  }
  return Promise.reject('请上传奖品配置文件')
}

const change = (v: number) => {
  const max = formState.value.filter.server_max || 0
  const min = formState.value.filter.server_min || 0
  formState.value.filter.server_max = max < min ? v : formState.value.filter.server_max
}
</script>

<template>
  <a-drawer v-model:open="open"
            :title="props.editId ? '编辑' : '新增'"
            :maskClosable="false"
            :width="600"
            @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form :model="formState"
              name="basic"
              ref="formRef"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
              autocomplete="off">
        <a-form-item label="奖品配置"
                     :rules="[{ validator: validateFile }]">
          <a-upload :file-list="fileList"
                    :before-upload="beforeUpload"
                    @remove="() => fileList = []"
                    :max-count="1"
                    accept="'.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'">
            <a-button>
              <upload-outlined></upload-outlined>
              点击上传
            </a-button>
          </a-upload>
          <a-typography-text type="warning"
                             style="font-size: 12px;">
            <ExclamationCircleFilled /> 新增时必须上传奖励配置
          </a-typography-text>
        </a-form-item>

        <a-form-item label="服务器"
                     name="f_s_ids"
                     :rules="[{ required: true, message: '请输入服务器ID' }]">
          <a-textarea v-model:value="formState.f_s_ids"
                      placeholder="请输入服务器ID，例如1,2-4,10,20-30"
                      allow-clear />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary"
                    @click="submit"
                    :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
</style>
