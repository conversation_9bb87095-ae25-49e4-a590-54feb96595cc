<script setup lang="ts">
import { getCommonRule } from '@/api/platformConfig'
import { COMMON_CONF_COLUMNS_MAP } from './const'
import Form from './Form.vue'
import { downloadTemp } from '@/api/common'

const state = reactive({
  editVisible: false,
  key: ''
})

// 列表数据获取
const RefCustomTable = ref()
const search = () => RefCustomTable.value.requestTableData(true)
const showEdit = (isShow: boolean, key?: string) => {
  state.editVisible = isShow
  state.key = key || ''
}
const download = () => {
  downloadTemp('rule')
}
</script>

<template>
  <CustomTable ref="RefCustomTable"
               :data-api="getCommonRule"
               :params="{}"
               :columns="COMMON_CONF_COLUMNS_MAP"
               :pagination="false">
    <template #bodyCell="{ record, column }">
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical"
                       style="margin: 0;" /></template>
          <a-typography-link @click="showEdit(true, record.key)">编辑</a-typography-link>
          <a-typography-link @click="download()">下载模板</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible"
        :edit-key="state.key"
        @close="showEdit(false)"
        @refresh="search" />
</template>

<style lang="scss" scoped>
</style>
