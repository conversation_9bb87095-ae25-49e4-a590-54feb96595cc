<script setup lang="ts">
import { downloadTemp } from '@/api/common';
import { getCommonRuleDetail, updateCommonRule, updateRuleFile } from '@/api/platformConfig';
import { message } from 'ant-design-vue';
import { Rule } from 'ant-design-vue/es/form';

interface IFormState {
  name: string | undefined
  key: string
  file: string
  content: Record<string, { text: string, title?: string, length: number }>
  last_uploaded_file: string
}

const props = defineProps(['editKey'])
const emits = defineEmits(['close', 'refresh'])

const formState = ref<IFormState>({
  name: undefined,
  key: '',
  file: '',
  content: {},
  last_uploaded_file: ''
})
const crtKey = ref('en')

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getCommonRuleDetail(props.editKey)
    .then((res: any) => {
      formState.value = { ...formState.value, ...res }
    })
    .finally(() => modalLoading.value = false)
}
getDetail()

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      // 编辑
      updateCommonRule(formState.value)
        .then(() => {
          emits('close')
          emits('refresh')
        })
        .catch(() => {})
        .finally(() => {
          submitLoading.value = false
        })
    })
    .catch(() => {})
}

// 文件上传
const fileUpload = ref({
  fileList: [],
  url: ''
})
const beforeUpload = async (file: any) => {
  if (!file) return false

  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('key', formState.value.key)
    updateRuleFile(formData).then((res: any) => {
      formState.value.content = res.content
      message.success('上传解析成功')
    }).catch()
    // 上传图片
  }  catch (err) {}
  return false
}

const downloadFile = () => {
  if (formState.value.last_uploaded_file) {
    window.open(formState.value.last_uploaded_file, '_blank')
  } else {
    downloadTemp(formState.value.key)
  }
}

const validateEditor = async (_rule: Rule, value: Record<string, { text: string, title?: string }>) => {
  let flag = true
  if (formState.value.key === 'faq_rules') {
    flag = false
  } else {
    for (const lang in value) {
      if (lang === 'en' || lang === 'zh_cn') {
        if (!value[lang].text) return Promise.reject('英文或中文内容不可为空');
      }
      if (!value[lang].text) flag = false
    }
  }
  if (flag) return Promise.reject('内容不可为空');
  // return Promise.reject("Two inputs don't match!");
  return Promise.resolve();
}
</script>

<template>
  <a-drawer v-model:open="open" title="编辑规则" :maskClosable="false" :width="600" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="规则分类" name="name" :rules="[{ required: true, message: '请选择规则分类' }]">
          <a-input v-model:value="formState.name" placeholder="请输入规则分类" />
        </a-form-item>
        <a-form-item label="规则KEY" name="key" :rules="[{ required: true, message: '请输入规则KEY' }]">
          <a-input v-model:value="formState.key" placeholder="请输入规则key" disabled />
        </a-form-item>
        <a-form-item label="规则文档" name="file">
          <a-upload
            name="file"
            style="height: 110px;"
            v-model:file-list="fileUpload.fileList"
            action="#"
            accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            :before-upload="beforeUpload"
            @remove="fileUpload.url = ''"
          >
            <a-space>
              <a-button>
                <upload-outlined></upload-outlined>
                点击上传
              </a-button>
              <a-button type="primary" @click.stop="downloadFile"><template #icon><CloudDownloadOutlined /></template>{{ formState.last_uploaded_file ? '下载文档' : '下载模板'}}</a-button>
            </a-space>
          </a-upload>
        </a-form-item>
        <a-form-item label="按语种编辑" name="content" :rules="[{ validator: validateEditor }]">
          <a-radio-group v-model:value="crtKey" style="line-height: 32px;margin-bottom: 8px;">
            <a-space wrap>
              <a-radio v-for="(_, key) in formState.content" :key="key" :value="key">{{ key }}</a-radio>
            </a-space>
          </a-radio-group>
          <template v-if="crtKey">
            <template v-if="formState.key === 'faq_rules'">
              <a-descriptions layout="vertical" bordered :column="1" size="small" v-if="formState.content[crtKey].length">
                <a-descriptions-item v-for="(item, index) in formState.content[crtKey]" :label="(item as any)[`question${index + 1}`]"><span v-html="(item as any)[`answer${index + 1}`]"></span></a-descriptions-item>
              </a-descriptions>
            </template>
            <template v-else-if="JSON.stringify(formState.content) !== '{}'">
              <Editor :key="crtKey" v-model:value="formState.content[crtKey].text"></Editor>
            </template>
          </template>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>

</style>