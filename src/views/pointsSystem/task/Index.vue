<script setup lang="ts">
import Form from './Form.vue';
import { getPropsList, delProps, updatePropsStatus, downloadList } from '@/api/pointsTask';
import { langKeyDetail, getGiftPkgNumList, LangGetData } from '@/api/resource';
import { PAGE_CONF } from '@/enum';
import { messageConfirm } from '@/utils';
import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

const GMAE_PROPS_COLUMNS_MAP = [
  { dataIndex: 'id', key: 'id', title: '任务ID', width: '100px' },
  { dataIndex: 'task_category', key: 'task_category', title: '任务类型', width: '100px' },
  { dataIndex: 'lang_key', key: 'lang_key', title: '任务描述', width: '130px' },
  // { dataIndex: 'vip', key: 'vip', title: '游戏', width: '80px' },

  { dataIndex: 'filter_server', key: 'filter', title: '服务器ID', width: '130px' },
  // { dataIndex: 'filter_vip_restriction', key: 'filter', title: 'VIP条件', width: '130px' },
  { dataIndex: 'dimension', key: 'dimension', title: '任务判定维度', width: '130px' },
  { dataIndex: 'cycle_type', key: 'cycle_type', title: '周期', width: '130px' },
  { dataIndex: 'cycle_times', key: 'cycle_times', title: '周期内次数', width: '130px' },
  { dataIndex: 'progress', key: 'progress', title: '单次进度', width: '130px' },
  { dataIndex: 'url', key: 'url', title: '跳转链接', width: '130px' },
  { dataIndex: 'pkg_id', key: 'pkg_id', title: '礼包名称（后台）', width: '130px' },
  { dataIndex: 'status', key: 'status', title: '状态', width: '130px' },
  { dataIndex: 'task_validity_type', key: 'task_validity_type', title: '有效期', width: '200px' },
  { dataIndex: 'game_project', key: 'game_project', title: '类型', width: '130px' },
  { dataIndex: 'action', key: 'action', title: '操作', width: '130px', fixed: 'right', align: 'center' }
]

const state = reactive({
  editVisible: false,
  editId: 0,
  searchParams: {
    task_category: null,
    vip_restriction: null,
  } as any,
  previewOpen: false,
  previewData: {},
  // 任务类型:1日常任务,2活跃任务,3成长任务,4游戏任务,
  optionsTaskCategory: [
    { label: '日常任务', value: 1 },
    { label: '活跃任务', value: 2 },
    { label: '成长任务', value: 3 },
    { label: '游戏任务', value: 4 },
    { label: '限时任务', value: 5 },
  ],
  // VIP条件- LV1-LV7
  optionsVip: [
    { label: 'LV1', value: 1 },
    { label: 'LV2', value: 2 },
    { label: 'LV3', value: 3 },
    { label: 'LV4', value: 4 },
    { label: 'LV5', value: 5 },
    { label: 'LV6', value: 6 },
    { label: 'LV7', value: 7 },
    // { label: 'LV8', value: 8 },
    // { label: 'LV9', value: 9 },
    // { label: 'LV10', value: 10 },
  ],
  optionsGift: [
    { label: '每日礼包', value: 1 },
    { label: '每周礼包', value: 2 },
    { label: '每月礼包', value: 3 },
    { label: '每年礼包', value: 4 },
    { label: '等级礼包', value: 5 },
    { label: '活动礼包', value: 6 },
  ],
  optionsGift1: [
    { label: '日', value: 1 },
    { label: '周', value: 2 },
    { label: '月', value: 3 },
    { label: '年', value: 4 },
    { label: '等级', value: 5 },
    { label: '终身', value: 6 },
  ],
  trigger1: [
    { label: '账号', value: 0 },
    { label: '角色', value: 1 },
  ],
  optionsGifts: [] as any[],
  optionsLang: [] as any[],
})

const switchLoading = ref<boolean[]>([])

// 列表数据获取
const RefCustomTable = ref()
// 搜索
const search = (noUpdate?: boolean) => RefCustomTable.value.requestTableData(!noUpdate)

onMounted(() => {
  getGiftPkgListFunc()
  langGetDataFunc({ page: 1, page_size: 1000 })
  getLangItem('task_desc')
})

const getGiftPkgListFunc = () => {
  getGiftPkgNumList()
    .then((res: any) => {
      if (res && res.length > 0) {
        state.optionsGifts = res
      }
    })
}

const langGetDataFunc = (params: any) => {
  LangGetData(params)
    .then((res: any) => {
      console.log('LangGetData res', res)
      if (res.data && res.data.length > 0) {
        state.optionsLang = res.data.map((item: any) => ({
          label: item.zh_cn,
          value: item.key,
        }))
      }
    })
}

const getLangItem = async (key: string) => {
  const res = await langKeyDetail({ key })
  console.log('res', res)
  const item = res?.zh_cn || '-'
  console.log('item', item)
  return item
}

// 编辑
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow
  state.editId = id || 0
}


// 删除
const batchDel = (id: string, isBatch: boolean) => messageConfirm(
  `确定要删除 ${isBatch ? '选中的' : '此条'}数据吗？`,
  delProps,
  { petId: id }
).then(() => search())

const beforeSwitch = (val: number, record: any, index: number) => {
  console.log('beforeSwitch val', val)
  switchLoading.value[index] = true
  record.status = 1 - val

  const sendVal = val === 0 ? 2 : 1

  Modal.confirm({
    title: '提示',
    content: '确定要切换此条数据状态吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      switchLoading.value[index] = false
      updatePropsStatus(record.id, sendVal).finally(() => search(true))
    },
    onCancel: () => {
      switchLoading.value[index] = false
    }
  })
}

</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getPropsList" :params="state.searchParams"
    :columns="GMAE_PROPS_COLUMNS_MAP">
    <template #top>
      <a-space direction="vertical">
        <a-space wrap style="gap: 20px;">
          <a-select style="width: 215px;" allowClear v-model:value="state.searchParams.task_category"
            :options="state.optionsTaskCategory" placeholder="请选择任务类型" />
          <!-- <a-select style="width: 215px;" allowClear v-model:value="state.searchParams.search_key" :options="state.optionsTaskCategory" placeholder="请选择任务子类" /> -->
          <a-select style="width: 215px;" allowClear v-model:value="state.searchParams.vip_restriction"
            :options="state.optionsVip" placeholder="请选择VIP条件" />
          <a-button type="primary" @click="search">搜索</a-button>
          <a-button
            @click="() => { (state.searchParams.task_category = null); (state.searchParams.vip_restriction = null); search() }">重置</a-button>
        </a-space>
        <a-space wrap style="padding: 20px 0;gap: 20px;">
          <UploadBtn v-has="'Operation'" ref="uploadBtn" @uploadSuccess="search" :downloadApi="downloadList"
            fileType="task-config" :page="PAGE_CONF.TASKCONFIG" :hideUploadBtn="true" />
          <a-button type="primary" @click="showEdit(true)">
            <template #icon>
              <PlusOutlined />
            </template>
            新增任务
          </a-button>
        </a-space>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'task_category'">
        {{ state.optionsTaskCategory.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'lang_key'">
        <!-- {{ getLangItem(record[column.key]) }} -->
        {{ state.optionsLang.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.dataIndex === 'filter_server'">
        {{ record[column.key] && JSON.parse(record[column.key])?.server_max ? (JSON.parse(record[column.key]).server_min
          + '-' + JSON.parse(record[column.key]).server_max) : '-' }}
      </template>
      <template v-if="column.dataIndex === 'filter_vip_restriction'">
        {{ record[column.key] && JSON.parse(record[column.key])?.vip_restriction ? state.optionsVip.find((item: any) =>
          item.value === JSON.parse(record[column.key])?.vip_restriction)?.label : '-' }}
      </template>
      <template v-if="column.dataIndex === 'dimension'">
        {{ state.trigger1.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.dataIndex === 'cycle_type'">
        {{ state.optionsGift1.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.dataIndex === 'progress'">
        {{ record[column.key] ? record[column.key] : '无限制' }}
      </template>
      <template v-if="column.dataIndex === 'pkg_id'">
        {{ state.optionsGifts.find((item: any) => item.value == record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'status'">
        <a-switch v-model:checked="record.status" :checkedValue="1" :unCheckedValue="0" checked-children="已上线"
          un-checked-children="未上线" :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"></a-switch>
      </template>
      <template v-if="column.key === 'task_validity_type'">
        {{ record[column.key] === 2 ? `${dayjs.utc(record.task_start_time * 1000).format('YYYY-MM-DD HH:mm:ss')} -
        ${dayjs.utc(record.task_end_time * 1000).format('YYYY-MM-DD HH:mm:ss')}` : '永久' }}
      </template>
      <template v-if="column.key === 'game_project'">
        {{ record[column.key] === "funplus_zone" ? '平台' : '游戏' }}
      </template>

      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0;" /></template>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" danger @click="batchDel(record.id, false)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Form>
</template>

<style lang="scss" scoped>
.preview-modal {
  position: relative;

  .preview-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(55, 60, 82, 0.12);
  }

  .preview-title,
  .preview-date {
    font-size: 14px;
    color: #BEBEBE;
  }

  .preview-desc {
    color: #3D3D3D;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    margin: 20px 0;
  }

  .preview-btn {
    color: #1A1B1F;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 0;
    margin-top: 32px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    border-radius: 12px;
    border: 1px solid rgba(55, 60, 82, 0.18);
    cursor: pointer;
  }
}
</style>
