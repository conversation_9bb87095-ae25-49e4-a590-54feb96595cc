<script setup lang="ts">
import { createProps, getPropsDetail, updateProps, exportTaskRule } from '@/api/pointsTask';
import { getGiftPkgNumList, LangGetData } from '@/api/resource';
import { onMounted } from 'vue';
import { useConfigStore } from '@/store';
import { PAGE_CONF } from '@/enum';
import { Modal, message } from 'ant-design-vue';
// import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';

interface FormState {
  id: number
  task_category: number | string
  lang_key: string | undefined
  filter: any
  task_dimension_desc: string
  cycle_type: number | string
  cycle_times: number | string
  pkg_id: number | string | undefined
  filterData: any
  dimension: number | string
  progress: string | number
  image: string
  task_key: string
  event_id: number | string | null
  task_event_id: number | string | null
  url_target: number | string
  url: string
  reward_type: number | string
  coin_num: number
  task_where: string
  task_where_data: any[]
  times: number[] | undefined
  task_validity_type: 1 | 2
  [key: string]: any
  task_params: string
  task_params_arr: any[]
  rule_desc: string

  // 默认配置
  name_key: string
  quantity: number
  is_filter: boolean
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const { configState } = useConfigStore()

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getPropsDetail(props.editId)
    .then((res: any) => {
      console.log('pointsSystem getPropsDetail res', res)
      formState.value = res
      formState.value.pkg_id = Number(res.pkg_id)
      formState.value.filterData = res.filter && res.filter.includes('server_min') ? JSON.parse(res.filter) : {}
      // 先强制设置filterData.open_filter为false
      formState.value.filterData.open_filter = false
      formState.value.task_where_data = res.task_where && res.task_where.includes('event_key') ? JSON.parse(res.task_where) : []
      if (res.task_validity_type === 2 && res.task_start_time && res.task_end_time) {
        formState.value.times = [res.task_start_time, res.task_end_time]
      } else {
        formState.value.times = undefined
      }

      // 重置rule_desc
      // formState.value.rule_desc = res.rule_desc || ''

      const task_params = res.task_params && res.task_params.includes('params_arr') ? JSON.parse(res.task_params) : {}

      if(task_params.params_arr && task_params.params_arr.length > 0) {
        // 判断task_params_arr中是否有is_progress且为1的值，如果有则将state.progressKey设置为该值的key，并且将task_params_arr中的is_progress设置为按顺序1,2,3...
        const progressKey = task_params.params_arr.findIndex((item: any) => item.is_progress === 1)
        // console.log('progressKey', progressKey)
        state.progressKey = progressKey + 1
        task_params.params_arr = task_params.params_arr.map((item: any, index: number) => {
          item.is_progress = index + 1
          return item
        })
        formState.value.task_params_arr = task_params.params_arr
      }

      // 如果是编辑状态且上线状态，则提示不可编辑
      if (res && res.status === 1) {
        state.isOnline = true
        Modal.confirm({
          title: '提示',
          content: '当前任务上线中，请下线后进行修改',
          okText: '确定',
          cancelText: '',
          cancelButtonProps: {
            ghost: true,
          },
          onOk: () => {
          }
        })
      } else {
        state.isOnline = false
      }
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  task_category: 1,
  lang_key: undefined,
  filter: '{}',
  task_dimension_desc: '123',
  cycle_type: 1,
  cycle_times: 1,
  pkg_id: undefined,
  dimension: 1,
  progress: 1,
  image: '',
  task_key: new Date().getTime().toString(),
  event_id: null,
  task_event_id: null,
  url_target: 0,
  url: '',
  reward_type: 1,
  coin_num: 0,
  task_validity_type: 1,
  times: undefined,
  // 默认配置
  name_key: '', // 名称多语言key
  quantity: 0, // 包裹数量
  is_filter: false, // 是否过滤
  task_where: '',
  task_where_data: [
    {
      value: '',
      key: Date.now(),
      event_key: null,
      operator: '',
    },
  ] as any[],
  filterData: {
    server_min: 0,
    server_max: 99999,
    vip_restriction: 1,
    game_growth: 0,
    game_role_growth: 0,
    open_filter: false,
  },
  task_params: '',
  task_params_arr: [],
  rule_desc: '',
})

const state = reactive({
  lang_value: '',
  lang_label: '',
  task_desc: '',
  isOnline: false,
  uploadLoading: false,
  progressKey: 0,
  optionsTaskCategory: [
    { label: '日常任务', value: 1 },
    { label: '活跃任务', value: 2 },
    { label: '成长任务', value: 3 },
    { label: '游戏任务', value: 4 },
    { label: '限时任务', value: 5 },
  ],
  optionsGift: [
    { label: '每日礼包', value: 1 },
    { label: '每周礼包', value: 2 },
    { label: '每月礼包', value: 3 },
    { label: '每年礼包', value: 4 },
    { label: '等级礼包', value: 5 },
    { label: '活动礼包', value: 6 },
  ],
  optionsOne: [
    { label: '系统通知', value: 1 },
  ],
  optionsTwo: [] as any[],
  optionsVip: [
    { label: 'LV1', value: 1 },
    { label: 'LV2', value: 2 },
    { label: 'LV3', value: 3 },
    { label: 'LV4', value: 4 },
    { label: 'LV5', value: 5 },
    { label: 'LV6', value: 6 },
    { label: 'LV7', value: 7 },
    // { label: 'LV8', value: 8 },
    // { label: 'LV9', value: 9 },
    // { label: 'LV10', value: 10 },
  ],
  jumpType: [
    { label: '跳转到特定页面', value: 0 },
    { label: '打开游戏', value: 2 },
  ],
  domains: [
    {
      value: '',
      key: Date.now(),
    },
  ],
  trigger1: [
    { label: '按账号', value: 0 },
    { label: '按角色', value: 1 },
  ],
  trigger2: [
    { label: '每日领取', value: 1 },
    { label: '每周领取', value: 2 },
    { label: '每月领取', value: 3 },
    { label: '每年领取', value: 4 },
    { label: '当前等级领取', value: 5, disabled: true },
    { label: '终身领取', value: 6 },
  ],
  // 1-10
  progressList: [
    { label: '每次需完成1个行为', value: 1 },
    { label: '每次需完成2个行为', value: 2 },
    { label: '每次需完成3个行为', value: 3 },
    { label: '每次需完成4个行为', value: 4 },
    { label: '每次需完成5个行为', value: 5 },
    { label: '每次需完成6个行为', value: 6 },
    { label: '每次需完成7个行为', value: 7 },
    { label: '每次需完成8个行为', value: 8 },
    { label: '每次需完成9个行为', value: 9 },
    { label: '每次需完成10个行为', value: 10 },
  ],
  // 奖励类型:0平台道具奖励,1游戏道具奖励
  rewardType: [
    // { label: '平台道具奖励', value: 0 },
    { label: '游戏道具奖励', value: 1 },
    // { label: '积分', value: 2 },
  ],
  optionsGifts: [] as any[],
  optionsGiftsFilter: [] as any[],
  optionsLang: [] as any[],
  optionsLangKey: [] as any[],
  optionsOriginTaskEvent: [] as any[],
  optionsTaskEvent: [] as any[],
})

// 校验规则
const rules = reactive({
  filterData: {
    task_where: [
      { required: true, message: '请选择任务条件', trigger: 'change' },
    ]
  }
}) as any;

const submitLoading = ref(false)
const formRef = ref();

onMounted(() => {
  getGiftPkgListFunc()
  langGetDataFunc({ page: 1, page_size: 1000 })
})

watch(() => configState, (value) => {
  // console.log('configState value 0000 ', value)
  if (value && value.task_events && value.task_events.length > 0) {
    state.optionsTaskEvent = value.task_events.map((item: any) => ({
      originLabel: item.label,
      label: item.label,
      value: item.task_events_id,
      // task_events_id: item.task_events_id,
    }))
  }
  // state.optionsTaskEvent = value ? value.task_events : []
}, { immediate: true })

// 监听任务类型 formState.task_category 变化，如果为限时任务，则显示任务有效期为定时
watch(() => formState.value.task_category, (value) => {
  if (value === 5) {
    formState.value.task_validity_type = 2
  }
})

// 监听formState.progress变化，更新state.optionsTaskEvent数组中每一项的label，将label中的{progress}替换为当前progress的值
watch(() => formState.value.progress, (value) => {
  // console.log('formState.value.progress value', value)
  // console.log('state.optionsTaskEvent', state.optionsTaskEvent)
  state.optionsTaskEvent.forEach((item: any) => {
    item.label = item.originLabel.replace(/\{progress\}/g, value)
  })
  // console.log('state.optionsTaskEvent22', state.optionsTaskEvent)
}, { immediate: true })

const initLabel = (value: string) => {
  // console.log('initLabel value', value)
  if(value) {
    // initLabel value 商城购买超过{parameter}美金的订单{progress}笔,或积分商城消耗{parameter}积分
    const list = formState.value.task_params_arr || []
    list.forEach((item: any) => {
      value = value.replace(/\{parameter\}/, item.value)
    })
    // console.log('initLabel value2', value)
    state.task_desc = value.replace(/\{progress\}/g, String(formState.value.progress))
  }
}

// 切换任务描述，判断是否有{parameter}，如果有则判断出现的次数，然后根据次数添加到formState.task_params_arr数组中
const updateLangLabel = (value: string) => {
  // console.log('updateLangLabel value', value)
  state.lang_label = value
  if (value) {
    const reg = /\{parameter\}/g
    const match = value.match(reg)
    // console.log('match', match)
    if (match) {
      const count = match.length
      console.log('count', count)
      formState.value.task_params_arr = Array.from({ length: count }, (_v, _k) => {
        // console.log('_v, _k', _v, _k)
        return {
          key: `parameter`,
          value: 1,
          is_progress: _k + 1,
        }
      })
      state.task_desc = value.replace(/\{parameter\}/g, '1').replace(/\{progress\}/g, String(formState.value.progress))
    } else {
      formState.value.task_params_arr = []
    }
  } else {
    formState.value.task_params_arr = []
  }
  console.log('formState.value.task_params_arr', formState.value.task_params_arr)
}

const handleChangeParams = () => {
  if (state.lang_label) {
    const langLabelArr = state.lang_label.split('{parameter}')
    // console.log('langLabelArr', langLabelArr)
    const paramsValues = formState.value.task_params_arr.map((item: any) => item.value)
    // console.log('paramsValues', paramsValues)
    // 遍历paramsValues，替换langLabelArr中的{parameter}为paramsValues中的值
    if(paramsValues.length > 0) {
      for (let j = 0; j < paramsValues.length; j++) {
        langLabelArr[j] = langLabelArr[j] + paramsValues[j]
      }
      state.task_desc = langLabelArr.join('').replace(/\{progress\}/g, String(formState.value.progress))
    }
  }
}

const getGiftPkgListFunc = () => {
  getGiftPkgNumList()
    .then((res: any) => {
      if (res && res.length > 0) {
        state.optionsGifts = res
        state.optionsGiftsFilter = [...state.optionsGifts]
      }
    })
}

const langGetDataFunc = (params: any) => {
  LangGetData(params)
    .then((res: any) => {
      if (res.data && res.data.length > 0) {
        state.optionsLang = res.data.map((item: any) => ({
          label: item.zh_cn,
          value: item.key,
        }))
        state.optionsLangKey = [...state.optionsLang]
      }
    })
}

const handleGiftPackage = (value: any) => {
  if(value === undefined) {
    state.optionsGiftsFilter = state.optionsGifts
  }
}

const fetchGiftPackage = (value: string) => {
  const filterData = state.optionsGifts.filter((item: any) => item.label?.indexOf(value) > -1)
  // console.log('filterData', filterData)
  state.optionsGiftsFilter = filterData
}

const uploadSuccess = (res: any) => {
  console.log('uploadSuccess res', res)
  formState.value.rule_desc = JSON.stringify((res || []))
  message.success('上传解析成功')
}

// 处理选择变化
const handleStyleTypeChange = (_index: number, _progress: number) => {
  // 在这里你可以处理选择变化时需要执行的其他逻辑
  // console.log('state.progressKey', state.progressKey)
  // console.log(`Selected index: ${_index}, Selected progress: ${_progress}`);
};

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      try {
        data.filter = JSON.stringify(data.filterData)
        data.task_where = JSON.stringify(data.task_where_data)
        console.log('submit data', data)
        if (data.task_validity_type === 2 && data.times) {
          data.task_start_time = data.times[0]
          data.task_end_time = data.times[1]
        } else {
          data.task_start_time = 0
          data.task_end_time = 0
        }
        if(data.task_params_arr && data.task_params_arr.length > 0) {
          // 重组task_params_arr，判断state.progressKey是否大于0且task_params_arr中的is_progress等于state.progressKey，如果是则将is_progress设置为1，否则设置为0
          const params_arr = [] as any[]
          data.task_params_arr.forEach((item: any) => {
            if(state.progressKey > 0 && state.progressKey === item.is_progress) {
              item.is_progress = 1
            } else {
              item.is_progress = 0
            }
            params_arr.push(item)
          })
          console.log('params_arr', params_arr)
          data.task_params = JSON.stringify({
            params_arr
          })
        } else {
          data.task_params = '{}'
        }
      } catch (error) {
        console.log('error', error)
      }
      console.log('data.task_params', data.task_params)
      console.log('data.rule_desc', data.rule_desc)
      console.log('data', data)
      if (props.editId) {
        updateProps(id, data)
          .then(() => {
            emits('close')
            emits('refresh', true)
          })
          .catch(() => { })
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        createProps(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => { })
          .finally(() => {
            submitLoading.value = false
          })
      }
      setTimeout(() => {
        submitLoading.value = false
      }, 1000);
    })
    .catch(() => { })
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑任务' : '新增任务'" :maskClosable="false" :width="800"
    @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form :model="formState" :rules="rules" name="basic" ref="formRef" :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }" autocomplete="off">

        <a-form-item label="任务类型" name="task_category" :rules="[{ required: true, message: '请选择任务类型' }]">
          <a-select style="width: 100%;" v-model:value="formState.task_category" :options="state.optionsTaskCategory" />
        </a-form-item>

        <!-- <a-form-item label="服务器" name="filterData.server_max">
          <a-space style="gap: 5px;">
            <a-input-number style="width: 100%;" v-model:value="formState.filterData.server_min" :min="0" :max="1000000"
              placeholder="请填写服务器区间" />
            <span>-</span>
            <a-input-number style="width: 100%;" v-model:value="formState.filterData.server_max" :min="0" :max="1000000"
              placeholder="请填写服务器区间" />
          </a-space>
        </a-form-item> -->

        <a-form-item label="任务条件" name="task_event_id" :rules="[{ required: true, message: '请选择任务事件' }]">
          <a-select style="width: 100%;" v-model:value="formState.task_event_id" :options="state.optionsTaskEvent"
            placeholder="请选择任务事件" />
        </a-form-item>

        <a-form-item label="任务icon" name="image" :rules="[{ required: true, message: '请上传任务icon' }]">
          <SelectImg v-model:value="formState.image" :width-height="[72, 72]"></SelectImg>
        </a-form-item>

        <a-form-item label="任务描述" name="lang_key" :rules="[{ required: true, message: '请选择任务描述' }]">
          <SelectLang v-model:value="formState.lang_key" @initLabel="initLabel" @updateLabel="updateLangLabel" placeholder="请选择任务描述"></SelectLang>
        </a-form-item>

        <a-form-item label="任务说明" name="file">
          <UploadBtn
            v-has="'Operation'"
            ref="uploadBtn"
            @uploadSuccess="uploadSuccess"
            :downloadApi="exportTaskRule"
            fileType="task-config-rule-desc"
            :page="PAGE_CONF.TASKRULE"
            :hideDownloadBtn="formState.msg_template_code ? false : true"
            :downloadData="formState.msg_template_code ? { 'msg_template_code': formState.msg_template_code } : {}"
          />
        </a-form-item>

        <a-form-item style="display: none;" label="VIP等级" name="filterData.vip_restriction"
          :rules="rules['filterData.vip_restriction']">
          <a-select style="width: 100%;" v-model:value="formState.filterData.vip_restriction"
            :options="state.optionsVip" />
        </a-form-item>

        <a-form-item style="display: none;" label="游戏成长值" name="game_growth">
          <a-input-number style="width: 100%;" v-model:value="formState.filterData.game_growth" :min="0" :max="1500000"
            placeholder="请输入游戏成长值" />
        </a-form-item>

        <a-form-item style="display: none;" label="角色成长值" name="game_role_growth">
          <a-input-number style="width: 100%;" v-model:value="formState.filterData.game_role_growth" :min="0"
            :max="1500000" placeholder="请输入角色成长值"></a-input-number>
        </a-form-item>

        <!-- <a-form-item
          style="display: none;"
          v-for="(item, index) in formState.task_where_data"
          :class="index > 0 ? 'task_item' : ''"
          :key="item.key + index"
          :label="index === 0 ? '任务条件' : ''"
          :name="['task_where_data', index, 'event_key']"
          :rules="{
            required: true,
            message: '请选择任务条件',
            trigger: 'change',
          }"
        >
          <a-space nowrap class="space-wrapper space-one" style="gap: 5px;">
            <a-select style="width: 100%;" v-model:value="item.event_key" :options="state.optionsTaskEvent" placeholder="请选择任务事件" />

            <PlusCircleOutlined
              style="display: none;"
              class="dynamic-button dynamic-add-button"
              @click="addDomain"
            />
            <MinusCircleOutlined
             style="display: none;"
              v-if="formState.task_where_data.length > 1"
              class="dynamic-button dynamic-delete-button"
              @click="removeDomain(item)"
            />
          </a-space>
        </a-form-item> -->

        <a-form-item label="任务参数" name="progress" :rules="[{ required: true, message: '请输入任务参数' }]">
          <div style="display: flex;align-items: center;margin-bottom: 30px;">
            <span style="display: inline-block;width: 100px;"><b style="color:red;">*</b>progress:
            </span>
            <a-input-number style="width: 50%;" :disabled="state.progressKey > 0" v-model:value="formState.progress" :min="1" :max="9999" @change="handleChangeParams" />
            次（周期内任务进度）
          </div>
          <div v-for="(item, i) in formState.task_params_arr" :key="i" style="display: flex;align-items: center;margin-bottom: 30px;">
            <span style="display: inline-block;width: 100px;">{{ item.key + (i + 1) }}: </span>
            <a-input-number style="width: 50%;" v-model:value="item.value" :min="1" :max="9999" @change="handleChangeParams" />
            <a-radio-group
              style="margin-left: 10px;"
              v-model:value="state.progressKey"
              @change="handleStyleTypeChange(i, item.is_progress)"
            >
              <a-radio :value="item.is_progress">设为进度</a-radio>
            </a-radio-group>
          </div>

          <div v-if="formState.task_params_arr && formState.task_params_arr.length > 0">
            <strong>任务描述示例：</strong>

            {{ state.task_desc }}
          </div>
        </a-form-item>

        <a-form-item label="领奖设置" name="cycle_type" :rules="[{ required: true, message: '请选择领奖设置' }]">
          <a-space nowrap class="space-wrapper" style="gap: 5px;">
            <a-select style="width: 100%;" v-model:value="formState.dimension" :options="state.trigger1"
              placeholder="请选择维度" />
            <a-select v-model:value="formState.cycle_type" :options="state.trigger2" />
            <a-input-number style="width: 100%;" v-model:value="formState.cycle_times" :min="1" :max="1500000"
              placeholder="请输入周期次数" />次
          </a-space>
        </a-form-item>

        <a-form-item label="跳转链接" name="url" :rules="[{ required: true, message: '请输入跳转链接' }]">
          <a-space nowrap class="space-wrapper" style="gap: 5px;">
            <a-select v-model:value="formState.url_target" :options="state.jumpType" />
            <a-input style="width: 100%;" v-model:value="formState.url" placeholder="请输入跳转链接" />
          </a-space>
        </a-form-item>

        <a-form-item label="奖励类型" name="reward_type" :rules="[{ required: true, message: '请选择奖励类型' }]">
          <a-select style="width: 100%;" v-model:value="formState.reward_type" :options="state.rewardType" />
        </a-form-item>

        <a-form-item v-if="formState.reward_type === 2" label="积分数量" name="coin_num"
          :rules="[{ required: true, message: '请输入积分数量' }]">
          <a-input-number style="width: 100%;" v-model:value="formState.coin_num" :min="0" :max="1500000"
            placeholder="请输入积分数量" />
        </a-form-item>

        <a-form-item v-if="formState.reward_type !== 2" label="礼包名称（后台）" name="pkg_id"
          :rules="[{ required: true, message: '请选择礼包名称' }]">
          <a-select style="width: 100%;" allowClear :filter-option="false" showSearch v-model:value="formState.pkg_id" :options="state.optionsGiftsFilter"
            placeholder="请选择礼包名称" @change="handleGiftPackage" @search="fetchGiftPackage" />
        </a-form-item>

        <a-form-item label="任务有效期" @change="formState.times = undefined"
          :rules="[{ required: true, message: '请选择起止时间' }]">
          <a-radio-group v-model:value="formState.task_validity_type"
            :options="[{ label: '永久', value: 1 }, { label: '定时', value: 2 }]" />
        </a-form-item>
        <template v-if="formState.task_validity_type === 2">
          <a-form-item :wrapper-col="{ offset: 6, span: 16 }" name="times"
            :rules="[{ required: true, message: '请选择起止时间' }]">
            <SelectDateTime v-model:value="formState.times" :isDisabledDate="true"></SelectDateTime>
          </a-form-item>
        </template>

        <a-form-item v-if="!state.isOnline" :wrapper-col="{ offset: 10, span: 12 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.dynamic-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  margin: 0 5px;
  font-size: 24px;
  color: #1677ff;
  transition: all 0.3s;
}

.dynamic-button:hover {
  color: #4096ff;
}

.space-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;

  ::v-deep(.ant-space-item) {
    flex: 1;

    &:nth-child(4),
    &:nth-child(5),
    &:nth-child(6) {
      flex: 0;
      width: 50px;
    }
  }

  &.space-one {
    ::v-deep(.ant-space-item) {
      flex: 1;

      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6) {
        flex: 0;
        width: 50px;
      }
    }
  }

  &.space-two {
    ::v-deep(.ant-space-item) {
      flex: 1;

      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6) {
        flex: 0;
        width: 50px;
      }
    }
  }
}

.task_item {
  padding-left: 125px;

  ::v-deep(.ant-form-row) {
    width: 500px;

    .ant-col {
      max-width: 100% !important;
    }
  }
}
</style>
