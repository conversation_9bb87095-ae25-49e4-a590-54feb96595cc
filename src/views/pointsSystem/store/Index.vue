<script setup lang="ts">
import Form from "./Form.vue";
import { getPropsList, delProps, updatePropsStatus, downloadList, adjustStock } from "@/api/pointsStore";
import { langKeyDetail, getGiftPkgNumList, LangGetData } from "@/api/resource";
import { PAGE_CONF } from "@/enum";
import { messageConfirm } from "@/utils";
import { Modal } from "ant-design-vue";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
dayjs.extend(utc);

const GMAE_PROPS_COLUMNS_MAP = [
  { dataIndex: "id", key: "id", title: "商品ID", width: "100px" },
  // { dataIndex: 'package', key: 'package', title: '商品名称', width: '100px' },
  { dataIndex: "item_list", key: "item_list", title: "道具详情", width: "130px" },
  { dataIndex: "cost_coin", key: "cost_coin", title: "兑换积分", width: "130px" },
  { dataIndex: "product_type", key: "product_type", title: "商品类型", width: "130px" },
  { dataIndex: "vip", key: "vip", title: "等级限制", width: "130px" },
  { dataIndex: "game_growth", key: "game_growth", title: "游戏成长值", width: "130px" },
  { dataIndex: "cycle_shape", key: "cycle_shape", title: "领取维度", width: "130px" },
  { dataIndex: "cycle_type", key: "cycle_type", title: "周期", width: "130px" },
  { dataIndex: "cycle_times", key: "cycle_times", title: "周期内次数", width: "130px" },
  { dataIndex: "status", key: "status", title: "状态", width: "130px" },
  { dataIndex: "online_type", key: "online_type", title: "上线时间", width: "130px" },
  { dataIndex: "is_time_limit", key: "is_time_limit", title: "有效期", width: "130px" },
  { dataIndex: "stocks_num", key: "stocks_num", title: "兑换上限", width: "100px" },
  { dataIndex: "now_stocks", key: "now_stocks", title: "实时库存", width: "100px" },
  { dataIndex: "action", key: "action", title: "操作", width: "200px", fixed: "right", align: "center" }
];

const state = reactive({
  editVisible: false,
  editId: 0,
  searchParams: {
    task_category: null,
    vip_restriction: null
  } as any,
  previewOpen: false,
  previewData: {} as any,
  // 任务类型:1日常任务,2活跃任务,3成长任务,4游戏任务,
  optionsTaskCategory: [
    { label: "日常任务", value: 1 },
    { label: "活跃任务", value: 2 },
    { label: "成长任务", value: 3 },
    { label: "游戏任务", value: 4 }
  ],
  // VIP条件- LV1-LV7
  optionsVip: [
    { label: "LV1", value: 1 },
    { label: "LV2", value: 2 },
    { label: "LV3", value: 3 },
    { label: "LV4", value: 4 },
    { label: "LV5", value: 5 },
    { label: "LV6", value: 6 },
    { label: "LV7", value: 7 }
    // { label: 'LV8', value: 8 },
    // { label: 'LV9', value: 9 },
    // { label: 'LV10', value: 10 },
  ],
  optionsGift: [
    { label: "每日礼包", value: 1 },
    { label: "每周礼包", value: 2 },
    { label: "每月礼包", value: 3 },
    { label: "每年礼包", value: 4 },
    { label: "等级礼包", value: 5 },
    { label: "活动礼包", value: 6 }
  ],
  optionsGift1: [
    { label: "日", value: 1 },
    { label: "周", value: 2 },
    { label: "月", value: 3 },
    { label: "年", value: 4 },
    { label: "等级", value: 5 },
    { label: "终身", value: 6 }
  ],
  trigger1: [
    { label: "账号", value: 1 },
    { label: "角色", value: 2 }
  ],
  optionsGifts: [] as any[],
  optionsLang: [] as any[]
});

const switchLoading = ref<boolean[]>([]);

// 列表数据获取
const RefCustomTable = ref();
// 搜索
// const search = () => RefCustomTable.value.requestTableData(true)

const search = (noUpdate?: boolean) => RefCustomTable.value.requestTableData(!noUpdate);

onMounted(() => {
  getGiftPkgListFunc();
  langGetDataFunc({ page: 1, page_size: 1000 });
  getLangItem("task_desc");
});

const getGiftPkgListFunc = () => {
  getGiftPkgNumList().then((res: any) => {
    if (res && res.length > 0) {
      state.optionsGifts = res;
    }
  });
};

const langGetDataFunc = (params: any) => {
  LangGetData(params).then((res: any) => {
    console.log("LangGetData res", res);
    if (res.data && res.data.length > 0) {
      state.optionsLang = res.data.map((item: any) => ({
        label: item.zh_cn,
        value: item.key
      }));
    }
  });
};

const getLangItem = async (key: string) => {
  const res = await langKeyDetail({ key });
  console.log("res", res);
  const item = res?.zh_cn || "-";
  console.log("item", item);
  return item;
};

// 库存调整
const showAdjust = (record: any) => {
  state.previewData = record;
  state.previewOpen = true;
};

// 库存调整确认
const handleSaveAdjust = () => {
  const { id, change_stocks } = state.previewData;
  // 校验 change_stocks
  if (change_stocks < 0) {
    return Modal.error({
      title: "提示",
      content: "所选礼包内包含多种道具/积分，请重新选择"
    });
  }

  adjustStock({ id, now_stocks: change_stocks }).then(() => {
    state.previewOpen = false;
    search(true);
  });
};

// 编辑
const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow;
  state.editId = id || 0;
};

// 删除
const batchDel = (id: string) => messageConfirm(`删除后，用户将看不到该商品，请确认后删除`, delProps, { petId: id }).then(() => search());

const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true;
  record.status = 1 - val;
  const content = val === 1 ? "当前商品未上线，是否确认现在上线" : "当前商品已上线，是否确认现在下线？";

  Modal.confirm({
    title: "提示",
    content,
    okText: "确定",
    cancelText: "取消",
    onOk: () => {
      switchLoading.value[index] = false;
      updatePropsStatus(record.id, val).finally(() => search(true));
    },
    onCancel: () => {
      switchLoading.value[index] = false;
    }
  });
};
</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getPropsList" :params="state.searchParams"
    :columns="GMAE_PROPS_COLUMNS_MAP">
    <template #top>
      <a-space direction="vertical">
        <a-space wrap style="padding: 20px 0; gap: 20px">
          <UploadBtn v-has="'Operation'" ref="uploadBtn" @uploadSuccess="search" :downloadApi="downloadList"
            fileType="points-products" :page="PAGE_CONF.PRODUCTCONFIG" />
          <a-button type="primary" @click="showEdit(true)">
            <template #icon>
              <PlusOutlined />
            </template>
            新增商品
          </a-button>
        </a-space>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'package'">
        {{ record[column.key]?.name }}
      </template>
      <template v-if="column.key === 'item_list'">
        <!-- {{ getLangItem(record[column.key]) }} -->
        {{ record[column.key] && record[column.key].length > 0 ? record[column.key][0].name + "*" +
          record[column.key][0].num : "" }}
      </template>
      <template v-if="column.key === 'vip'">
        R{{ record.vip }} - R{{ record.vip_max }}
      </template>
      <template v-if="column.dataIndex === 'product_type'">
        {{ record[column.key] !== 1 ? '平台' : '游戏' }}
      </template>
      <template v-if="column.dataIndex === 'cycle_shape'">
        {{ state.trigger1.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.dataIndex === 'cycle_type'">
        {{ state.optionsGift1.find((item: any) => item.value === record[column.key])?.label }}
      </template>
      <template v-if="column.dataIndex === 'progress'">
        {{ record[column.key] ? record[column.key] : "无限制" }}
      </template>
      <template v-if="column.dataIndex === 'pkg_id'">
        {{ state.optionsGifts.find((item: any) => item.value == record[column.key])?.label }}
      </template>
      <template v-if="column.key === 'status'">
        <a-switch v-model:checked="record.status" :checkedValue="1" :unCheckedValue="0" checked-children="已上线"
          un-checked-children="未上线" :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"></a-switch>
      </template>

      <template v-if="column.dataIndex === 'online_type' && record[column.key] === 2">
        <a-tooltip :title="`${dayjs.utc(record.online_timing * 1000).format('YYYY-MM-DD HH:mm:ss')}`"
          placement="topLeft">{{
            dayjs.utc(record.online_timing * 1000).format("YYYY-MM-DD HH:mm:ss")
          }}</a-tooltip>
      </template>
      <template v-if="column.dataIndex === 'online_type' && record[column.key] === 1">立即上线</template>

      <template v-if="column.dataIndex === 'is_time_limit' && record[column.key] === 1">
        <a-tooltip :title="`${dayjs.utc(record.start_time * 1000).format('YYYY-MM-DD HH:mm:ss')} -
        ${dayjs.utc(record.end_time * 1000).format('YYYY-MM-DD HH:mm:ss')}`" placement="topLeft">
          定时
        </a-tooltip>
      </template>
      <template v-if="column.dataIndex === 'is_time_limit' && record[column.key] === 0"> 永久 </template>
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0" /></template>
          <a-typography-link :disabled="!record.stocks_num" @click="showAdjust(record)">库存调整</a-typography-link>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" danger @click="batchDel(record.id)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Form v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Form>

  <!-- 库存调整弹窗：标题-库存调整，描述-库存调整后立即生效，请确认修改后保存，表单，表单label：实时库存：xx -> 输入框 -->
  <a-modal class="preview-modal" v-model:visible="state.previewOpen" title="库存调整" @cancel="state.previewOpen = false"
    @ok="state.previewOpen = false">
    <div class="preview-desc">库存调整后立即生效，请确认修改后保存</div>
    <a-form-item label="实时库存" class="preview-item">
      <strong>{{ state.previewData.now_stocks }} </strong>
      <strong>→</strong>
      <a-input-number v-model:value="state.previewData.change_stocks" :min="0" :max="state.previewData.stocks_num" />
    </a-form-item>

    <!-- 取消 确认 -->
    <template #footer>
      <a-button key="back" @click="state.previewOpen = false"> 取消 </a-button>
      <a-button key="submit" type="primary" @click="handleSaveAdjust"> 确认 </a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.preview-modal {
  position: relative;

  .preview-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(55, 60, 82, 0.12);
  }

  .preview-title,
  .preview-date {
    font-size: 14px;
    color: #bebebe;
  }

  .preview-desc {
    color: #3d3d3d;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin: 20px 0;
  }

  .preview-item {
    margin-bottom: 20px;

    ::v-deep .ant-form-item-control-input-content {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .preview-btn {
    color: #1a1b1f;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 0;
    margin-top: 32px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    border-radius: 12px;
    border: 1px solid rgba(55, 60, 82, 0.18);
    cursor: pointer;
  }
}
</style>
