<script setup lang="ts">
import { createProps, getPropsDetail, updateProps } from "@/api/pointsStore";
import { getGiftPkgNumList, LangGetData } from "@/api/resource";
import { onMounted } from "vue";
import { useConfigStore } from "@/store";
import { Modal } from "ant-design-vue";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { setDefaultFilter } from "@/utils";
import { PointsStoreItem } from "@/api/pointsStore";
dayjs.extend(utc);

const props = defineProps(["editId"]);
const emits = defineEmits(["close", "refresh"]);

const { configState } = useConfigStore();

const open = ref(true);
const modalLoading = ref(false);
const getDetail = () => {
  modalLoading.value = true;
  getPropsDetail(props.editId)
    .then((res: any) => {
      console.log("store getPropsDetail res", res);
      formState.value = res;
      formState.value.gift_id = Number(res.gift_id);
      formState.value.item_limit_type = res.item_limit_type || 1;
      formState.value.stocks_num = res.stocks_num || 1;
      formState.value.online_timing_str = res.online_timing ? dayjs.utc(res.online_timing * 1000).format("YYYY-MM-DD HH:mm:ss") : "";
      if (res.is_time_limit === 1 && res.start_time && res.end_time) {
        formState.value.times = [res.start_time, res.end_time];
      } else {
        formState.value.times = undefined;
      }
    })
    .finally(() => (modalLoading.value = false));
};
if (props.editId) getDetail();

const formState = ref<PointsStoreItem>({
  id: 0,
  cycle_type: 1,
  cycle_shape: 1,
  cycle_times: 1,
  cost_coin: 0,
  vip: 1,
  vip_max: 1,
  game_growth: 0,
  role_growth: 0,
  gift_id: null,
  status: 0,
  is_time_limit: 0,
  times: undefined,
  use_stocks: 0,
  item_limit_type: 1,
  stocks_num: 1,
  use_desc_key: undefined,
  online_type: 1,
  online_timing: 0,
  online_timing_str: "",
  product_type: 1,
  is_filter: 0,
  f_s_ids: ""
});

const state = reactive({
  uploadLoading: false,
  optionsTaskCategory: [
    { label: "日常任务", value: 1 },
    { label: "活跃任务", value: 2 },
    { label: "成长任务", value: 3 },
    { label: "游戏任务", value: 4 }
  ],
  optionsGift: [
    { label: "每日礼包", value: 1 },
    { label: "每周礼包", value: 2 },
    { label: "每月礼包", value: 3 },
    { label: "每年礼包", value: 4 },
    { label: "等级礼包", value: 5 },
    { label: "活动礼包", value: 6 }
  ],
  optionsOne: [{ label: "系统通知", value: 1 }],
  optionsTwo: [] as any[],
  optionsVip: [
    { label: "R1", value: 1 },
    { label: "R2", value: 2 },
    { label: "R3", value: 3 },
    { label: "R4", value: 4 },
    { label: "R5", value: 5 },
    { label: "R6", value: 6 },
    { label: "R7", value: 7 }
    // { label: 'R8', value: 8 },
    // { label: 'R9', value: 9 },
    // { label: 'R10', value: 10 },
  ],
  jumpType: [
    { label: "跳转到特定页面", value: 0 },
    { label: "打开游戏", value: 2 }
  ],
  domains: [
    {
      value: "",
      key: Date.now()
    }
  ],
  trigger1: [
    { label: "按账号", value: 1 },
    { label: "按角色", value: 2 }
  ],
  trigger2: [
    { label: "每日领取", value: 1 },
    { label: "每周领取", value: 2 },
    { label: "每月领取", value: 3 },
    { label: "每年领取", value: 4 },
    { label: "当前等级领取", value: 5, disabled: true },
    { label: "终身领取", value: 6 }
  ],
  // 1-10
  progressList: [
    { label: "每次需完成1个行为", value: 1 },
    { label: "每次需完成2个行为", value: 2 },
    { label: "每次需完成3个行为", value: 3 },
    { label: "每次需完成4个行为", value: 4 },
    { label: "每次需完成5个行为", value: 5 },
    { label: "每次需完成6个行为", value: 6 },
    { label: "每次需完成7个行为", value: 7 },
    { label: "每次需完成8个行为", value: 8 },
    { label: "每次需完成9个行为", value: 9 },
    { label: "每次需完成10个行为", value: 10 }
  ],
  // 奖励类型:0平台道具奖励,1游戏道具奖励
  rewardType: [
    { label: "平台道具奖励", value: 0 },
    { label: "游戏道具奖励", value: 1 },
    { label: "积分", value: 2 }
  ],
  optionsGifts: [] as any[],
  optionsLang: [] as any[],
  optionsLangKey: [] as any[],
  optionsOriginTaskEvent: [] as any[],
  optionsTaskEvent: [] as any[],
  // 兑换限制类型：0=无兑换上限 1=每日兑换上限，2=每日兑换上限，3=每日兑换上限，4=每日兑换上限，5=终身兑换总限制上限
  stocksTypeList: [
    { label: "每日兑换上限", value: 1 },
    { label: "每周兑换上限", value: 2 },
    { label: "每月兑换上限", value: 3 },
    { label: "每年兑换上限", value: 4 },
    { label: "总兑换上限", value: 5 }
  ],
  productTypeList: [
    { label: "游戏", value: 1 },
    { label: "平台", value: 2 }
  ],
  giftsList: [] as any[]
});

// 校验规则
const rules = reactive({
  filterData: {
    task_where: [{ required: true, message: "请选择任务条件", trigger: "change" }]
  }
}) as any;

const submitLoading = ref(false);
const formRef = ref();

const disabledDate = (current: any) => {
  // Can not select days before today and today
  return current && current < dayjs().startOf("day");
};

onMounted(() => {
  getGiftPkgListFunc();
  langGetDataFunc({ page: 1, page_size: 1000 });
});

watch(
  () => configState,
  (value) => {
    // console.log('configState value 0000 ', value)
    if (value && value.task_events && value.task_events.length > 0) {
      state.optionsTaskEvent = value.task_events.map((item: any) => ({
        originLabel: item.label,
        label: item.label,
        value: item.value
      }));
    }
    // state.optionsTaskEvent = value ? value.task_events : []
  },
  { immediate: true }
);

const handleProductTypeChange = (value: any) => {
  formState.value.gift_id = null
  state.optionsGifts = state.giftsList.filter((item: any) => value === 1 ? item.game_project !== "funplus_zone" : item.game_project === "funplus_zone");
}

const getGiftPkgListFunc = () => {
  getGiftPkgNumList().then((res: any) => {
    if (res && res.length > 0) {
      state.giftsList = res
      state.optionsGifts = res.filter((item: any) => formState.value.product_type === 1 ? item.game_project !== "funplus_zone" : item.game_project === "funplus_zone");
    }
  });
};

const langGetDataFunc = (params: any) => {
  LangGetData(params).then((res: any) => {
    if (res.data && res.data.length > 0) {
      state.optionsLang = res.data.map((item: any) => ({
        label: item.zh_cn,
        value: item.key
      }));
      state.optionsLangKey = [...state.optionsLang];
    }
  });
};

const handleGiftPackage = (value: any) => {
  const item = state.optionsTwo.find((item: any) => item.value === value);
  console.log("handleGiftPackage item", item);
  if (item) {
  }
};

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true;
      const { id, ...data } = formState.value;
      data.game_growth = data.game_growth || 0;

      // 判断vip_max是否大于vip
      if (data.vip_max && data.vip_max < data.vip) {
        submitLoading.value = false;
        return Modal.error({
          title: "提示",
          content: "等级上限不能小于等级下限"
        });
      }
      // 判断state.optionsGifts中的id为data.gift_id的数据，如果item_list_length大于1，则提示“所选礼包内包含多种道具/积分，请重新选择”
      const item = state.optionsGifts.find((item: any) => item.id === data.gift_id);
      if (item && item.item_list_length > 1) {
        submitLoading.value = false;
        return Modal.error({
          title: "提示",
          content: "所选礼包内包含多种道具/积分，请重新选择"
        });
      }

      // 判断兑换积分为0，则提示“兑换所需积分不能为0”
      if (data.cost_coin === 0) {
        submitLoading.value = false;
        return Modal.error({
          title: "提示",
          content: "兑换所需积分不能为0"
        });
      }
      // // 判断游戏成长值为0，则提示“游戏成长值不能为0”
      // if (data.game_growth === 0) {
      //   submitLoading.value = false
      //   return Modal.error({
      //     title: '提示',
      //     content: '游戏成长值不能为0',
      //   })
      // }

      if (data.is_time_limit === 1 && data.times) {
        data.start_time = data.times[0];
        data.end_time = data.times[1];
      } else {
        data.start_time = 0;
        data.end_time = 0;
      }

      if (data.online_type === 2) {
        data.online_timing = data.online_timing_str ? dayjs.utc(data.online_timing_str).unix() : 0;
      } else {
        data.online_timing = 0;
      }

      // 商品类型选择平台时，判断是否存在说明，如果不存在，则提示“请选择使用说明”
      if (data.product_type === 2 && !data.use_desc_key) {
        submitLoading.value = false;
        return Modal.error({
          title: "提示",
          content: "请选择使用说明"
        });
      }
      if (props.editId) {
        // 判断当前状态是否已上线，如果是则二次提示，否则直接保存
        if (formState.value.status) {
          Modal.confirm({
            title: "提示",
            content: "当前商品已上线，保存后立即生效，请确认后操作",
            okText: "确定",
            cancelText: "取消",
            onOk: () => {
              updateProps(id, data)
                .then(() => {
                  emits("close");
                  emits("refresh", true);
                })
                .catch(() => { })
                .finally(() => {
                  submitLoading.value = false;
                });
            },
            onCancel: () => {
              submitLoading.value = false;
            }
          });
        } else {
          updateProps(id, data)
            .then(() => {
              emits("close");
              emits("refresh", true);
            })
            .catch(() => { })
            .finally(() => {
              submitLoading.value = false;
            });
        }
      } else {
        createProps(data)
          .then(() => {
            emits("close");
            emits("refresh");
          })
          .catch(() => { })
          .finally(() => {
            submitLoading.value = false;
          });
      }
      setTimeout(() => {
        submitLoading.value = false;
      }, 1000);
    })
    .catch(() => { });
};
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑商品' : '新增商品'" :maskClosable="false" :width="800"
    @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form :model="formState" :rules="rules" name="basic" ref="formRef" :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }" autocomplete="off">
        <a-form-item label="商品类型" name="product_type" :rules="[{ required: true, message: '请选择商品类型' }]">
          <a-select style="width: 100%" v-model:value="formState.product_type" @change="handleProductTypeChange"
            :options="state.productTypeList" />
        </a-form-item>
        <a-form-item label="商品名称" name="gift_id" :rules="[{ required: true, message: '请选择商品名称' }]">
          <a-select style="width: 100%" allowClear show-search :filter-option="filterOption"
            v-model:value="formState.gift_id" :options="state.optionsGifts" placeholder="请选择商品名称"
            @change="handleGiftPackage" />
        </a-form-item>

        <a-form-item label="兑换所需积分" name="cost_coin" :rules="[{ required: true, message: '请填写兑换所需积分' }]">
          <a-input-number style="width: 100%" v-model:value="formState.cost_coin" :min="1" :max="1500000"
            placeholder="请填写兑换所需积分" />
        </a-form-item>

        <a-form-item label="等级限制" name="vip" :rules="[{ required: true, message: '请选择等级限制' }]">
          <a-space style="width: 100%">
            <a-select style="width: 100px" v-model:value="formState.vip" :options="state.optionsVip" />
            <span style="margin: 0 10px;">-</span>
            <a-select style="width: 100px" v-model:value="formState.vip_max" :options="state.optionsVip" />
          </a-space>
        </a-form-item>

        <a-form-item label="游戏成长值" name="game_growth">
          <a-input-number style="width: 100%" v-model:value="formState.game_growth" placeholder="请输入游戏成长值" />
        </a-form-item>

        <a-form-item label="领取维度及周期" name="cycle_times" :rules="[{ required: true, message: '请填写领取维度及周期' }]">
          <a-space nowrap class="space-wrapper" style="gap: 5px">
            <a-select style="width: 100%" v-model:value="formState.cycle_shape" :options="state.trigger1" />

            <a-select style="width: 100%" v-model:value="formState.cycle_type" :options="state.trigger2" />

            <a-input-number style="width: 100%" v-model:value="formState.cycle_times" :min="1" :max="9999"
              placeholder="请填写次数" />次
          </a-space>
        </a-form-item>

        <a-form-item label="商品上线时间" @change="formState.times = undefined"
          :rules="[{ required: true, message: '请选择起止时间' }]">
          <a-radio-group v-model:value="formState.online_type" :options="[
            { label: '立即上线', value: 1 },
            { label: '定时上线', value: 2 }
          ]" />
        </a-form-item>
        <template v-if="formState.online_type === 2">
          <a-form-item class="stocks-item" :wrapper-col="{ offset: 6, span: 16 }" name="online_timing_str"
            :rules="[{ required: true, message: '请选择结束时间' }]">
            <a-date-picker style="width: 100%" v-model:value="formState.online_timing_str" format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate" :show-now="false" show-time />
          </a-form-item>
        </template>

        <a-form-item label="商品有效期" @change="formState.times = undefined"
          :rules="[{ required: true, message: '请选择起止时间' }]">
          <a-radio-group v-model:value="formState.is_time_limit" :options="[
            { label: '永久', value: 0 },
            { label: '定时', value: 1 }
          ]" />
        </a-form-item>
        <template v-if="formState.is_time_limit === 1">
          <a-form-item class="time-limit-item" :wrapper-col="{ offset: 6, span: 16 }" name="times"
            :rules="[{ required: true, message: '请选择起止时间' }]">
            <SelectDateTime v-model:value="formState.times as [number, number] | undefined"></SelectDateTime>
          </a-form-item>
        </template>

        <a-form-item label="是否限量兑换" :rules="[{ required: true, message: '请选择是否限量兑换' }]">
          <a-radio-group v-model:value="formState.use_stocks" :options="[
            { label: '否', value: 0 },
            { label: '是', value: 1 }
          ]" />
        </a-form-item>
        <template v-if="formState.use_stocks === 1">
          <a-form-item class="stocks-item" :wrapper-col="{ offset: 6, span: 16 }" name="item_limit_type"
            :rules="[{ required: true, message: '请选择兑换上限类型' }]">
            <a-select style="width: 50%" v-model:value="formState.item_limit_type" :options="state.stocksTypeList" />
            <a-input-number v-model:value="formState.stocks_num" :min="1" :max="9999" placeholder="请填写兑换上限" />
          </a-form-item>
        </template>

        <a-form-item label="筛选器" name="is_filter" :rules="[{ required: true, message: '请选择筛选器' }]">
          <a-radio-group v-model:value="formState.is_filter" @change="setDefaultFilter(formState, ['f_s_ids'])">
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>

        <template v-if="formState.is_filter === 1">
          <a-form-item label="服务器" name="f_s_ids" :rules="[{ required: true, message: '请输入服务器ID' }]">
            <a-textarea v-model:value="formState.f_s_ids" placeholder="请输入服务器ID，例如1,2-4,10,20-30" allow-clear />
          </a-form-item>
        </template>

        <a-form-item label="使用说明" name="use_desc_key">
          <SelectLang v-model:value="formState.use_desc_key" />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 10, span: 12 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.dynamic-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  margin: 0 5px;
  font-size: 24px;
  color: #1677ff;
  transition: all 0.3s;
}

.dynamic-button:hover {
  color: #4096ff;
}

.space-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;

  ::v-deep(.ant-space-item) {
    flex: 1;

    &:nth-child(4),
    &:nth-child(5),
    &:nth-child(6) {
      flex: 0;
      width: 50px;
    }
  }

  &.space-one {
    ::v-deep(.ant-space-item) {
      flex: 1;

      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6) {
        flex: 0;
        width: 50px;
      }
    }
  }

  &.space-two {
    ::v-deep(.ant-space-item) {
      flex: 1;

      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6) {
        flex: 0;
        width: 50px;
      }
    }
  }
}

.task_item {
  padding-left: 125px;

  ::v-deep(.ant-form-row) {
    width: 500px;

    .ant-col {
      max-width: 100% !important;
    }
  }
}

.time-limit-item,
.stocks-item {
  ::v-deep(.ant-form-item-control-input-content) {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
</style>
