<script setup lang="ts">
import Detail from "./Detail.vue";
import { messageConfirm } from "@/utils";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { VAJRA_COLUMNS_MAP, LINK_TYPE } from "../const";
import { copySDKVajra, delSDKVajra, getSDKVajraList, setSDKVajraPos, setSDKVajraStatus } from "@/api/operation";
import { Modal } from "ant-design-vue";
dayjs.extend(utc);

const state = reactive({
  editVisible: false,
  editId: 0
});

// 列表数据获取
const RefCustomTable = ref();
const search = () => RefCustomTable.value.requestTableData(true);

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow;
  state.editId = id || 0;
};

// 上下移动
const setOrder = (id: number, type: "up" | "down") =>
  messageConfirm(`确定要${type === "up" ? "上移" : "下移"}此条数据吗？`, setSDKVajraPos, { id, type }).then(() => search());
// 复制
const copy = (id: number) => messageConfirm(`确定要复制此条数据并生成一份新数据吗？`, copySDKVajra, { id }).then(() => search());
// 删除
const batchDel = (id: string) =>
  messageConfirm("删除后游戏内浮层将不展示该项，即刻生效，是否确认删除？", delSDKVajra, { id }).then(() => search());
// 状态切换
const switchLoading = ref<boolean[]>([]);
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true;
  record.status = 1 - val;

  // 判断是否在活动周期内
  // const now = Math.floor((dayjs.utc().valueOf()/1000))
  // if (record.status === 0 && record.offline_at && (now < record.online_at || now > record.offline_at)) {
  //   if (now < record.online_at) {
  //     message.warning('未到上线时间，请修改后操作')
  //   } else {
  //     message.warning('已过期，请修改上线时间后操作')
  //   }
  //   switchLoading.value[index] = false
  //   return
  // }
  Modal.confirm({
    title: "提示",
    content: val === 0 ? "关闭后游戏内浮层将不展示该项，即刻生效，是否确认关闭？" : "开启后游戏内浮层将展示该项，即刻生效，是否确认开启？",
    okText: "确定",
    cancelText: "取消",
    onOk: () => {
      switchLoading.value[index] = false;
      setSDKVajraStatus(record.id, val).finally(() => search());
    },
    onCancel: () => {
      switchLoading.value[index] = false;
    }
  });
};
</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getSDKVajraList" :params="{}" :columns="VAJRA_COLUMNS_MAP" :pagination="false">
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'img_id'">
        <a-image :src="record.img_id" :height="60" />
      </template>
      <template v-if="column.key === 'times'">
        {{
          record.offline_at
            ? `${dayjs.utc(record.online_at * 1000).format("YYYY-MM-DD HH:mm:ss")} - ${dayjs
                .utc(record.offline_at * 1000)
                .format("YYYY-MM-DD HH:mm:ss")}`
            : "永久"
        }}
      </template>
      <template v-if="column.key === 'entrance_name'">
        <LangKey :lang-key="record.entrance_name" :i18n_name="record.i18n_name"></LangKey>
      </template>
      <template v-if="column.key === 'status'">
        <a-switch
          v-model:checked="record.status"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="开启"
          un-checked-children="关闭"
          :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"
        ></a-switch>
      </template>
      <template v-if="column.key === 'link_type'">
        {{ record.link_type ? LINK_TYPE[record.link_type].label : "-" }}
      </template>
      <FilterCell v-if="column.key === 'filter'" :record="record" />
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0" /></template>
          <a-typography-link @click="setOrder(record.id, 'up')" :disabled="record.first"><ArrowUpOutlined />上移</a-typography-link>
          <a-typography-link @click="setOrder(record.id, 'down')" :disabled="record.last"><ArrowDownOutlined />下移</a-typography-link>
          <a-typography-link type="success" @click="copy(record.id)">复制</a-typography-link>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="batchDel(record.id)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Detail v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Detail>
</template>

<style lang="scss" scoped></style>
