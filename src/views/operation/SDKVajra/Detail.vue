<script setup lang="ts">
import { createSDKVajra, getSDKVajraDetail, updateSDKVajra } from '@/api/operation';
import dayjs from 'dayjs';
import { LINK_TYPE } from '../const';
import { setDefaultFilter } from '@/utils';

interface FormState {
  id: number
  entrance_name: string | undefined
  link_url: string
  link_type: string | undefined
  img_id: string
  is_forever: 0 | 1
  times: number[] | undefined
  is_red_dot: 0 | 1

  is_filter: 0 | 1
  f_os: string | undefined
  f_channel: string | undefined
  lang: string | undefined
  f_s_ids: string
  f_lv_ids: string
  [key: string]: any
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getSDKVajraDetail(props.editId)
    .then((res: any) => {
      formState.value = res
      if (res.online_at && res.offline_at) {
        formState.value.is_forever = 0
        formState.value.times = [res.online_at, res.offline_at]
      } else {
        formState.value.is_forever = 1
        formState.value.times = undefined
      }
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  entrance_name: undefined,
  link_type: undefined,
  link_url: '',
  img_id: '',
  is_forever: 0,
  times: undefined,
  is_red_dot: 0,
  is_filter: 0,
  f_os: '',
  f_channel: '',
  lang: '',
  f_s_ids: '',
  f_lv_ids: ''
})

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      if (data.is_forever === 0 && data.times) {
        data.online_at = data.times[0]
        data.offline_at = data.times[1]
      } else {
        data.online_at = dayjs.utc().unix()
        data.offline_at = ''
      }
      if (props.editId) {
        // 编辑
        updateSDKVajra(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        // 新增
        createSDKVajra(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
    .catch(() => {})
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑' : '新增'" :maskClosable="false" :width="600" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="入口名称" name="entrance_name" :rules="[{ required: true, message: '请选择入口名称多语言' }]">
          <SelectLang v-model:value="formState.entrance_name" placeholder="请选择入口名称多语言"></SelectLang>
        </a-form-item>
        <a-form-item label="跳转类型" name="link_type" :rules="[{ required: true, message: '请选择跳转类型' }]">
          <a-select v-model:value="formState.link_type" :options="Object.values(LINK_TYPE)" placeholder="请选择跳转类型"></a-select>
        </a-form-item>
        <a-form-item label="跳转URL" name="link_url" :rules="[{ required: true, message: '请输入跳转URL' }]">
          <a-input v-model:value="formState.link_url" placeholder="请输入跳转URL"></a-input>
        </a-form-item>
        <a-form-item label="入口icon" name="img_id" :rules="[{ required: true, message: '请上传入口icon' }]">
          <SelectImg v-model:value="formState.img_id" :width-height="[56, 56]"></SelectImg>
        </a-form-item>
        
        <a-form-item label="上线时间" @change="formState.times = undefined">
          <a-radio-group v-model:value="formState.is_forever" :options="[{ label: '永久', value: 1 }, { label: '定时', value: 0 }]"/>
        </a-form-item>
        <template v-if="formState.is_forever === 0">
          <a-form-item :wrapper-col="{ offset: 6, span: 16 }" name="times" :rules="[{ required: true, message: '请选择起止时间' }]">
            <SelectDateTime v-model:value="formState.times"></SelectDateTime>
          </a-form-item>
        </template>
        <a-form-item label="是否需要红点" name="is_red_dot">
          <a-radio-group v-model:value="formState.is_red_dot">
            <a-radio :value="1">需要</a-radio>
            <a-radio :value="0">不需要</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="筛选器" name="is_filter" :rules="[{ required: true, message: '请选择筛选器' }]">
          <a-radio-group v-model:value="formState.is_filter" @change="setDefaultFilter(formState, ['f_os', 'f_channel', 'lang', 'f_s_ids', 'f_lv_ids'])">
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
        <template v-if="formState.is_filter === 1">
          <a-form-item label="操作系统" name="f_os" :rules="[{ required: true, message: '请选择操作系统' }]">
            <SelectWithAllComp v-model:value="formState.f_os" placeholder="请选择操作系统" type="platform"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="语种" name="lang" :rules="[{ required: true, message: '请选择语种' }]">
            <SelectWithAllComp v-model:value="formState.lang" placeholder="请选择语种" type="langs"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="渠道" name="f_channel" :rules="[{ required: true, message: '请选择渠道' }]">
            <SelectWithAllComp v-model:value="formState.f_channel" placeholder="请选择渠道" type="channels"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="服务器" name="f_s_ids" :rules="[{ required: true, message: '请输入服务器ID' }]">
            <a-textarea v-model:value="formState.f_s_ids" placeholder="请输入服务器ID，例如1,2-4,10,20-30" allow-clear />
          </a-form-item>
          <a-form-item label="城堡等级" name="f_lv_ids" :rules="[{ required: true, message: '请输入城堡等级' }]">
            <a-textarea v-model:value="formState.f_lv_ids" placeholder="请输入城堡等级，例如1,7-9999" allow-clear />
          </a-form-item>
        </template>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>

</style>