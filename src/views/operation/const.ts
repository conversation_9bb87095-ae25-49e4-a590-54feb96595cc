export const VAJRA_COLUMNS_MAP = [
  { dataIndex: "id", key: "id", title: "ID", width: "50px" },
  { dataIndex: "list_order", key: "list_order", title: "排序", width: "50px" },
  { dataIndex: "entrance_name", key: "entrance_name", title: "入口名称", width: "200px" },
  { dataIndex: "img_id", key: "img_id", title: "Icon", width: "100px" },
  { dataIndex: "status", key: "status", title: "状态", width: "80px" },
  { dataIndex: "link_type", key: "link_type", title: "跳转类型", width: "100px" },
  { dataIndex: "link_url", key: "link_url", title: "跳转链接", width: "200px" },
  { dataIndex: "times", key: "times", title: "上线时间", width: "160px" },
  { dataIndex: "filter", key: "filter", title: "筛选器", width: "60px" },
  { dataIndex: "action", key: "action", title: "操作", width: "260px", fixed: "right", align: "center" }
];

export const ICONSPUSH_COLUMNS_MAP = [
  { dataIndex: "id", key: "id", title: "id", width: "50px" },
  { dataIndex: "name", key: "name", title: "后台名称", width: "200px" },
  { dataIndex: "img_url", key: "img_url", title: "角标预览", width: "100px" },
  { dataIndex: "is_online", key: "is_online", title: "状态", width: "80px" },
  { dataIndex: "target_url", key: "target_url", title: "跳转链接", width: "200px" },
  { dataIndex: "validity_type", key: "validity_type", title: "上线时间", width: "160px" },
  { dataIndex: "weight", key: "weight", title: "权重", width: "60px" },
  { dataIndex: "action", key: "action", title: "操作", width: "260px", fixed: "right", align: "center" }
];

export const ADS_COLUMNS_MAP = [
  { dataIndex: "id", key: "id", title: "id", width: "50px" },
  { dataIndex: "name", key: "name", title: "后台名称", width: "200px" },
  { dataIndex: "img_lang_type", key: "img_lang_type", title: "banner图", width: "100px" },
  { dataIndex: "is_online", key: "is_online", title: "状态", width: "80px" },
  { dataIndex: "target_url", key: "target_url", title: "跳转链接", width: "200px" },
  { dataIndex: "validity_type", key: "validity_type", title: "上线时间", width: "160px" },
  { dataIndex: "action", key: "action", title: "操作", width: "260px", fixed: "right", align: "center" }
];

export const LINK_TYPE: Record<any, { label: string; value: number }> = {
  "1": {
    label: "私域内跳",
    value: 1
  },
  "2": {
    label: "打开新页面",
    value: 2
  }
};

export const BANNER_LINK_TYPE: Record<any, { label: string; value: number }> = {
  "1": {
    label: "站内文章",
    value: 1
  },
  "2": {
    label: "站内H5",
    value: 2
  },
  "3": {
    label: "站外链接",
    value: 3
  }
};

export const BANNER_COLUMNS_MAP = [
  { dataIndex: "id", key: "id", title: "组id", width: "50px" },
  { dataIndex: "group_name", key: "group_name", title: "组名称", width: "100px" },
  { dataIndex: "style_type", key: "style_type", title: "样式", width: "100px" },
  { dataIndex: "status", key: "status", title: "状态", width: "80px" },
  { dataIndex: "show_page", key: "show_page", title: "展示页面", width: "200px" },
  { dataIndex: "times", key: "times", title: "上线时间", width: "160px" },
  { dataIndex: "action", key: "action", title: "操作", width: "190px", fixed: "right", align: "center" }
];

export const BANNER_DETAIL_COLUMNS_MAP = [
  { dataIndex: "id", key: "id", title: "ID", width: "50px" },
  { dataIndex: "title", key: "title", title: "名称", width: "200px" },
  { dataIndex: "img_id", key: "img_id", title: "banner图", width: "150px" },
  { dataIndex: "status", key: "status", title: "状态", width: "80px" },
  { dataIndex: "type", key: "type", title: "跳转类型", width: "100px" },
  { dataIndex: "link_url", key: "link_url", title: "跳转链接", width: "200px" },
  { dataIndex: "times", key: "times", title: "上线时间", width: "160px" },
  { dataIndex: "action", key: "action", title: "操作", width: "220px", fixed: "right", align: "center" }
];
