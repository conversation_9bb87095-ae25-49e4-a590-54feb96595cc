<script setup lang="ts">
import { createBanner, getBannerDetail, updateBanner } from '@/api/operation';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
import { ACT_BANNER_STYLE_TYPES } from '@/enum';
import { storeToRefs } from 'pinia';
import { useConfigStore } from '@/store';
dayjs.extend(utc)

interface FormState {
  id: number
  group_name: string
  group_title: string | undefined
  style_type: number
  show_page: Array<string> | string
  is_forever: 0 | 1
  times: number[] | undefined

  is_filter: 0 | 1
  f_os: string | undefined
  f_channel: string | undefined
  lang: string | undefined
  f_s_ids: string
  f_lv_ids: string
  [key: string]: any
}

const props = defineProps(['editId'])
const emits = defineEmits(['close', 'refresh'])
const { configState } = storeToRefs(useConfigStore())

const open = ref(true)
const modalLoading = ref(false)
const getDetail = () => {
  modalLoading.value = true
  getBannerDetail(props.editId)
    .then((res: any) => {
      formState.value = { ...formState.value, ...res }
      formState.value.show_page = res.show_page.split('|')
      if (res.online_at && res.offline_at) {
        formState.value.is_forever = 0
        formState.value.times = [res.online_at, res.offline_at]
      } else {
        formState.value.is_forever = 1
        formState.value.times = undefined
      }
    })
    .finally(() => modalLoading.value = false)
}
if (props.editId) getDetail()

const formState = ref<FormState>({
  id: 0,
  group_name: '',
  group_title: undefined,
  style_type: 1,
  show_page: [],
  is_forever: 0,
  times: undefined,
  is_filter: 0,
  f_os: '',
  f_channel: '',
  lang: '',
  f_s_ids: '',
  f_lv_ids: ''
})

const submitLoading = ref(false)
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const { id, ...data } = formState.value
      data.show_page = typeof data.show_page !== 'string' ? data.show_page.join('|') : data.show_page
      console.log('data.times', data.times)
      if (data.is_forever === 0 && data.times) {
        data.online_at = data.times[0]
        data.offline_at = data.times[1]
      } else {
        data.online_at = 0
        data.offline_at = 0
      }
      if (props.editId) {
        // 编辑
        updateBanner(id, data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        // 新增
        createBanner(data)
          .then(() => {
            emits('close')
            emits('refresh')
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
    .catch(() => {})
}
</script>

<template>
  <a-drawer v-model:open="open" :title="props.editId ? '编辑' : '新增'" :maskClosable="false" :width="600" @afterOpenChange="(open: boolean) => !open && emits('close')">
    <a-spin :spinning="modalLoading">
      <a-form
        :model="formState"
        name="basic"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="组名称" name="group_name" :rules="[{ required: true, message: '请输入组名称' }]">
          <a-input v-model:value="formState.group_name" placeholder="请输入banner组名称，仅后台展示用"></a-input>
        </a-form-item>
        <a-form-item label="样式">
          <div v-for="(item, index) in ACT_BANNER_STYLE_TYPES" :key="index" style="line-height: 32px;margin-bottom: 5px;">
            <a-radio-group v-model:value="formState.style_type" @change="() => formState.group_title = undefined">
              <a-radio :value="item.value">
                {{ item.label }}
                <a-image :src="item.img" :preview="false" style="height: 100px; border: 1px solid #ccc;border-radius: 5px;"></a-image>
              </a-radio>
            </a-radio-group>
          </div>
        </a-form-item>

        <a-form-item v-if="formState.style_type === 3" label="标题" name="group_title" :rules="[{ required: true, message: '请选择标题' }]">
          <SelectLang v-model:value="formState.group_title"></SelectLang>
        </a-form-item>

        <a-form-item label="展示页面" name="show_page" :rules="[{ required: true, message: '请选择展示页面' }]">
          <a-checkbox-group v-model:value="formState.show_page">
            <a-checkbox v-for="(item, index) in configState.show_pages" :key="index" :value="item.value" name="show_page">{{ item.label }}</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="上线时间" @change="formState.times = undefined">
          <a-radio-group v-model:value="formState.is_forever" :options="[{ label: '永久', value: 1 }, { label: '定时', value: 0 }]"/>
        </a-form-item>
        <template v-if="formState.is_forever === 0">
          <a-form-item :wrapper-col="{ offset: 6, span: 16 }" name="times" :rules="[{ required: true, message: '请选择起止时间' }]">
            <SelectDateTime v-model:value="formState.times"></SelectDateTime>
          </a-form-item>
        </template>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>

</style>
