<script setup lang="ts">
import DetailForm from "./DetailForm.vue";
import { messageConfirm } from "@/utils";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { BANNER_DETAIL_COLUMNS_MAP, BANNER_LINK_TYPE } from "../const";
import { delBannerItem, getBannerDetail, getBannerItemList, setBannerItemPos, setBannerItemStatus } from "@/api/operation";
import { Modal } from "ant-design-vue";
import { useRoute } from "vue-router";
import { ACT_BANNER_STYLE_TYPES } from "@/enum";
import { useConfigStore } from "@/store";
dayjs.extend(utc);

const route = useRoute();
const state = reactive({
  editVisible: false,
  editId: 0,
  style_type: 0,
  group_id: (route.params.id && Number(route.params.id)) || 0,
  bannerInfo: {} as any
});

const { getConfItem } = useConfigStore();

const getBannerInfo = () => {
  getBannerDetail(state.group_id).then((res) => {
    state.bannerInfo = res;
    state.style_type = res.style_type;
  });
};
getBannerInfo();

// 列表数据获取
const RefCustomTable = ref();
const search = () => RefCustomTable.value.requestTableData(true);

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow;
  state.editId = id || 0;
};

// 上下移动
const setOrder = (id: number, type: "up" | "down") =>
  messageConfirm(`确定要${type === "up" ? "上移" : "下移"}此条数据吗？`, setBannerItemPos, {
    id,
    params: { type, group_id: state.group_id }
  }).then(() => search());
// 删除
const batchDel = (id: string, isBatch: boolean) =>
  messageConfirm(`确定要删除${isBatch ? "选中的" : "此条"}数据吗？`, delBannerItem, { id }).then(() => search());
// 状态切换
const switchLoading = ref<boolean[]>([]);
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true;
  record.status = 1 - val;

  // 判断是否在活动周期内
  // const now = Math.floor((dayjs.utc().valueOf()/1000))
  // if (record.status === 0 && record.offline_at && (now < record.online_at || now > record.offline_at)) {
  //   if (now < record.online_at) {
  //     message.warning('未到上线时间，请修改后操作')
  //   } else {
  //     message.warning('已过期，请修改上线时间后操作')
  //   }
  //   switchLoading.value[index] = false
  //   return
  // }
  Modal.confirm({
    title: "提示",
    content: "确定要切换此条数据状态吗？",
    okText: "确定",
    cancelText: "取消",
    onOk: () => {
      switchLoading.value[index] = false;
      setBannerItemStatus(record.id, val).finally(() => search());
    },
    onCancel: () => {
      switchLoading.value[index] = false;
    }
  });
};
</script>

<template>
  <a-card>
    <a-space size="large">
      <a-typography-text strong>{{ state.bannerInfo.group_name }}(ID: {{ state.bannerInfo.id }})</a-typography-text>
      <a-space>
        <a-typography-text strong>展示页面：</a-typography-text>
        <a-tag v-for="(item, i) in (state.bannerInfo.show_page || '').split('|')" :key="i">{{
          getConfItem("show_pages", item)?.label || item
        }}</a-tag>
      </a-space>
    </a-space>
  </a-card>
  <CustomTable
    ref="RefCustomTable"
    :data-api="getBannerItemList"
    :params="{ group_id: state.group_id }"
    :columns="BANNER_DETAIL_COLUMNS_MAP"
    :pagination="false"
  >
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'img_id'">
        <a-image :src="record.img_id" :height="60" />
      </template>
      <template v-if="column.key === 'times'">
        {{
          record.offline_at
            ? `${dayjs.utc(record.online_at * 1000).format("YYYY-MM-DD HH:mm:ss")} - ${dayjs
                .utc(record.offline_at * 1000)
                .format("YYYY-MM-DD HH:mm:ss")}`
            : "永久"
        }}
      </template>
      <template v-if="column.key === 'status'">
        <a-switch
          v-model:checked="record.status"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="开启"
          un-checked-children="关闭"
          :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"
        ></a-switch>
      </template>
      <template v-if="column.key === 'title'">
        <LangKey :lang-key="record.title" :key="record.title" :i18n_name="record.i18n_name"></LangKey>
      </template>
      <template v-if="column.key === 'type'">
        {{ record.type ? BANNER_LINK_TYPE[record.type].label : "-" }}
      </template>
      <FilterCell v-if="column.key === 'filter'" :record="record" />
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0" /></template>
          <a-typography-link @click="setOrder(record.id, 'up')" :disabled="record.first"><ArrowUpOutlined />上移</a-typography-link>
          <a-typography-link @click="setOrder(record.id, 'down')" :disabled="record.last"><ArrowDownOutlined />下移</a-typography-link>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="batchDel(record.id, false)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <DetailForm
    v-if="state.editVisible"
    :edit-id="state.editId"
    @close="showEdit(false)"
    @refresh="search"
    :style-config="ACT_BANNER_STYLE_TYPES.filter((item) => item.value === state.style_type)[0]"
  ></DetailForm>
</template>

<style lang="scss" scoped></style>
