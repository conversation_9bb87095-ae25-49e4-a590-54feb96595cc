<script setup lang="ts">
import IndexForm from "./IndexForm.vue";
import { messageConfirm } from "@/utils";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { BANNER_COLUMNS_MAP } from "../const";
import { copyBanner, delBanner, getBannerList, setBannerStatus } from "@/api/operation";
import { ACT_BANNER_STYLE_TYPES } from "@/enum";
import { useRouter } from "vue-router";
import { useConfigStore } from "@/store";
import { Modal, message } from "ant-design-vue";
dayjs.extend(utc);

const state = reactive({
  editVisible: false,
  editId: 0
});
const router = useRouter();
const { getConfItem } = useConfigStore();

// 列表数据获取
const RefCustomTable = ref();
const search = () => RefCustomTable.value.requestTableData(true);

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow;
  state.editId = id || 0;
};

// 复制
const copy = (id: number) => messageConfirm(`确定要复制此条数据并生成一份新数据吗？`, copyBanner, { id }).then(() => search());
// 删除
const batchDel = (id: string, isBatch: boolean) =>
  messageConfirm(`确定要删除${isBatch ? "选中的" : "此条"}数据吗？`, delBanner, { id }).then(() => search());
// 状态切换
const switchLoading = ref<boolean[]>([]);
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true;
  record.status = 1 - val;

  // 判断是否在活动周期内
  const now = Math.floor(dayjs.utc().valueOf() / 1000);
  if (record.status === 0 && record.offline_at && now > record.offline_at) {
    message.warning("已过期，请修改上线时间后操作");
    switchLoading.value[index] = false;
    return;
  }
  Modal.confirm({
    title: "提示",
    content: "确定要切换此条数据状态吗？",
    okText: "确定",
    cancelText: "取消",
    onOk: () => {
      switchLoading.value[index] = false;
      setBannerStatus(record.id, val).finally(() => search());
    },
    onCancel: () => {
      switchLoading.value[index] = false;
    }
  });
};
</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getBannerList" :params="{}" :columns="BANNER_COLUMNS_MAP">
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'img_id'">
        <a-image :src="record.img_id" :height="60" />
      </template>
      <template v-if="column.key === 'times'">
        {{
          record.offline_at
            ? `${dayjs.utc(record.online_at * 1000).format("YYYY-MM-DD HH:mm:ss")} - ${dayjs
                .utc(record.offline_at * 1000)
                .format("YYYY-MM-DD HH:mm:ss")}`
            : "永久"
        }}
      </template>
      <template v-if="column.key === 'style_type'">
        <template v-if="ACT_BANNER_STYLE_TYPES.filter((i) => i.value === record.style_type)[0]">
          <a-popover placement="right">
            <template #content>
              <img :src="ACT_BANNER_STYLE_TYPES.filter((i) => i.value === record.style_type)[0].img" alt="" />
            </template>
            <a-typography-link>{{ ACT_BANNER_STYLE_TYPES.filter((i) => i.value === record.style_type)[0].label }}</a-typography-link>
          </a-popover>
        </template>
        <template v-else>{{ record.style_type || "-" }}</template>
      </template>
      <template v-if="column.key === 'status'">
        <a-switch
          v-model:checked="record.status"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="开启"
          un-checked-children="关闭"
          :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"
        ></a-switch>
      </template>
      <template v-if="column.key === 'show_page'">
        <a-tag v-for="(item, i) in record.show_page.split('|')" :key="i">{{ getConfItem("show_pages", item)?.label || item }}</a-tag>
      </template>
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0" /></template>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="success" @click="copy(record.id)">复制</a-typography-link>
          <a-typography-link @click="router.push(`/operation/banner/detail/${record.id}`)">详情</a-typography-link>
          <a-typography-link type="danger" @click="batchDel(record.id, false)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <IndexForm v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></IndexForm>
</template>

<style lang="scss" scoped></style>
