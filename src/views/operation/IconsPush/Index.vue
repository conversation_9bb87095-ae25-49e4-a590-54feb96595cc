<script setup lang="ts">
import Detail from "./Detail.vue";
import { messageConfirm } from "@/utils";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { ICONSPUSH_COLUMNS_MAP, LINK_TYPE } from "../const";
import { delSDKIconsPush, getSDKIconsPushList, setSDKIconsPushStatus } from "@/api/operation";
import { Modal } from "ant-design-vue";
dayjs.extend(utc);

const state = reactive({
  editVisible: false,
  editId: 0
});

// 列表数据获取
const RefCustomTable = ref();
const search = () => RefCustomTable.value.requestTableData(true);

const showEdit = (isShow: boolean, id?: number) => {
  state.editVisible = isShow;
  state.editId = id || 0;
};

// 删除
const batchDel = (id: string) =>
  messageConfirm("删除后游戏内将不展示该项，即刻生效，是否确认删除？", delSDKIconsPush, { id }).then(() => search());
// 状态切换
const switchLoading = ref<boolean[]>([]);
const beforeSwitch = (val: number, record: any, index: number) => {
  switchLoading.value[index] = true;
  record.is_online = 1 - val;

  Modal.confirm({
    title: "提示",
    content: "切换状态后即刻生效，是否确认操作？",
    okText: "确定",
    cancelText: "取消",
    onOk: () => {
      switchLoading.value[index] = false;
      setSDKIconsPushStatus(record.id, val).finally(() => search());
    },
    onCancel: () => {
      switchLoading.value[index] = false;
    }
  });
};
</script>

<template>
  <CustomTable ref="RefCustomTable" :data-api="getSDKIconsPushList" :params="{}" :columns="ICONSPUSH_COLUMNS_MAP" :pagination="false">
    <template #leftTool>
      <a-button type="primary" @click="showEdit(true)">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
    </template>
    <template #bodyCell="{ record, column, index }">
      <template v-if="column.key === 'img_url'">
        <a-image :src="record.img_url" :height="60" />
      </template>
      <template v-if="column.key === 'validity_type'">
        {{
          record.validity_type === 2
            ? `${dayjs.utc(record.start_time * 1000).format("YYYY-MM-DD HH:mm:ss")} - ${dayjs
                .utc(record.end_time * 1000)
                .format("YYYY-MM-DD HH:mm:ss")}`
            : "永久"
        }}
      </template>
      <template v-if="column.key === 'is_online'">
        <a-switch
          v-model:checked="record.is_online"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="开启"
          un-checked-children="关闭"
          :loading="switchLoading[index]"
          @click="(val: number) => beforeSwitch(val, record, index)"
        ></a-switch>
      </template>
      <template v-if="column.key === 'link_type'">
        {{ record.link_type ? LINK_TYPE[record.link_type].label : "-" }}
      </template>
      <FilterCell v-if="column.key === 'filter'" :record="record" />
      <template v-if="column.key === 'action'">
        <a-space>
          <template #split><a-divider type="vertical" style="margin: 0" /></template>
          <a-typography-link @click="showEdit(true, record.id)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="batchDel(record.id)">删除</a-typography-link>
        </a-space>
      </template>
    </template>
  </CustomTable>

  <Detail v-if="state.editVisible" :edit-id="state.editId" @close="showEdit(false)" @refresh="search"></Detail>
</template>

<style lang="scss" scoped></style>
