<script setup lang="ts">
import { createSDKIconsPush, getSDKIconsPushDetail, updateSDKIconsPush } from "@/api/operation";
import dayjs from "dayjs";
import { setDefaultFilter } from "@/utils";

interface FormState {
  id: number;
  name: string | undefined;
  target_url: string;
  validity_type: 1 | 2; // 1: 永久 2: 定时
  start_time: number | string | undefined;
  end_time: number | string | undefined;
  weight: number;
  img_url: string;
  img_id: string;
  is_forever: 0 | 1;
  times: number[] | undefined;

  is_filter: 0 | 1;
  f_os: string | undefined;
  f_channel: string | undefined;
  f_lang: string | undefined;
  f_s_ids: string;
  f_lv_ids: string;
  [key: string]: any;
}

const props = defineProps(["editId"]);
const emits = defineEmits(["close", "refresh"]);

const open = ref(true);
const modalLoading = ref(false);
const getDetail = () => {
  modalLoading.value = true;
  getSDKIconsPushDetail(props.editId)
    .then((res: any) => {
      formState.value = res;
      if (res.validity_type === 2) {
        formState.value.times = [res.start_time, res.end_time];
      } else {
        formState.value.times = undefined;
      }
    })
    .finally(() => (modalLoading.value = false));
};
if (props.editId) getDetail();

const formState = ref<FormState>({
  id: 0,
  name: undefined,
  target_url: "",
  validity_type: 1,
  start_time: undefined,
  end_time: undefined,
  weight: 0,
  img_url: "",
  img_id: "",
  is_forever: 0,
  times: undefined,
  is_filter: 0,
  f_os: "",
  f_channel: "",
  f_lang: "",
  f_s_ids: "",
  f_lv_ids: ""
});

const submitLoading = ref(false);
const formRef = ref();
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true;
      const { id, ...data } = formState.value;
      if (data.validity_type === 2 && data.times) {
        data.start_time = data.times[0];
        data.end_time = data.times[1];
      } else {
        data.start_time = dayjs.utc().unix();
        data.end_time = 0;
      }
      if (props.editId) {
        // 编辑
        updateSDKIconsPush(id, data)
          .then(() => {
            emits("close");
            emits("refresh");
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false;
          });
      } else {
        // 新增
        createSDKIconsPush(data)
          .then(() => {
            emits("close");
            emits("refresh");
          })
          .catch(() => {})
          .finally(() => {
            submitLoading.value = false;
          });
      }
    })
    .catch(() => {});
};
</script>

<template>
  <a-drawer
    v-model:open="open"
    :title="props.editId ? '编辑' : '新增'"
    :maskClosable="false"
    :width="600"
    @afterOpenChange="(open: boolean) => !open && emits('close')"
  >
    <a-spin :spinning="modalLoading">
      <a-form :model="formState" name="name" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" autocomplete="off">
        <a-form-item label="后台名称" name="name" :rules="[{ required: true, message: '请输入后台名称' }]">
          <a-input allowClear v-model:value="formState.name" placeholder="请输入后台名称"></a-input>
        </a-form-item>
        <a-form-item label="跳转URL" name="target_url" :rules="[{ required: true, message: '请输入跳转URL' }]">
          <a-input allowClear v-model:value="formState.target_url" placeholder="请输入跳转URL"></a-input>
        </a-form-item>
        <a-form-item label="上线时间" @change="formState.times = undefined">
          <a-radio-group
            v-model:value="formState.validity_type"
            :options="[
              { label: '永久', value: 1 },
              { label: '定时', value: 2 }
            ]"
          />
        </a-form-item>
        <template v-if="formState.validity_type === 2">
          <a-form-item :wrapper-col="{ offset: 6, span: 16 }" name="times" :rules="[{ required: true, message: '请选择起止时间' }]">
            <SelectDateTime v-model:value="formState.times"></SelectDateTime>
          </a-form-item>
        </template>

        <a-form-item label="权重" name="weight" :rules="[{ required: true, message: '请输入权重' }]">
          <a-input-number style="width: 100%" v-model:value="formState.weight" :min="0" :max="1500000" placeholder="请输入权重" />
        </a-form-item>

        <a-form-item label="图片配置" name="img_url" :rules="[{ required: true, message: '请上传图片配置' }]">
          <SelectImg v-model:value="formState.img_url" :width-height="[40, 40]" :tips="'png格式，建议尺寸比例: '"></SelectImg>
        </a-form-item>

        <a-form-item label="筛选器" name="is_filter" :rules="[{ required: true, message: '请选择筛选器' }]">
          <a-radio-group
            v-model:value="formState.is_filter"
            @change="setDefaultFilter(formState, ['f_os', 'f_channel', 'lang', 'f_s_ids', 'f_lv_ids'])"
          >
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
        <template v-if="formState.is_filter === 1">
          <a-form-item label="操作系统" name="f_os" :rules="[{ required: true, message: '请选择操作系统' }]">
            <SelectWithAllComp v-model:value="formState.f_os" placeholder="请选择操作系统" type="platform"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="语种" name="f_lang" :rules="[{ required: true, message: '请选择语种' }]">
            <SelectWithAllComp v-model:value="formState.f_lang" placeholder="请选择语种" type="langs"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="渠道" name="f_channel" :rules="[{ required: true, message: '请选择渠道' }]">
            <SelectWithAllComp v-model:value="formState.f_channel" placeholder="请选择渠道" type="channels"></SelectWithAllComp>
          </a-form-item>
          <a-form-item label="服务器" name="f_s_ids" :rules="[{ required: true, message: '请输入服务器ID' }]">
            <a-textarea v-model:value="formState.f_s_ids" placeholder="请输入服务器ID，例如1,2-4,10,20-30" allow-clear />
          </a-form-item>
          <a-form-item label="城堡等级" name="f_lv_ids" :rules="[{ required: true, message: '请输入城堡等级' }]">
            <a-textarea v-model:value="formState.f_lv_ids" placeholder="请输入城堡等级，例如1,7-9999" allow-clear />
          </a-form-item>
        </template>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" @click="submit" :loading="submitLoading">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped></style>
