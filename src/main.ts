import { createApp, h } from "vue";
import App from "./App.vue";
import router from "./router";
import { loadAllPlugins } from "./plugins";
import store from "./store";
import "@/router/permission";
import defineComponents from "./utils/defineComponents";
import formItems from "@/components/form-item";
import tableCell from "@/components/table-cell";
import directives from "./directives";
import VueKinesis from "vue-kinesis";

import "@/styles/reset.scss";

let app: any = null;
app = createApp({
  render: () => h(App)
});

// 安装插件
loadAllPlugins(app);

console.log("发布", 20241231);

app.use(store).use(router).use(directives).use(defineComponents).use(formItems).use(tableCell).use(VueKinesis);

app.mount("#app");
