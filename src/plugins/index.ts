import { createApp } from 'vue'

/**
 * @description 加载所有 Plugins
 * @param  {ReturnType<typeofcreateApp>} app 整个应用的实例
 */
export function loadAllPlugins (app: ReturnType<typeof createApp>) {
  const files = import.meta.glob('./*/index.ts', { eager: true })
  for (const key in files) {
    const defaultExport = (files[key] as any).default
    if (typeof defaultExport === 'function') {
      defaultExport(app)
    }
  }
}
