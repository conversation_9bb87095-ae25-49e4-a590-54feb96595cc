import * as Comps from './components';
import 'ant-design-vue/dist/reset.css';
import * as Icons from "./icons";
import { App } from 'vue';
const icons: any = Icons;
const comps: any = Comps

export default function loadElementPlugin (instance: App) {
  for (const key in (comps)) {
    const comp = comps[key]
    comp.install && instance.use(comp)
  }
  for (const i in icons) {
    instance.component(i, icons[i]);
  }
}

// import Antd from 'ant-design-vue';
// import 'ant-design-vue/dist/reset.css';
// import * as Icons from "@ant-design/icons-vue";
// import { App } from 'vue';
// const icons: any = Icons;

// export default function loadElementPlugin (instance: App) {
//   instance.use(Antd)
//   for (const i in icons) {
//     instance.component(i, icons[i]);
//   }
// }

