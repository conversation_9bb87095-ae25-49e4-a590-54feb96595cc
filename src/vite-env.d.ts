/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare module 'tiny-emitter/instance'
declare module 'path-to-regexp'
declare module 'nprogress'
declare module 'vue3-clipboard'
declare module '@codemirror/lang-javascript'
declare module '@codemirror/theme-one-dark'
declare module '@wangeditor/editor-for-vue'

declare interface Window {
  __POWERED_BY_QIANKUN__: any
  __INJECTED_PUBLIC_PATH_BY_QIANKUN__: any
  __webpack_public_path__: any
}

declare interface HTMLElement {
  mozRequestFullScreen: Function
  webkitRequestFullScreen: Function
  msRequestFullscreen: Function
}

declare interface Document {
  mozCancelFullScreen: Function
  webkitCancelFullScreen: Function
  msExitFullscreen: Function
}

declare module 'vue-kinesis'
