import checkPermission from '@/utils/checkPermission'
export default {
  name: 'has',
  // Directive has a set of lifecycle hooks:
  // called before bound element's parent component is mounted
  beforeMount (el: Element, binding: any, vNode: any) {
    const permissionName = binding.value
    const has = checkPermission.checkBtnPromission(permissionName)
    if (!has) {
      if (el.parentNode) {
        (el.parentNode?.removeChild as any)(el)
      }
    } else if (vNode.type === 'template') {
      el.replaceWith(...el.children)
    }
  },
  // called when bound element's parent component is mounted
  mounted () {},
  // called before the containing component's VNode is updated
  beforeUpdate () {},
  // called after the containing component's VNode and the VNodes of its children // have updated
  updated () {},
  // called before the bound element's parent component is unmounted
  beforeUnmount () {},
  // called when the bound element's parent component is unmounted
  unmounted () {}
}
