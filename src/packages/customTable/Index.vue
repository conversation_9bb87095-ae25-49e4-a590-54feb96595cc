<script lang="ts">export default { name: 'CustomTable'}</script>
<script setup lang="ts">
import { TableProps } from 'ant-design-vue';
import { nextTick, onMounted, reactive, ref, useSlots } from 'vue';
import { debounce } from 'lodash-es'

type tableModelTypes = 'FillLeaveHeight' | 'customHeight';
interface CustomTableProps {
  height?: number | string,
  bottomPadding?: number,
  dataApi: (param: any) => Promise<any>,
  params: Record<string, any>,
  tableModel?: tableModelTypes,
  rowKey?: string,
  selection?: boolean,
  columns?: any[] | null,
  noCard?: boolean,
  pagination?: boolean,
}
const props = withDefaults(defineProps<CustomTableProps>(), {
  height: 'auto',
  bottomPadding: 0,
  dataApi: () => Promise.resolve(),
  params: () => ({}),
  tableModel: 'FillLeaveHeight',
  columns: null,
  noCard: false,
  pagination: true
})
const cmpEmit = defineEmits(['changeInfo', 'selection-change'])

const customTabelWrap = ref<HTMLDivElement>()

const tableFields = ref<any>([])
const computedColumns = computed(() => {
  if (tableFields.value.length > 0) {
    return [...tableFields.value, ...(props.columns || [])]
  } else {
    return props.columns
  }
})

// 表格数据
const loading = ref(false)
const tableOptions = reactive({
  pageIndex: 1,
  pageSize: 15,
  total: 1,
  list: [],
  sort_field: '',
  sort_type: '',
  selectedRowKeys: []
})
function requestTableData (resetPage = false) {
  console.log('requestTableData resetPage', resetPage)
  // console.log('requestTableData tableOptions', tableOptions)
  if (!props.dataApi) return console.error('params dataApi err！')
  if (resetPage) {
    tableOptions.pageIndex = 1
  }
  const params: Record<string, any> = {
    ...props.params,
    sort_field: tableOptions.sort_field,
    sort_type: tableOptions.sort_type
  }
  if (props.pagination) {
    params.page = tableOptions.pageIndex,
    params.page_size = tableOptions.pageSize
  }
  // console.log('requestTableData params', params)
  loading.value = true
  props.dataApi(params)
    .then(async (res: any) => {
      if (!tableFields.value.length && res.fields) {
        tableFields.value = []
      }
      clearSelection()
      // if (res.current_page > res.last_page) {
      //   tableOptions.pageIndex = res.last_page
      //   requestTableData()
      //   return
      // }
      tableOptions.list = res.data.map((item: any, index: number) => {
        if (res.data.length === 1) return { ...item, first: true, last: true }
        else if (index === 0) return { ...item, first: true }
        else if (index === res.data.length - 1) return { ...item, last: true }
        return item
      })
      tableOptions.pageIndex = Number(res.current_page)
      tableOptions.total = res.total
      tableOptions.pageSize = res.per_page
      await nextTick()
      cmpEmit('changeInfo', res)
    })
    .catch((e: any) => {
      console.error(e)
    })
    .finally(() => { loading.value = false })
}
const clearSelection = () => {
  tableOptions.selectedRowKeys = []
  cmpEmit('selection-change', [])
}
const toggleRowSelection = (row: any) => {
  tableOptions.selectedRowKeys.push(row.id as never)
  cmpEmit('selection-change', tableOptions.list.filter(x => tableOptions.selectedRowKeys.includes((x as any).id as never)))
}
// 排序
// const sortChange = ({ prop, order }: any) => {
//   tableOptions.sort_field = prop
//   tableOptions.sort_type = order?.replace('ending', '')
//   requestTableData()
//   return false
// }
const handleCurrentChange = (toPageIndex: number) => {
  tableOptions.pageIndex = toPageIndex
  requestTableData()
  clearSelection()
}

/* table类型 */
const TableHeight = ref(200)
const customTable = ref('customTable')
const FillLeaveHeightAdapter = debounce(async () => {
  await nextTick()
  await nextTick()
  TableHeight.value = customTabelWrap.value?.clientHeight || 0
  TableHeight.value -= customTabelWrap.value?.querySelector('.ant-table-header')?.clientHeight || 40
  if (hasSlot(['leftTool', 'rightTool']) && customTabelWrap.value?.querySelector('.ant-table-title')) {
    TableHeight.value -= (customTabelWrap.value?.querySelector('.ant-table-title') as HTMLDivElement).offsetHeight
  }
}, 300)
const tableChange: TableProps['onChange'] = (_page, _filters, sort: any) => {
  tableOptions.sort_field = sort.columnKey || sort.field
  tableOptions.sort_type = sort.order?.replace('end', '')
  tableOptions.pageIndex = 1
  requestTableData()
}
const renderHeight = async () => {
  if (props.tableModel === 'FillLeaveHeight') {
    FillLeaveHeightAdapter()
    window.removeEventListener('resize', FillLeaveHeightAdapter)
    window.addEventListener('resize', FillLeaveHeightAdapter)
  }
  //  else if (props.tableModel === 'customHeight') {
  //   await nextTick()
  //   TableHeight.value = props.height
  // }
}

onMounted(() => {
  requestTableData()
  renderHeight()
})

defineExpose({
  requestTableData,
  renderHeight,
  customTable,
  clearSelection,
  toggleRowSelection
})

const slot = useSlots()
const hasSlot = (name: string | Array<string>): boolean => {
  if (typeof name === 'string') return !!slot[name]

  for (let i = 0; i < name.length; i++) {
    if (slot[name[i]]) return true
  }
  return false
}

</script>

<template>
  <a-card class="box-card mw-child-h-auto" :class="{ 'no-card': noCard }" :border="false">
    <div class="box-card-content">
      <div class="custom-table-top" v-if="hasSlot('top')">
        <slot name="top"></slot>
      </div>
      <div class="custom-table-wrap" id="custom-table-wrap" ref="customTabelWrap">
        <a-table
          :loading="loading"
          id="table"
          ref="customTable"
          :data-source="tableOptions.list"
          :row-selection="selection
            ? { fixed: true, selectedRowKeys: tableOptions.selectedRowKeys, onChange: (selectedRowKeys: any, selectedRows: any) => { tableOptions.selectedRowKeys = selectedRowKeys; cmpEmit('selection-change', selectedRows)} }
            : null"
          :pagination="false"
          :scroll="{ x: '100%', y: `${TableHeight}px` }"
          :rowKey="rowKey || 'id'"
          @change="tableChange"
          :columns="computedColumns"
          size="small">
          <slot></slot>
          <template #title  v-if="hasSlot(['leftTool', 'rightTool'])">
            <div class="tools" ref="tools">
              <div class="left"><slot name="leftTool"></slot></div>
              <div class="right"><slot name="rightTool"></slot></div>
            </div>
          </template>
          <template #bodyCell="{ text, record, index, column }">
            <slot name="bodyCell" :column="column" :record="record" :index="index" :text="text"></slot>
          </template>
          <template #headerCell="{ column }">
            <slot name="headerCell" :column="column">
              <a-tooltip v-if="column.desc" placement="top" class="th-over-hidden">
                <template #title>
                  <span>{{ column.desc }}</span>
                </template>
                <!-- {{ column.title }} -->
                <span>{{ column.title }}</span>
              </a-tooltip>
              <template v-else>
                <span v-if="column.child" style="font-weight: 400; color: #777; font-size: 13px;">{{ column.title }}</span>
                <template v-else>{{ column.title }}</template>
              </template>
            </slot>
          </template>
        </a-table>
      </div>
      <div class="custom-table-footer" v-if="props.pagination">
        <a-pagination
          size="small"
          v-model:current="tableOptions.pageIndex"
          v-model:page-size="tableOptions.pageSize"
          :total="tableOptions.total"
          :page-size-options="['15', '30', '50', '100']"
          @change="handleCurrentChange"
          show-quick-jumper
          show-size-changer
          layout="total, prev, pager, next, sizes"
        />
      </div>
    </div>
  </a-card>
</template>

<style lang="scss" scoped>
.box-card {
  width: 100%;
  min-height: 30vh;
  display: flex;
  &.no-card {
    border: none;
    :deep(.ant-card-body) {
      padding: 20px 0 0;
    }
  }
  & :deep(> div) {
    width: 100%;
  }
  .box-card-content{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: hidden;
    .custom-table-wrap{
      flex: 1;
      overflow: hidden;
      ::v-deep(.ant-table-body) {
        border-bottom: 1px solid #e8e8e8;
      }
    }
    .custom-table-footer{
      display: flex;
      justify-content: center;
    }
  }

  .tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }


  .custom-table-wrap {
    width: 100%;
    flex: 1;
  }
  ::v-deep(.th-over-hidden) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
  }
}
.pagination-sizes {
  height: 100%;
}
</style>
