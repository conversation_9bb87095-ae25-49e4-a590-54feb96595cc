<script lang="ts">
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { IDomEditor, IToolbarConfig } from '@wangeditor/editor';
export default defineComponent({
  name: 'Editor',
  components: { WangEditor: Editor, Toolbar },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 300
    }
  },

  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    const editorRef = shallowRef()

    // 内容 HTML
    const valueHtml = computed({
      get: () => props.value,
      set: (val: string) => {
        emit('update:value', val)
      }
    })

    // options
    const toolbarConfig: Partial<IToolbarConfig> = {
      toolbarKeys: [
        'headerSelect',
        'fontSize',
        'bold',
        'underline',
        'italic',
        'through',
        'clearStyle',
        'color',
        'bgColor',
        'sup',
        'sub',
        'bulletedList',
        'numberedList',
        'todo',
      ]
    }
    const editorConfig = { placeholder: '请输入内容...' }

    watch(
      () => props.disabled,
      (val) => {
        editorRef.value && (val ? editorRef.value.disable() : editorRef.value.enable())
      }
    )

    // 组件销毁时，也及时销毁编辑器
    onBeforeUnmount(() => {
      const editor = editorRef.value
      if (editor == null) return
      editor.destroy()
    })

    const handleCreated = (editor: any) => {
      editorRef.value = editor // 记录 editor 实例，重要！
    }

    const onChange = (editor: IDomEditor) => {
      if (!editor.getText()) {
        emit('update:value', '')
      }
    }

    return {
      editorRef,
      valueHtml,
      mode: 'default', // 或 'simple'
      toolbarConfig,
      editorConfig,
      handleCreated,
      onChange
    };

  }
})
</script>

<template>
  <div class="custom-editor">
    <Toolbar
      style="border-bottom: 1px solid #f0f0f0"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <WangEditor
      :style="`height: ${height}px; overflow-y: hidden;`"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="(_: any) => editorRef = _"
      @onChange="onChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.custom-editor {
  border: 1px solid #f0f0f0;

}
</style>