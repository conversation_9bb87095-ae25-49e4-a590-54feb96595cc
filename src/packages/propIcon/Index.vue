<script lang="ts">
export default { name: 'PropIcon' }
</script>
<script setup lang="ts">
const props = defineProps<{
  propDetail: {
    image_quality: string
    image: string
    image_chip: string
  }
}>()
</script>

<template>
  <div class="prop-icon">
    <img :src="props.propDetail.image_quality" v-show="props.propDetail.image_quality">
    <img :src="props.propDetail.image" v-show="props.propDetail.image">
    <img :src="props.propDetail.image_chip" v-show="props.propDetail.image_chip">
  </div>
</template>

<style lang="scss" scoped>
.prop-icon{
  width: 40px;
  height: 40px;
  position: relative;
  transition: transform .3s;
  img{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    &:nth-child(2) {
      transform: scale(.85);
    }
  }
}
</style>