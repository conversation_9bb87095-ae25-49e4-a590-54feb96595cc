<script lang="ts">
export default { name: 'UploadBtn' }
</script>
<script setup lang="ts">
import { reactive } from 'vue';
import { downloadTemp, importFile } from '@/api/common'
import { UploadChangeParam, UploadFile, UploadProps, message } from 'ant-design-vue';

interface BtnProps{
  uploadData?: Record<string, any>,
  page: string,
  accept?: string,
  downloadApi: Function,
  fileType: string,
  downloadData?: Record<string, any>
  hideUploadBtn?: boolean
  hideDownloadBtn?: boolean
}

const props = withDefaults(
  defineProps<BtnProps>(),
  {
    uploadData: () => ({}),
    page: '',
    accept: '.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel',
    downloadApi: () => {},
    fileType: '',
    downloadData: () => ({}),
    hideUploadBtn: false,
    hideDownloadBtn: false
  }
)

const state = reactive({
  visibleUpload: false,
  uploadLoading: false,
  fileList: [] as UploadFile[],
  file: null as UploadFile | null
})
/* 上传Btn引用 */
const cmpEmit = defineEmits(['uploadSuccess'])

function cancel () {
  state.visibleUpload = false
  state.uploadLoading = false
  state.fileList = []
}

const beforeUpload: UploadProps['beforeUpload'] = (file: UploadFile) => {
  state.fileList = [file];
  return false;
};

function fileChange (info: UploadChangeParam) {
  state.fileList = [info.file]
}
function submitUpload () {
  if (state.fileList.length === 0) {
    return message.error('未选择文件！')
  }
  state.uploadLoading = true

  const formData = new FormData()
  formData.append('file', (state.fileList[0] as any))
  for (const key in props.uploadData) {
    formData.append(key, props.uploadData[key])
  }
  importFile(props.page, formData)
    .then((res: any) => {
      cmpEmit('uploadSuccess', res)
      cancel()
    })
    .catch(() => {
      state.uploadLoading = false
      return message.error('请按照模版上传')
    })
}

watch(() => state.visibleUpload, (nv, ov) => {
  if (nv === false && ov === true) {
    state.uploadLoading = false
    state.fileList = []
  }
})

const download = (is_tpl: boolean) => {
  if (is_tpl) {
    downloadTemp(props.fileType)
  } else {
    props.downloadApi({ ...props.downloadData })
  }
}
</script>

<template>
  <a-space>
    <a-button v-if="!props.hideUploadBtn" @click="state.visibleUpload = true">批量更新</a-button>
    <a-button v-if="!props.hideDownloadBtn" type="primary" link @click="download(false)">导出配置</a-button>
    <a-modal v-model:open="state.visibleUpload" title="批量更新" :mask-closable="false">
      <a-card>
        <div class="content-box">
          <div class="title">1.下载导入模板</div>
          <div class="desc">请先下载导入模板，按模板要求填写数据；为避免导入失败，填写过程请勿修改表头</div>
          <div class="btns-wrap">
            <a-button type="primary" @click="download(true)">
              <template #icon><CloudDownloadOutlined /></template>
              下载导入模板
            </a-button>
          </div>
        </div>
      </a-card>
      <a-card>
        <div class="content-box">
          <div class="title">2.导入文件</div>
          <div class="desc">
            <slot name="tip">
              * 请按照模板格式准备需要导入的数据，更新后，已配置数据将被覆盖<br>
              * 文件小于2M，上传后即开始导入
            </slot>
          </div>
          <div class="btns-wrap">
            <a-upload-dragger
              v-model:fileList="state.fileList"
              name="file"
              :multiple="false"
              action="/"
              :accept="props.accept"
              @change="fileChange"
              @remove="state.fileList = []"
              :before-upload="beforeUpload"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined></inbox-outlined>
              </p>
              <p class="ant-upload-text">点击或拖拽文件至此区域即可上传</p>
            </a-upload-dragger>
          </div>
        </div>
      </a-card>
      <template #footer>
        <a-button link @click="cancel" :disabled="state.uploadLoading">取消</a-button>
        <a-button type="primary" :loading="state.uploadLoading" @click="submitUpload">
          <template #icon><CloudUploadOutlined /></template>
          上传
        </a-button>
      </template>
    </a-modal>
  </a-space>
</template>

<style lang="scss" scoped>
.ant-card {
  & + .ant-card {
    margin-top: 10px;
  }
}
.content-box {
  .title {
    font-weight: 500;
  }
  .desc {
    margin: 5px 0;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.7);
  }
  .btns-wrap {
    text-align: center;
  }
}
</style>
