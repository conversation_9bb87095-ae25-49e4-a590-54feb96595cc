import type { ExtractPropTypes } from 'vue'

export const galleryProps = {
  callback: {
    type: Function,
    default: () => () => {}
  },
  isOnlyUpload: {
    type: Boolean,
    default: false
  },
  onDestroy: {
    type: Function,
    default: () => () => {}
  }
}

export type GalleryProps = ExtractPropTypes<typeof galleryProps>

export type TImg = {
  id: number
  img_key: string
  md5: string
  preview_img: string
  url: string
}
