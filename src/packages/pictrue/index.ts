import { createVNode, render } from 'vue'
import { GalleryProps } from './gallery'
import Gallery from './Index.vue'

let vm: any = null
let container: any = null

export interface IPhotoGalleryOption {
  callback: Function
  isOnlyUpload?: boolean
}

export const PhotoGallery = (options?: IPhotoGalleryOption) => {
  if (!vm) {
    container = document.createElement('div')

    const props: Partial<GalleryProps> = {
      ...options,
      onDestroy: close
    }

    vm = createVNode(
      Gallery,
      props,
      null
    )

    render(vm, container)
    document.body.appendChild(container)
  }
}

const close = () => {
  document.body.removeChild(container)
  vm = null
}

export default {
  install: (app: any) => {
    app.config.globalProperties.$photoGallery = PhotoGallery
  }
}
