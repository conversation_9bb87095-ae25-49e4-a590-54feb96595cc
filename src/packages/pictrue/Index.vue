<!--
 * 图片库组件,可以选择图片/上传图片
 * 组件分两部分
 * step === 1   选择图片组件
 * step === 2   图片上传组件
 * isOnlyUpload 是否只上传图片(图片库使用)
 * bus.$emit('showPhotoGallery', {
 *   callback: (data: any) => { console.log(data) },
 *   isOnlyUpload: boolean
 * })
-->
<script setup lang="ts" name="Gallery">
import bus from '@/lib/bus';
import List from './components/List.vue';
import Upload from './components/Upload.vue';

let cb: Function

const state = reactive({
  visible: false,
  step: 1, // 1-选择图片, 2-上传
  isOnlyUpload: false
})

const close = () => { state.visible = false }
const setStep = (step: number) => { state.step = step }

provide('isOnlyUpload', state.isOnlyUpload)
provide('setStep', setStep)

const chooseImg = (item: any) => {
  cb && cb(item)
  close()
}

const uploadFinish = () => {
  if (state.step === 2) {
    state.step = 1
  } else {
    cb && cb()
    close()
  }
}

bus.$on('showPhotoGallery', (options: any) => {
  cb = options.callback
  state.isOnlyUpload = options.isOnlyUpload || state.isOnlyUpload
  state.visible = true
})
</script>

<template>
  <a-modal
    v-model:open="state.visible"
    :title="state.step === 1 ? '图片库' : '上传图片'"
    width="923px"
    destroy-on-close
    wrap-class-name="fp-mall-admin"
    :footer="null"
  >
    <List v-if="!state.isOnlyUpload && state.step === 1" @submit="chooseImg" @close="close"></List>
    <Upload ref="upload" v-if="state.isOnlyUpload || state.step === 2" @finish="uploadFinish"></Upload>
  </a-modal>
</template>

<style lang="scss" scoped>

</style>