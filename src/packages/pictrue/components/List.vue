<template>
  <div class="img-list">
    <template v-if="imgTypes.length > 1">
      <div class="left">
        asd
      </div>
      <a-divider type="vertical" style="height: 100%;"></a-divider>
    </template>
    <div class="right">
      <div class="filter">
        <a-input-search
          v-model:value="searchParams.search"
          placeholder="请输入图片名称"
          style="width: 300px;"
          enter-button="查询"
          allowClear
          @search="search"
        ></a-input-search>
        <a-button @click="setStep(2)" >上传图片</a-button>
      </div>
      <div class="result">
        <div class="list">
          <div
            class="img-item"
            :class="{ active: crtItem.id === item.id }"
            v-for="item in list"
            :key="item.id"
            @click="setCrt(item)"
          >
            <div class="img"><img :src="item.preview_img"></div>
            <div class="name">
              <div class="pre">{{ item.img_key.length > 5 ? item.img_key.substring(0, item.img_key.length-2) : item.img_key }}</div>
              <div class="last">{{ item.img_key.length > 5 && item.img_key.substring(item.img_key.length-2) || '' }}</div>
            </div>
          </div>
        </div>
        <div class="pagination-wrap">
          <a-pagination
            v-model:current="searchParams.page"
            :default-page-size="searchParams.page_size"
            :total="total"
            @current-change="handleCurrentChange"
          >
          </a-pagination>
        </div>
      </div>
    </div>
  </div>
  <div class="footer">
    <a-space wrap>
      <a-button @click="close">取消</a-button>
      <a-button type="primary" @click="confirm">确定</a-button>
    </a-space>
  </div>
</template>

<script lang="ts">
import { getImgList } from '@/api/resource'
import { defineComponent, inject, onBeforeMount, reactive, toRefs } from 'vue'
import { TImg } from '../gallery'
import { message } from 'ant-design-vue'

export default defineComponent({
  name: 'List',

  emits: ['submit', 'close'],
  setup (_, ctx) {
    const setStep: any = inject('setStep')

    const state = reactive({
      searchParams: {
        search: '',
        page: 1,
        page_size: 20
      },
      crtItem: {} as TImg,
      loading: false,
      list: [] as TImg[],
      total: 0,
      imgTypes: []
    })

    const search = () => {
      state.searchParams.page = 1
      getList()
    }

    const getList = () => {
      const params = { ...state.searchParams }
      state.loading = true
      getImgList(params).then((res: any) => {
        state.list = res.data
        state.searchParams.page = res.current_page
        state.total = res.total
      }).finally(() => { state.loading = false })
    }
    const handleCurrentChange = (val: number) => {
      state.searchParams.page = val
      getList()
    }

    const setCrt = (item: any) => {
      state.crtItem = item
    }

    onBeforeMount(() => {
      getList()
    })

    const confirm = () => {
      if (!state.crtItem.id) {
        return message.error('请选择一张图片！')
      }
      ctx.emit('submit', state.crtItem)
    }

    const close = () => ctx.emit('close')

    return {
      ...toRefs(state),
      search,
      setCrt,
      handleCurrentChange,
      setStep,
      close,
      confirm
    }
  }
})
</script>

<style lang='scss' scoped >
.img-list {
  display: flex;
  align-items: stretch;
  height: 550px;
  .left {
    width: 233px;
    flex-shrink: 0;
  }
  .right {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .result {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    .list {
      flex: 1;
      overflow: auto;
      font-size: 0;
      .item {
        display: inline-block;
        width: calc(25% - 10px);
        margin: 5px;
        cursor: pointer;
        &.active {
          border-color: #409EFF;
        }
        .img {
          width: 110px;
          height: 110px;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            max-width: 100%;
            max-height: 100%;
          }
        }
        .name {
          font-size: 15px;
          text-align: center;
          width: 100%;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-top: 10px;
          white-space: nowrap;
        }
      }
      .img-item {
        width: 115px;
        margin: 5px;
        cursor: pointer;
        display: inline-block;
        &.active {
          .img::after {
            content: "";
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            border: 1px solid #4096ff;
            box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
          }
        }
        .img {
          background-color: rgba(5, 5, 5, 0.06);
          position: relative;
          width: 115px;
          height: 115px;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            max-width: 100%;
            max-height: 100%;
          }
        }
        .name {
          font-size: 14px;
          display: flex;
          width: 100%;
          overflow: hidden;
          margin-top: 5px;
          .pre {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .last {
            flex-shrink: 0;
          }
        }
      }
    }
    .pagination-wrap {
      display: flex;
      justify-content: flex-end;
    }
  }
}
.footer {
  margin-top: 10px;
  text-align: center;
}
</style>
