<template>
  <a-upload
    class="upload-wrap"
    ref="update"
    action="#"
    list-type="picture-card"
    v-model:file-list="list"
    :before-upload="imgChange"
    :max-count="10"
    accept="image/gif,image/jpeg,image/jpg,image/png,image/svg"
    :multiple="true"
  >
    <div v-if="list.length < 10">
      <plus-outlined />
      <div style="margin-top: 8px">选择图片</div>
    </div>

    <template #previewIcon></template>
    <template #itemRender="{ file, actions: { remove } }">
      <div class="img-item">
        <div class="image">
          <img :src="file.thumbUrl" />
        </div>
        <div class="title" :title="file.name" :class="{ error: !checkValueLNL(file.name.split('.')[0]) }">
          <div class="pre">{{ file.name.length > 10 ? file.name.substring(0, file.name.length-6) : file.name }}</div>
          <div class="last">{{ file.name.length > 10 && file.name.substring(file.name.length-6) || '' }}</div>
        </div>
        <CloseCircleOutlined class="del-icon" @click="remove"/>
      </div>
    </template>
    <!-- <template #file="{ file }">
    </template> -->
    <template #tip>
      <div class="tips">单次最多上传 10 张图片, 且每张图片不可大于 2M, 文件名必须是字母、数字和下划线!</div>
    </template>
  </a-upload>
  <div class="footer">
    <a-space wrap>
      <a-button v-if="!isOnlyUpload" @click="setStep(1)">返回</a-button>
      <a-button type="primary" @click="preCheck" :loading="loading" :disabled="list.length === 0">确定</a-button>
    </a-space>
  </div>
</template>

<script lang="ts">
import { defineComponent, inject, reactive, toRaw, toRefs } from 'vue'
import { uploadImgs, uploadImgsCheck } from '@/api/resource'
import { checkValueLNL } from '@/utils'
import { UploadFile, message } from 'ant-design-vue'

export default defineComponent({
  name: 'Upload',


  emits: ['finish'],
  setup (_, ctx) {
    const setStep: any = inject('setStep')
    const isOnlyUpload = inject('isOnlyUpload')
    const state = reactive({
      list: [] as UploadFile[],
      loading: false
    })

    const imgChange = (_file: UploadFile, fileList: UploadFile[]) => {
      console.log(fileList)
      // state.list = fileList
      return false
    }

    const checkImgs = (): string => {
      const temp = {} as Record<string, boolean>
      for (const item in state.list) {
        const file = state.list[item]
        if (file.size && file.size > 1024 * 1024 * 2) {
          return `文件(${file.name})大小超过大小限制`
        }
        if (!checkValueLNL(file.name.split('.')[0])) {
          return `文件(${file.name})不符合命名规则, 文件名只包含数字、字母、下划线`
        }
        if (temp[file.name]) {
          return `文件(${file.name})重复选择`
        } else {
          temp[file.name] = true
        }
      }
      return ''
    }
    const preCheck = async () => {
      if (checkImgs()) {
        return message.error(checkImgs())
      }

      const formData = new FormData()
      toRaw(state.list).map((item: any) => {
        formData.append('file[]', item.originFileObj)
      })

      try {
        await uploadImgsCheck(formData)
        submit()
      } catch (e: any) {
        if (e.errCode === 50) {
          if (e.data.exist.length) {
          }
        }
      }
    }
    const submit = async () => {
      const formData = new FormData()
      toRaw(state.list).map((item: any) => {
        formData.append('file[]', item.originFileObj)
      })

      state.loading = true
      try {
        await uploadImgs(formData)
        state.list = []
        message.success('操作成功')
        ctx.emit('finish')
      } catch (error) {}
      state.loading = false
    }

    return {
      ...toRefs(state),
      isOnlyUpload,
      imgChange,
      setStep,
      preCheck,
      checkValueLNL
    }
  }
})
</script>

<style lang='scss' scoped >
.upload-wrap {
  height: 450px;
  overflow: auto;
  :deep(.ant-upload.ant-upload-select) {
    margin-bottom: 8px;
    width: 137px;
    height: 137px;
  }
  :deep(.ant-upload-list.ant-upload-list-picture-card .ant-upload-list-item-container) {
    width: 137px;
    height: 137px;
  }
  .tips {
    font-size: 12px;
    color: #E6A23C;
  }
  .img-item {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    &:hover {
      .del-icon {
        display: block;
      }
    }
    .del-icon {
      display: none;
      position: absolute;
      color: #ccc;
      right: 5px;
      top: 5px;
      font-size: 16px;
      cursor: pointer;
      &:hover {
        color: #666;
      }
    }
    .image {
      background-color: rgba(0, 0, 0, 0.02);
      width: 100%;
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    img {
      max-width: 100%;
      max-height: 100%;
    }
    .title {
      padding: 0 4px;
      width: 100%;
      text-align: center;
      font-size: 12px;
      flex-shrink: 0;
      display: flex;
      width: 100%;
      overflow: hidden;
      &.error {
        color: #ff4d4f;
      }
      .pre {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .last {
        flex-shrink: 0;
      }
    }
  }
}
.footer {
  margin-top: 10px;
  text-align: center;
}
</style>
