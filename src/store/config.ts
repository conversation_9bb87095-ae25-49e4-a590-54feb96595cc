import { getGlobalConfig } from '@/api/common'
import { defineStore } from 'pinia'
import { ref } from 'vue'

// 配置项类型定义
export interface TConfItem {
  label: string
  value: string | number
}

// 游戏渠道类型定义
export interface GameChannelItem {
  game_project: string
  sdk_pkg_channels: TConfItem[]
}

// 配置键类型
export type TConfigKeys = 'langs' | 'channels' | 'platform' | 'show_pages' | 'prize_type' | 'game_projects' | 'task_events'

// 配置状态类型定义
export interface ConfigState {
  langs: TConfItem[]
  channels: TConfItem[]
  platform: TConfItem[]
  show_pages: TConfItem[]
  prize_type: TConfItem[]
  game_projects: TConfItem[]
  task_events: TConfItem[]
  game_channels: GameChannelItem[]
}

/**
 * 配置管理 Store
 * 用于管理全局配置数据，包括语言、渠道、平台等选项
 */
export const useConfigStore = defineStore('config', () => {
  // 配置状态
  const configState = ref<ConfigState>({
    langs: [],
    channels: [],
    platform: [],
    show_pages: [],
    prize_type: [],
    game_projects: [],
    game_channels: [],
    task_events: []
  })

  /**
   * 获取全局配置
   * @param isForce 是否强制刷新
   */
  const FETCH_GLOBAL_CONFIG = async (isForce: boolean = false): Promise<void> => {
    if (!isForce && configState.value.langs.length) return

    try {
      // 请求全局配置
      const res = await getGlobalConfig()
      configState.value = res as ConfigState
    } catch (error) {
      console.error('获取全局配置失败:', error)
    }
  }

  /**
   * 根据值获取配置项
   * @param key 配置键
   * @param val 配置值
   * @returns 配置项
   */
  const getConfItem = (key: TConfigKeys, val: string | number): TConfItem | undefined => {
    return configState.value[key].find((item: TConfItem) => item.value === val)
  }

  /**
   * 获取游戏项目对应的渠道
   * @param game_project 游戏项目
   * @returns 渠道列表
   */
  const getGameChannel = (game_project: string): TConfItem[] => {
    const gameChannel = configState.value.game_channels.find((item: GameChannelItem) => item.game_project === game_project)
    return gameChannel?.sdk_pkg_channels || []
  }

  return {
    configState,
    getConfItem,
    FETCH_GLOBAL_CONFIG,
    getGameChannel
  }
})
