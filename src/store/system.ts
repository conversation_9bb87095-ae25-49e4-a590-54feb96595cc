import { defineStore } from 'pinia'
import { reactive } from 'vue'

// 系统状态接口
export interface ISystemState {
  asideStatus: boolean
  env: string
}

/**
 * 系统管理 Store
 * 用于管理系统级别的状态，如侧边栏状态、环境信息等
 */
export const useSystemStore = defineStore('system', () => {
  // 系统状态
  const systemState: ISystemState = reactive({
    asideStatus: false,
    env: ''
  })

  /**
   * 切换侧边栏状态
   */
  const setAsideStatus = (): void => {
    systemState.asideStatus = !systemState.asideStatus
  }

  /**
   * 设置当前环境
   * @param env 环境名称
   */
  const setCrtEnv = (env: string): void => {
    systemState.env = env
  }

  return {
    systemState,
    setAsideStatus,
    setCrtEnv
  }
})
