import { MenuItemRouter } from '@/@types/rotuer'
import { asyncRoutes, constantRoutes } from '@/router'
import { getPermissionMap, recursionRouter } from '@/utils/recursion-router'
import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { RouteRecordRaw } from 'vue-router'

// 权限状态接口
export interface PermissionState {
  routes: MenuItemRouter[]
  dynamicRoutes: MenuItemRouter[]
  commonBtnPromissions: string[]
  permissionMap: Record<string, string>
  subRoutes: MenuItemRouter[] | RouteRecordRaw[]
}

/**
 * 权限管理 Store
 * 用于管理用户权限、路由和按钮权限
 */
export const usePermissionStore = defineStore('permission', () => {
  // 权限状态
  const permissionState: PermissionState = reactive({
    routes: [],
    dynamicRoutes: [],
    commonBtnPromissions: [],
    permissionMap: {},
    subRoutes: []
  })

  /**
   * 设置权限映射
   * @param data 权限映射数据
   */
  const setPermissionMap = (data: Record<string, string>): void => {
    permissionState.permissionMap = data
  }

  /**
   * 设置路由
   * @param routes 路由列表
   */
  const setRoutes = (routes: MenuItemRouter[]): void => {
    permissionState.routes = routes.concat(constantRoutes)
    permissionState.dynamicRoutes = routes
  }

  /**
   * 设置按钮权限
   * @param routes 路由列表
   */
  const setBtnPromise = (routes: MenuItemRouter[]): void => {
    routes.forEach((route: any) => {
      if (route.name === '通用配置管理') {
        route._child && route._child.forEach((child: any) => permissionState.commonBtnPromissions.push(child.component_name))
      }
    })
  }

  /**
   * 设置子路由
   * @param routes 路由列表
   */
  const setSubRoutes = (routes: MenuItemRouter[] | RouteRecordRaw[]): void => {
    permissionState.subRoutes = routes.filter((item) => !item.meta?.hidden)
  }

  /**
   * 设置路由权限
   * @param permissionList 权限列表
   */
  const SET_ROUTES = (permissionList: any[]): void => {
    const routes = recursionRouter(permissionList, asyncRoutes)

    setPermissionMap(getPermissionMap(routes))
    setRoutes(routes)
  }

  return {
    permissionState,
    setPermissionMap,
    setRoutes,
    setBtnPromise,
    SET_ROUTES,
    setSubRoutes
  }
})
