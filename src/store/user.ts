import { IdType } from '@/model/common'
import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { publicChildPermission, publicGameList } from '@/api'

// 游戏配置接口
export interface IGameConfig {
  system: string
  game_project: string
  api_server_url: string
  game_name: string
  game_icon_url: string
  gmUrl: string
}

// 用户信息接口
export interface UserInfoT {
  username: string
  email: string
  id: IdType
  permission_list: object[]
  route_path: string[]
}

// 用户状态接口
export interface UserStateT {
  isLogin: boolean
  ticket: string | null
  userInfo: UserInfoT
  loading: boolean
  commonBtnPromissions: any[]
  game_infos: IGameConfig[]
  crt_game?: IGameConfig
}

/**
 * 用户管理 Store
 * 用于管理用户登录状态、权限信息和游戏配置
 */
export const useUserStore = defineStore('user', () => {
  // 用户状态
  const userState: UserStateT = reactive({
    isLogin: false,
    ticket: '',
    // 开启全屏loading
    loading: false,
    userInfo: {
      email: '',
      id: undefined,
      permission_list: [],
      route_path: [],
      username: ''
    },
    commonBtnPromissions: [],
    game_infos: []
  })

  /**
   * 用户登录
   * @param userInfo 用户信息
   */
  const login = (userInfo: UserInfoT): void => {
    userState.isLogin = true
    userState.userInfo = { ...userInfo }
  }

  /**
   * 用户登出
   */
  const logout = (): void => {
    const ticket = localStorage.getItem('ticket')
    localStorage.removeItem('ticket')
    location.href =
      `${import.meta.env.VITE_APP_ADMIN_CENTER_API}/admin/publicLogout?system=1&referer=&ticket=${ticket}` +
      encodeURIComponent(`${location.origin}/login`)
  }

  /**
   * 设置用户票据
   * @param ticket 用户票据
   */
  const setTicket = (ticket: string | null): void => {
    userState.ticket = ticket
  }

  /**
   * 保存按钮权限
   * @param data 权限数据
   */
  const saveBtnPromis = (data: any[]): void => {
    data.forEach((item: any) => {
      if (item.component === 'CommonPromission') {
        item._child &&
          item._child.forEach((child: any) => {
            userState.commonBtnPromissions.push(child.component)
          })
      }
    })
  }

  /**
   * 获取用户权限
   * @returns Promise<string>
   */
  const FETCH_PERMISSION = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (userState.userInfo.permission_list.length) {
        return resolve('success')
      }
      if (userState.ticket === '') {
        reject(new Error('未检测到ticket!'))
      }
      const params = {
        ticket: userState.ticket
      }
      publicChildPermission(params)
        .then((permissionRes) => {
          saveBtnPromis(permissionRes.permission_list)
          login(permissionRes)
          resolve('success')
        })
        .catch(() => {
          reject(new Error('权限请求失败'))
        })
    })
  }

  /**
   * 获取游戏权限
   * @returns Promise<string>
   */
  const FETCH_GAME_PERMISSION = async (): Promise<string> =>
    new Promise((resolve, reject) => {
      console.log(userState.game_infos, 'userState.game_infos')
      // if (userState.game_infos.length) return resolve('success')
      if (userState.ticket === '') {
        return reject(new Error('未检测到ticket!'))
      }
      const params = {
        ticket: userState.ticket
      }
      publicGameList(params)
        .then((res: IGameConfig[]) => {
          userState.game_infos = res.map((item) => ({
            ...item,
            gmUrl: import.meta.env[`VITE_APP_${item.game_project.toUpperCase()}_GM_URL`]
          }))
          console.log(userState.game_infos, 'userState.game_infos')
          if (userState.game_infos.length) {
            const localGame = localStorage.getItem('crtGame')
            // console.log('localGame', localGame)
            const activeGame = userState.game_infos.find((item: IGameConfig) => item.game_project === localGame)
            const filterGame = userState.game_infos[0] || {}
            // console.log('activeGame', activeGame)
            // console.log('filterGame', filterGame)
            const crtGame = activeGame || filterGame
            // console.log('crtGame', crtGame)
            userState.crt_game = crtGame
            localStorage.setItem('crtGame', crtGame.game_project)

            resolve('success')
          } else {
            localStorage.removeItem('ticket')
            reject('no game infos')
          }
        })
        .catch(() => {
          reject(new Error('权限请求失败'))
        })
    })

  return {
    userState,
    login,
    logout,
    setTicket,
    saveBtnPromis,
    FETCH_PERMISSION,
    FETCH_GAME_PERMISSION
  }
})
