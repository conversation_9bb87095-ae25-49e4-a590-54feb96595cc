export const PAGE_CONF = {
  MULTILINGUAL: 'multilingual', // 工具箱 - 多语言库
  PLATFORM_PROPS: 'platformProps', // 工具箱 - 平台道具
  DAILY_CHECK: 'dailyCheck', // 工具箱 - 平台道具
  LADDERCONFIGS: 'ladderConfigs', // 权益入口 - 上传梯度配置
  RIGHTGIFT: 'rightGift', // 专享礼包 - 批量导入
  TASKCONFIG: 'taskConfig', // 积分任务 - 批量导入
  TASKRULE: 'taskRule', // 积分任务 - 任务说明
  PRODUCTCONFIG: 'productConfig', // 积分商城 - 批量导入
  GIFTCONFIG: 'giftConfig', // 礼包配置 - 批量导入
  DAILY_CHECK_WEEKLY: 'dailyCheckWeekly', // 周签到 - 批量导入
  DAILY_CHECK_NEW: 'dailyCheckNew' // 新版月签到 - 批量导入
}

// 权限
export enum PERMISSION_TYPE {
  PAGE = 1,
  BUTTON = 3
}

export enum COMMON_TYPES {
  CLOSE,
  OPEN
}

// 非登录页面
export const visitorPageList = ['/login', '/403', '/error']

// banner 组样式类型
export const ACT_BANNER_STYLE_TYPES = [
  {
    value: 1,
    img: new URL('../assets/img/banner_type_1.jpeg', import.meta.url).href,
    label: '样式一',
    width: 686,
    height: 292
  },
  {
    value: 3,
    img: new URL('../assets/img/banner_type_2.jpeg', import.meta.url).href,
    label: '样式二',
    width: 686,
    height: 292
  }
]
