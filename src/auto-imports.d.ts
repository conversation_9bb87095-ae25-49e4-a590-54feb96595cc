/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const Modal: typeof import('ant-design-vue')['Modal']
  const antdTheme: typeof import('./utils/antdTheme')['default']
  const checkChannel: typeof import('./utils/formValidate')['checkChannel']
  const checkImg: typeof import('./utils/formValidate')['checkImg']
  const checkInt: typeof import('./utils/formValidate')['checkInt']
  const checkPermission: typeof import('./utils/checkPermission')['default']
  const checkPlatform: typeof import('./utils/formValidate')['checkPlatform']
  const checkValueLNL: typeof import('./utils/index')['checkValueLNL']
  const columnWithActionPermission: typeof import('./utils/index')['columnWithActionPermission']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineComponents: typeof import('./utils/defineComponents')['default']
  const downloadFile: typeof import('./utils/downloadFile')['downloadFile']
  const effectScope: typeof import('vue')['effectScope']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getPermissionMap: typeof import('./utils/recursion-router')['getPermissionMap']
  const getSecondTimeStep: typeof import('./utils/index')['getSecondTimeStep']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const isFormData: typeof import('./utils/index')['isFormData']
  const isPositiveInt: typeof import('./utils/index')['isPositiveInt']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const markRaw: typeof import('vue')['markRaw']
  const message: typeof import('ant-design-vue')['message']
  const messageConfirm: typeof import('./utils/index')['messageConfirm']
  const nextTick: typeof import('vue')['nextTick']
  const notification: typeof import('ant-design-vue')['notification']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const recursionRouter: typeof import('./utils/recursion-router')['recursionRouter']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setDefaultFilter: typeof import('./utils/index')['setDefaultFilter']
  const setDefaultRoute: typeof import('./utils/recursion-router')['setDefaultRoute']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useFullScreen: typeof import('./hooks/useFullScreen')['useFullScreen']
  const useLink: typeof import('vue-router')['useLink']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const validServers: typeof import('./utils/formValidate')['validServers']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, InjectionKey, PropType, Ref, VNode, WritableComputedRef } from 'vue'
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly Modal: UnwrapRef<typeof import('ant-design-vue')['Modal']>
    readonly antdTheme: UnwrapRef<typeof import('./utils/antdTheme')['default']>
    readonly checkChannel: UnwrapRef<typeof import('./utils/formValidate')['checkChannel']>
    readonly checkImg: UnwrapRef<typeof import('./utils/formValidate')['checkImg']>
    readonly checkInt: UnwrapRef<typeof import('./utils/formValidate')['checkInt']>
    readonly checkPermission: UnwrapRef<typeof import('./utils/checkPermission')['default']>
    readonly checkPlatform: UnwrapRef<typeof import('./utils/formValidate')['checkPlatform']>
    readonly checkValueLNL: UnwrapRef<typeof import('./utils/index')['checkValueLNL']>
    readonly columnWithActionPermission: UnwrapRef<typeof import('./utils/index')['columnWithActionPermission']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineComponents: UnwrapRef<typeof import('./utils/defineComponents')['default']>
    readonly downloadFile: UnwrapRef<typeof import('./utils/downloadFile')['downloadFile']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getPermissionMap: UnwrapRef<typeof import('./utils/recursion-router')['getPermissionMap']>
    readonly getSecondTimeStep: UnwrapRef<typeof import('./utils/index')['getSecondTimeStep']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly isFormData: UnwrapRef<typeof import('./utils/index')['isFormData']>
    readonly isPositiveInt: UnwrapRef<typeof import('./utils/index')['isPositiveInt']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly message: UnwrapRef<typeof import('ant-design-vue')['message']>
    readonly messageConfirm: UnwrapRef<typeof import('./utils/index')['messageConfirm']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly notification: UnwrapRef<typeof import('ant-design-vue')['notification']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly recursionRouter: UnwrapRef<typeof import('./utils/recursion-router')['recursionRouter']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setDefaultFilter: UnwrapRef<typeof import('./utils/index')['setDefaultFilter']>
    readonly setDefaultRoute: UnwrapRef<typeof import('./utils/recursion-router')['setDefaultRoute']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useFullScreen: UnwrapRef<typeof import('./hooks/useFullScreen')['useFullScreen']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useRoute: UnwrapRef<typeof import('vue-router')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('vue-router')['useRouter']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly validServers: UnwrapRef<typeof import('./utils/formValidate')['validServers']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly Modal: UnwrapRef<typeof import('ant-design-vue')['Modal']>
    readonly antdTheme: UnwrapRef<typeof import('./utils/antdTheme')['default']>
    readonly checkChannel: UnwrapRef<typeof import('./utils/formValidate')['checkChannel']>
    readonly checkImg: UnwrapRef<typeof import('./utils/formValidate')['checkImg']>
    readonly checkInt: UnwrapRef<typeof import('./utils/formValidate')['checkInt']>
    readonly checkPermission: UnwrapRef<typeof import('./utils/checkPermission')['default']>
    readonly checkPlatform: UnwrapRef<typeof import('./utils/formValidate')['checkPlatform']>
    readonly checkValueLNL: UnwrapRef<typeof import('./utils/index')['checkValueLNL']>
    readonly columnWithActionPermission: UnwrapRef<typeof import('./utils/index')['columnWithActionPermission']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineComponents: UnwrapRef<typeof import('./utils/defineComponents')['default']>
    readonly downloadFile: UnwrapRef<typeof import('./utils/downloadFile')['downloadFile']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getPermissionMap: UnwrapRef<typeof import('./utils/recursion-router')['getPermissionMap']>
    readonly getSecondTimeStep: UnwrapRef<typeof import('./utils/index')['getSecondTimeStep']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly isFormData: UnwrapRef<typeof import('./utils/index')['isFormData']>
    readonly isPositiveInt: UnwrapRef<typeof import('./utils/index')['isPositiveInt']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly message: UnwrapRef<typeof import('ant-design-vue')['message']>
    readonly messageConfirm: UnwrapRef<typeof import('./utils/index')['messageConfirm']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly notification: UnwrapRef<typeof import('ant-design-vue')['notification']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly recursionRouter: UnwrapRef<typeof import('./utils/recursion-router')['recursionRouter']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setDefaultFilter: UnwrapRef<typeof import('./utils/index')['setDefaultFilter']>
    readonly setDefaultRoute: UnwrapRef<typeof import('./utils/recursion-router')['setDefaultRoute']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useFullScreen: UnwrapRef<typeof import('./hooks/useFullScreen')['useFullScreen']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useRoute: UnwrapRef<typeof import('vue-router')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('vue-router')['useRouter']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly validServers: UnwrapRef<typeof import('./utils/formValidate')['validServers']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}
