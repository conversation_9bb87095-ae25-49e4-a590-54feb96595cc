import { downloadFile } from '@/utils/downloadFile'
import type { AxiosInstance } from 'axios'
import axios from 'axios'
import router from '@/router'
import { message } from 'ant-design-vue'
import { useSystemStore } from '@/store'

const service: AxiosInstance = axios.create({
  timeout: 60000
})

service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  async response => {
    if (response.status === 200) {
      // responseType
      if (response.config.responseType === 'arraybuffer') {
        downloadFile(response)
        return Promise.resolve('')
      }

      const res = response.data
      if (res.errCode === 0 || res.code === 200) {
        const { setCrtEnv } = useSystemStore()
        res.env && setCrtEnv(res.env)
        return res.data
      } else {
        if (res.code === 403) {
          message.error({
            content: '登录已失效，请重新登录！',
            onClose: () => {
              router.replace('/login')
            }
          })
        } else {
          // console.log('err res', res)
          const msg = typeof res.msg === 'string' ? res.msg : ''
          const errMsg = typeof res.errMsg === 'string' ? res.errMsg : ''
          const messageStr = msg || errMsg
          if(messageStr) {
            message.error(messageStr)
          }
        }
        return Promise.reject(response.data)
      }
    } else {
      errorHandle(response)
      Promise.reject(response.data)
      return response
    }
  },
  error => {
    const { response } = error
    if (response) {
      // 请求已发出，但是不在2xx的范围
      errorHandle(response)
      return Promise.reject(response.data)
    } else {
      // 处理断网的情况
      // eg:请求超时或断网时，更新state的network状态
      // network状态在app.vue中控制着一个全局的断网提示组件的显示隐藏
      // 关于断网组件中的刷新重新获取数据，会在断网组件中说明
      message.error('网络连接异常,请稍后再试!')
      return Promise.reject('网络连接异常,请稍后再试!')
    }
  }
)

const errorHandle = (res: any) => {
    // 状态码判断
  switch (res.status) {
    case 401:
      break
    case 403:
      break
    case 404:
      message.error('网络连接异常,请稍后再试!')
      break
    case 50: // 图片上传校验，失败后业务逻辑处理
      break
    case 30:
      message.error({
        content: '登录已失效，请重新登录！',
        onClose: () => {
          router.replace('/login')
        }
      })
      break
    default:
      message.error(res.msg || res.errMsg)
  }
}

export default service
