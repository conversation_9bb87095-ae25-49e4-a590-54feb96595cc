import type { AxiosError } from 'axios'
import service from './interceptor'
import { storeToRefs } from 'pinia'
import { usePermissionStore, useUserStore } from '@/store'
import router from '@/router'
import { isFormData } from '@/utils'

const request = {
  get<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('GET', url, { params: setCommonParams(params) })
  },
  post<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('POST', url, { data: setCommonParams(params) })
  },
  put<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('PUT', url, { data: setCommonParams(params) })
  },
  delete<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('DELETE', url, { data: setCommonParams(params) })
  },
  downfile<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('POST', url, { data: setCommonParams(params), responseType: 'arraybuffer' })
  },
  downfileGet<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('GET', url, { params: setCommonParams(params), responseType: 'arraybuffer' })
  },
  request<T = any>(method = 'GET', url: string, params: any = {}): Promise<T> {
    const { userState } = storeToRefs(useUserStore())
    if (url.startsWith('/api') && userState.value.crt_game) {
      url = userState.value.crt_game.api_server_url + url
    }
    if (url.startsWith('/backend')) {
      url = url.replace('/backend', import.meta.env.VITE_APP_ADMIN_CENTER_API)
    }
    return new Promise((resolve, reject) => {
      service({ method, url, ...params })
        .then((res) => {
          resolve(res as unknown as Promise<T>)
        })
        .catch((e: Error | AxiosError) => {
          reject(e)
        })
    })
  }
}

// 接口统一加参数
const setCommonParams = (params: any = {}): {} => {
  const { userState } = storeToRefs(useUserStore())
  const { permissionState } = storeToRefs(usePermissionStore())
  // console.log('userState.value.crt_game', userState.value.crt_game)
  const commonParams: Record<string, string | number | undefined | null> = {
    admin_project: userState.value.crt_game?.game_project,
    game_project: userState.value.crt_game?.game_project,
    system: import.meta.env.VITE_APP_SYSTEM,
    ticket: userState.value.ticket,
    permission_id:
      (router.currentRoute.value.meta.permission && permissionState.value.permissionMap[router.currentRoute.value.meta.permission]) ||
      router.currentRoute.value.meta.id ||
      '',
    user_id: userState.value.userInfo.id,
    user_name: userState.value.userInfo.username
  }

  // if (params.is_admin) {
  //   // 请求运营中心接口用 game_project
  //   params.game_project = userState.value.crt_game?.game_project
  //   console.log('params.game_project', params.game_project)
  //   delete params.is_admin
  // } else if (commonParams.admin_project !== 'funplus_zone') {
  //   // * 请求业务接口 -
  //   // * 具体游戏时统一添加 game_project 字段为当前游戏
  //   params.game_project = commonParams.admin_project
  // }

  if (isFormData(params)) {
    Object.keys(commonParams).forEach((k: string) => {
      params.append(k, commonParams[k])
    })
  } else {
    params = { ...commonParams, ...params }
  }

  return params
}

export default request
