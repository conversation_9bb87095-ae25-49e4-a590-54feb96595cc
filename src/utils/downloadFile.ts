/** 接口返回文件流，下载文件 */
export const downloadFile = (response: any) => {
  const blob = new Blob([response.data], { type: response.headers['content-type'] })
  if (response.headers['content-type'] === 'application/json') {
    // 接口错误 返回 json, 解析后提示 msg
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      response = JSON.parse((reader.result as string))
    }
  } else {
    // 接口正确，返回
    const fileName = (response.headers['content-disposition'] || response.headers['Content-Disposition'] || '').split('filename=')[1]
    const link = document.createElement('a') // 生成一个a标签。
    link.href = window.URL.createObjectURL(blob) // href属性指定下载链接
    link.download = fileName // dowload属性指定文件名
    link.click() // click()事件触发下载
  }
}
