/** 路由校对 */

import { PERMISSION_TYPE } from "@/enum";

/**
 *
 * @param  {Array} userRouter 后台返回的用户权限json
 * @param  {Array} allRouter  前端配置好的所有动态路由的集合
 * @return {Array} realRoutes 过滤后的路由
 */
export const recursionRouter = (userRouter: any[] = [], allRouter: any[] = []) => {
  if (process.env.NODE_ENV === "development") {
    return allRouter;
  }
  const crtGame = localStorage.getItem("crtGame");
  const realRoutes = [] as any[];
  allRouter.forEach((v, _i) => {
    userRouter.forEach((item, _index) => {
      if (crtGame === "funplus_zone" && v.meta?.not_funplus_zone) return;
      if (crtGame !== "funplus_zone" && v.meta?.funplus_zone) return;
      if (v.name && item.component_name === v.name && item.category === PERMISSION_TYPE.PAGE) {
        v.meta.id = item.id;
        v.meta.sort = item.sort;
        v.key = item.path;

        item._child = item._child || [];
        const btnPermission = item._child.filter((item: any) => item.category === PERMISSION_TYPE.BUTTON);
        v.meta.rule = {
          button: btnPermission.map((item: any) => item.component_name.trim())
        };

        const pageChildrenList = item._child.filter((item: any) => item.category === PERMISSION_TYPE.PAGE);
        v.children = recursionRouter(pageChildrenList, v.children);
        v.redirect = v.children[0] ? v.children[0].path : "";
        realRoutes.push(v);
      }
    });
  });
  return realRoutes.sort((a, b) => {
    return b.meta.sort - a.meta.sort;
  });
};

/**
 *
 * @param {Array} routes 用户过滤后的路由
 *
 * 递归为所有有子路由的路由设置第一个children.path为默认路由
 */
export function setDefaultRoute(routes: any[]) {
  routes.forEach((v) => {
    if (v._child && v.children.length > 0) {
      v.redirect = { name: v.children[0].name };
      setDefaultRoute(v.children);
    }
  });
}

/**
 * 将路由转化为  name: permission_id 的 map 表
 * @param routes 路由
 */
export const getPermissionMap = (routes: any[]) => {
  let o = {} as any;
  routes.forEach((route: any) => {
    o[route.name] = route.meta.id;
    if (route.children && route.children.length) {
      o = Object.assign(o, getPermissionMap(route.children));
    }
  });
  return o;
};
