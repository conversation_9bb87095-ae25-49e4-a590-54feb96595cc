/** 方法集 */
import { Modal, message } from 'ant-design-vue'
import checkPermission from './checkPermission'

/**
 * * 校验字符串是否符合 字母/数字/_ 的组合
 * @param v 被校验字符串
 * @returns 校验结果
 */
export const checkValueLNL = (v: string) => {
  if (!v) return false
  return /^[a-zA-Z0-9_]*$/.test(v)
}

/**
 * 检测是否非正整数
 * @param v 字符串
 * @returns Boolean
 */
export const isPositiveInt = (v: string | number): boolean => {
  v = v.toString()
  return /^\+?[1-9][0-9]*$/.test(v)
}

// 判断对象是否为 formData 类型
export const isFormData = (v: any) => {
  return Object.prototype.toString.call(v) === '[object FormData]'
}

export const getSecondTimeStep = () => {
  return Math.floor(new Date().getTime() / 1000)
}

/**
 * 批量操作
 * @param type 弹出框类型  '' | 'success' | 'warning' | 'info' | 'error'
 * @param title 提示信息
 * @param api 请求接口
 * @param params 接口参数
 * @returns
 */
export const messageConfirm = (content: string, api: any, params: {}) =>
  new Promise((resolve: Function, reject: Function) => {
    Modal.confirm({
      title: '提示',
      content,
      onOk: () => {
        return new Promise((res, rej) => {
          api(params)
            .then(() => {
              message.success('操作成功')
              res('操作成功')
              resolve('操作成功')
            })
            .catch(() => rej())
        })
      },
      onCancel: () => {
        reject()
      }
    })
  })

// autoAddActions
export const columnWithActionPermission = (
  columns: Array<Record<string, string | boolean | number>>,
  width: string | number
): Array<Record<string, string | boolean | number>> => {
  const oparetionPermission = checkPermission.checkBtnPromission('Operation')
  return oparetionPermission ? [...columns, { key: 'action', title: '操作', width, fixed: 'right', align: 'center' }] : columns
}

// 筛选器默认值类型定义
export interface DefaultFilterValues {
  f_os: string
  f_channel: string
  lang: string
  f_s_ids: string
  f_lv_ids: string
}

/**
 * 设置筛选器默认值
 * @param formData 表单数据对象
 * @param keys 需要设置默认值的字段数组
 * @description 当筛选器开启时，为指定字段设置默认值；关闭时清空字段值
 */
export const setDefaultFilter = <T extends Record<string, any>>(formData: T & { is_filter?: number }, keys: Array<keyof T>): void => {
  const initData: DefaultFilterValues = {
    f_os: 'all',
    f_channel: 'all',
    lang: 'all',
    f_s_ids: '0-99999',
    f_lv_ids: '0-99999'
  }

  keys.forEach((key) => {
    console.log(formData, formData.is_filter)
    const defaultValue = initData[key as keyof DefaultFilterValues]
    ;(formData as any)[key] = formData.is_filter === 1 ? defaultValue : ''
  })
}
