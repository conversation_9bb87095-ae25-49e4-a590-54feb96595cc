/** 表单校验函数 */

import { isPositiveInt } from './index'

export const checkPlatform = (_rule: any, value: any, cb: Function) => {
  if (value.length === 0 || !value) {
    return cb(new Error('请选择操作系统'))
  }
  cb()
}

export const checkChannel = (_rule: any, value: any, cb: Function) => {
  if (value.length === 0 || !value) {
    return cb(new Error('请选择渠道'))
  }
  cb()
}

/**
  * 校验服务器合法性
  * 1.支持多组服务器
  * 2.服务器区间不存在交集
  * @param {Array} servers 服务器数据
  */
export const validServers = (servers: string[][]) => {
  const rep = /^[0-9]+?$/
  if (!servers || !servers.length) {
    return {
      status: true,
      msg: '服务器不能为空!'
    }
  }
  for (let i = 0; i < servers.length; i++) {
    const serverArr = servers[i]
    const [prev, next] = serverArr
    for (let j = 0; j < servers.length; j++) {
      const serverArrJ = servers[j]
      const [sPrev, sNext] = serverArrJ
      if ((!sPrev && Number(sPrev) !== 0) || (!sNext && Number(sNext) !== 0)) {
        return {
          status: true,
          msg: '服务器不能为空!'
        }
      }
      if (!rep.test(sPrev) || !rep.test(sNext)) {
        return {
          status: true,
          msg: '服务器参数值必须是非负整数!'
        }
      }
      if (Number(sPrev) > Number(sNext)) {
        return {
          status: true,
          msg: '结束服务器不能小于开始服务器!'
        }
      }
      if (i === j) continue
      if (
        (next >= sPrev && prev <= sPrev) || (sNext >= prev && sPrev <= prev)
      ) {
        return {
          status: true,
          msg: '服务器各组区间不能存在交集!'
        }
      }
    }
  }
  return {
    status: false
  }
}

/**
 * 图片资源校验函数
 * @param rule 校验规则
 * @param value 表单项 value
 * @param cb 验证结果函数
 */
export const checkImg = (_rule: any, value: string, cb: Function) => {
  if (!value) {
    return cb(new Error('请选择图片'))
  }
  cb()
}

export const checkInt = (_rule: any, value: any, cb: Function) => {
  if (!isPositiveInt(value)) {
    return cb(new Error('请输入正整数'))
  }
  cb()
}
