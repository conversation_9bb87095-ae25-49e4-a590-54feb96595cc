/** 前端验权 */
import router from '@/router'
import { usePermissionStore } from '@/store';
import { storeToRefs } from 'pinia';
const checkPermission = {
  isShow: (key: string, type = 'button') => {
    if (process.env.NODE_ENV === 'development') return true
    const route = router.currentRoute as any
    const rule = (route.value.meta.rule && route.value.meta.rule[type]) || []
    return rule.indexOf(key) > -1
  },
  checkBtnPromission: (name: string) => {
    const { permissionState } = storeToRefs(usePermissionStore())
    return permissionState.value.commonBtnPromissions.indexOf(name) > -1
  }
}

export default checkPermission
