@import './_transition.scss';
// @import './_element.scss';
@import './_antd.scss';

:root{
  /* window */
  --window-header-height: 50px;

  /*page*/
  --page-height:calc(100vh - var(--window-header-height));
  --page-toolbar-height:146px;

  /* table */
  --pagination-height:100px;
  --custom-table-height:calc(var(--page-height) - var(--page-toolbar-height) - var(--pagination-height));

  // 只有一个检索项时 且 居中展示的
  --fp-only-search-width: 450px;
  --fp-bg-color-page: #f0f2f5;

  // header
  --fp-page-padding: 15px;
}

*{
  outline: none;
}
body {
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

html {
  height: 100%;
  box-sizing: border-box;
}

body, html {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
  // background: radial-gradient(ellipse at bottom, #1b2735 0%, #454646 100%);
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}
p {
  margin: 0;
  margin-bottom: 0;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.ellipsis-multiline {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modal-form {
  overflow: auto;
}
