.ant-table {
  .ant-table-title {
    padding: 0 0 8px !important;
  }
  .ant-table-tbody {
    tr.ant-table-row {
      &:hover {
        td {
          background: rgb(245, 245, 245);
        }
      }
    }
  }
}

.ant-card {
  .ant-card-body {
    padding: 20px;
  }
}

.ant-menu {
  &.ant-menu-dark {
    &.ant-menu-inline-collapsed {
      .ant-menu-submenu {
        &.ant-menu-submenu-selected {
          &>.ant-menu-submenu-title {
            background-color: #1677ff;
          }
        }
      }
    }
  }
}

.ant-form {
  .ant-form-item-has-error {
    .custom-select-img {
      border-color: #ff4d4f;
    }
    .ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select {
      border-color: #ff4d4f;
    }
  }
}

.no-margin-form-item {
  margin: 0;
}
