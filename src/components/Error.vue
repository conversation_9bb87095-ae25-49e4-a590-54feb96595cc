<template>
  <a-result status="403" title="403">
    <template #subTitle>
      抱歉，您没有当前页面的权限或页面不存在，请飞书联系管理员: 谷翼涵！
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="router.replace('/')">回到首页</a-button>
      </a-space>
    </template>
  </a-result>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()
</script>

<style lang="scss" scoped>
.error-page {
  height: 100%;
}
</style>
