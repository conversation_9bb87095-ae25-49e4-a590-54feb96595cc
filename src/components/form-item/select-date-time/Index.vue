<template>
  <a-range-picker v-model:value="modelValue" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
    :disabled-date="disabledDate"
    :show-time="showTime()" />
</template>

<script lang="ts">
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'SelectDateTime',

  props: {
    value: {
      type: Array,
      default: () => []
    },
    isDisabledDate: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:value', 'change'],
  setup(_, ctx) {

    const modelValue = computed({
      get: () => {
        if (!_.value) return undefined
        if (_.value.length === 0) return []
        return _.value.map((item: any) => item && dayjs.utc(item * 1000).format('YYYY-MM-DD HH:mm:ss') || undefined)
      },
      set: (val) => {
        let nv
        if (val?.length) {
          nv = [
            dayjs.utc(val[0]).valueOf() / 1000,
            dayjs.utc(val[1]).valueOf() / 1000
          ]
        }

        ctx.emit('update:value', nv)
        ctx.emit('change', nv)
      }
    })

    const disabledDate = (current: any) => {
      // Can not select days before today and today
      return _.isDisabledDate ? current && current < dayjs().startOf('day') : false
    }

    const showTime = () => {

      const hideDisabledOptions = _.isDisabledDate ? true : false
      // 开启关闭限制时，设置默认时间为当前日期当前时分秒，否则设置默认时间为当前时间的00:00:00
      const h = dayjs().hour()
      const m = dayjs().minute()
      const s = dayjs().second()
      const defaultValue = _.isDisabledDate ? [dayjs().hour(h).minute(m).second(s), dayjs().hour(h).minute(m).second(s)] : [dayjs().hour(0).minute(0).second(0), dayjs().hour(h).minute(m).second(s)]

      return {
        hideDisabledOptions,
        defaultValue
      }
    }

    return {
      ...toRefs(_),
      modelValue,
      disabledDate,
      showTime,
      dayjs
    }
  }
})
</script>

<style lang="scss" scoped></style>
