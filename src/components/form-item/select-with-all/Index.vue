<template>
  <a-select
    v-model:value="modelValue"
    :options="computedOptions"
    :placeholder="placeholder"
    mode="multiple"
    allow-clear
  ></a-select>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'SelectWithAll',

  props: {
    value: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择'
    }
  },

  emits: ['update:value', 'change'],
  setup (_, ctx) {

    const modelValue = computed({
      get: () => {
        if (!_.value) return undefined
        if (_.value === 'all') return ['all']
        return _.value.split('|').filter(item => item)
      },
      set: (val) => {
        let nv: string | undefined = undefined
        if (!val || val.length === 0) nv = undefined
        else if (val[0] === 'all') nv = 'all'
        else nv = `|${val.join('|')}|`

        ctx.emit('update:value', nv)
        ctx.emit('change', nv)
      }
    })
    
    const computedOptions = computed(() => {
      const opt = _.options.map(item => {
        if (typeof item === 'string') {
          return { label: item, value: item }
        } else {
          return item
        }
      })
      const allOpt = [{ label: 'ALL', value: 'all' }]
      if (_.value === 'all') {
        return allOpt
      } else if (!_.value) {
        return [ ...allOpt, ...opt ]
      } else {
        return opt
      }
    })
    return {
      ...toRefs(_),
      modelValue,
      computedOptions
    }
  }
})
</script>

<style lang="scss" scoped>

</style>