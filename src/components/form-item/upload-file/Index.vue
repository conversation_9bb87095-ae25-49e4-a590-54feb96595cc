<template>
  <!-- <a-upload
    v-model:file-list="fileList"
    name="file"
    action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
    :headers="headers"
    @change="handleChange"
  >
    <a-button>
      <upload-outlined></upload-outlined>
      Click to Upload
    </a-button>
  </a-upload> -->
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'UploadFile',

  props: {
    value: {
      type: String,
      default: ''
    }
  },
  
  setup (_, _ctx) {
    return {
    }
  }
})
</script>

<style lang="scss" scoped>

</style>