<template>
  <SelectWithAll v-model:value="modelValue" :placeholder="placeholder" :options="configState[type as 'platform' | 'langs' | 'channels']"></SelectWithAll>
</template>

<script lang="ts">
import { storeToRefs } from 'pinia';
import SelectWithAll from '../select-with-all/Index.vue'
import { defineComponent } from 'vue'
import { useConfigStore } from '@/store';
export default defineComponent({
  name: 'SelectWithAllComp',

  components: { SelectWithAll },

  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择操作系统'
    },
    type: {
      type: String,
      default: 'platform',
      validate: (val: string) => ['platform', 'langs', 'channels'].includes(val)
    }
  },

  emits: ['update:value', 'change'],
  setup (_, ctx) {
    const { configState } = storeToRefs(useConfigStore())

    const modelValue = computed({
      get: () => _.value,
      set: (val) => {
        ctx.emit('update:value', val)
        ctx.emit('change', val)
      }
    })

    return {
      ...toRefs(_),
      modelValue,
      configState
    }
  }
})
</script>

<style lang="scss" scoped>

</style>