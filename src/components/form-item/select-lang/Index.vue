<template>
  <div class="lang-select-wrap">
    <a-select
      v-model:value="modelValue"
      allowClear
      showSearch
      placeholder="请输入多语言key"
      :filter-option="false"
      :not-found-content="loading ? undefined : null"
      option-label-prop="label"
      @search="remoteMethod"
      @change="handleChange"
      style="min-width: 200px;"
      :options="options"
    >
      <template #notFoundContent>
        <a-spin size="small" />
      </template>
      <template #option="{ value: val, label }">
        <div class="item">
          <div class="t">{{ label }}</div>
          <div class="b">{{ val }}</div>
        </div>
      </template>
    </a-select>
    <!-- <LangPopover v-model="value"></LangPopover> -->
  </div>
</template>

<script lang="ts">
import { langKeyDetail, langKeySearch } from '@/api/resource';
import { reactive, defineComponent } from 'vue'
export default defineComponent({
  name: 'SelectLang',

  props: {
    value: {
      type: String,
      default: undefined
    },
  },

  emits: ['update:value', 'updateLabel', 'initLabel',],
  setup (_, ctx) {
    const state = reactive({
      loading: false,
      options: [] as Array<{ label: string, value: string }>
    })

    watch(
      () => _.value,
      (nv, ov) => {
        if (nv && !ov && !state.options.length) {
          langKeyDetail({ key: nv }).then(res => {
            state.options.push({
              label: res.zh_cn || res.en,
              value: nv
            })
          })
        }
      }
    )

    const modelValue = computed({
      get: () => {
        if(_.value) {
          // console.log('get modelValue _.value', _.value)
          ctx.emit('initLabel', state.options.find(item => item.value === _.value)?.label)
        }
        return _.value
      },
      set: value => {
        // console.log('set modelValue value', value)
        ctx.emit('update:value', value)
        ctx.emit('updateLabel', state.options.find(item => item.value === value)?.label)
      }
    })

    const remoteMethod = (query: string) => {
      if (!query) {
        state.options = []
        return
      }
      state.loading = true
      langKeySearch({ key: query }).then(res => {
        state.options = res
      }).finally(() => {
        state.loading = false
      })
    }

    const handleChange = (value: string) => {
      ctx.emit('update:value', value)
    }

    return {
      ...toRefs(state),
      modelValue,
      remoteMethod,
      handleChange
    }
  }
})
</script>

<style lang="scss" scoped>
.lang-select-wrap {
  display: flex;
  align-items: center;
  justify-items: flex-start;
}
.item {
  width: 100%;
  overflow: hidden;
  padding: 7px 0;

  div {
    line-height: 1.3;
  }
  .b {
    color: rgb(136, 136, 136);
  }
}
</style>
