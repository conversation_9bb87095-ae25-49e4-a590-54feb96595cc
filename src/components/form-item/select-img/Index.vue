<template>
  <a-upload
    style="height: 110px"
    v-model:file-list="fileList"
    action="#"
    list-type="picture-card"
    accept="image/gif,image/jpeg,image/jpg,image/png,image/svg"
    :before-upload="beforeUpload"
    @preview="() => setVisible(true)"
    @remove="imageUrl = ''"
  >
    <div v-if="!imageUrl">
      <loading-outlined v-if="loading"></loading-outlined>
      <plus-outlined v-else></plus-outlined>
      <div class="ant-upload-text">Upload</div>
    </div>
  </a-upload>
  <a-typography-text type="warning" style="font-size: 12px" v-if="widthHeight.length > 0">
    <ExclamationCircleFilled />
    {{ (tips ? `${tips} ` : `前台展示图片，建议尺寸比例: `) + `${widthHeight[0]} * ${widthHeight[1]}` }}
  </a-typography-text>
  <div :style="{ display: 'none' }">
    <a-image
      :width="200"
      :preview="{
        visible: previewVisible,
        onVisibleChange: setVisible
      }"
      :src="value"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { Form, UploadChangeParam } from "ant-design-vue";
import { uploadImg } from "@/api/resource";
export default defineComponent({
  name: "SelectImg",

  props: {
    value: {
      type: String,
      default: ""
    },
    // 宽高限制 - [width, height]
    widthHeight: {
      type: Array<Number>,
      default: () => []
    },

    tips: {
      type: String,
      default: ""
    }
  },

  emits: ["update:value", "change"],
  setup(_, { emit }) {
    const formItemContext = Form.useInjectFormItemContext();
    const triggerChange = (imgUrl: string) => {
      emit("update:value", imgUrl);
      emit("change", imgUrl);
      formItemContext.onFieldChange();
    };

    const state = reactive({
      loading: false,
      previewVisible: false
    });

    const imageUrl = computed({
      get: () => _.value,
      set: (val: string) => {
        triggerChange(val);
      }
    });
    const fileList = computed(() => {
      if (!imageUrl.value) return [];
      return [
        {
          uid: Math.floor(Math.random() * 100),
          name: "image.png",
          status: "done",
          url: imageUrl.value
        }
      ];
    });

    // const checkSize = async (file: any) => {
    //   return new Promise((resolve, reject) => {
    //     var reader = new FileReader();
    //     reader.onload = function (event) {
    //       const data = event.target?.result
    //       // 加载图片获取图片真实宽度和高度
    //       var image = new Image();
    //       image.onload = function(){
    //         var width = image.width;
    //         var height = image.height;
    //         if (width !== _.widthHeight[0] || height !== _.widthHeight[1]) {
    //           message.error('图片尺寸不符合要求, 要求尺寸: ' + _.widthHeight.join('x'))
    //           reject()
    //         } else {
    //           resolve('success')
    //         }
    //       };

    //       image.onerror = function(){
    //         reject()
    //       };

    //       image.src = data as string
    //     };
    //     reader.readAsDataURL(file);
    //   })
    // }
    const uploadImgs = async (file: File) =>
      new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append("image", file);
        uploadImg(formData)
          .then((res: any) => {
            imageUrl.value = res.url;
            resolve("");
          })
          .catch(() => {
            reject("");
          });
      });

    const beforeUpload = async (file: any) => {
      if (!file) return false;

      try {
        // if (_.widthHeight.length) {
        //   await checkSize(file)
        // }
        // 上传图片
        state.loading = true;
        await uploadImgs(file);
      } catch (error) {}
      state.loading = false;
      return false;
    };

    const handleChange = (info: UploadChangeParam) => {
      console.log("info=====", info);
    };

    const setVisible = (value: boolean): void => {
      state.previewVisible = value;
    };

    return {
      ...toRefs(_),
      ...toRefs(state),
      imageUrl,
      fileList,
      triggerChange,
      beforeUpload,
      handleChange,
      setVisible
    };
  }
});
</script>

<style lang="scss" scoped></style>
