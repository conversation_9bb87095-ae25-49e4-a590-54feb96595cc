<template>
  <div class="custom-select-img">
    <template v-if="url || preview">
      <a-image :src="url || preview" style="width: auto;max-width: 100%; max-height: 100%" :preview="{ visible: previewVisible, onVisibleChange }" @error="imgLoadError">
        <template #previewMask>
          <a-space align="center">
            <EyeOutlined @click="previewVisible = true" />
            <DeleteOutlined @click="triggerChange('')" />
          </a-space>
        </template>
      </a-image>
    </template>
    <div v-else class="img-plus" @click="chooseImg">
      <PlusOutlined />
    </div>
  </div>
</template>

<script lang="ts">
import { getImgMsg } from '@/api/resource'
import bus from '@/lib/bus'
import { defineComponent, onBeforeMount } from 'vue'
import { Form, message } from 'ant-design-vue';
export default defineComponent({
  name: 'SelectImgGallery',

  props: {
    value: {
      type: String,
      default: ''
    },
    preview: {
      type: String,
      default: ''
    }
  },

  emits: ['update:value', 'change'],
  setup (_, { emit }) {
    const formItemContext = Form.useInjectFormItemContext();
    const triggerChange = (imgUrl: string, data: any = {}) => {
      state.url = data.showUrl
      emit('update:value', imgUrl);
      emit('change', data);
      formItemContext.onFieldChange();
    };
    
    const state = reactive({
      url: '',
      isFirst: true,
      previewVisible: false
    })

    // 触发表单 change 校验
    // watch(
    //   () => _.value,
    //   (nv, ov) => {
    //     if (nv && !ov) getImgDetail()
    //   }
    // )

    const chooseImg = () => {
      bus.$emit('showPhotoGallery', {
        callback: (item: any) => {
          triggerChange(item.img_key, item)
        }
      })
    }

    const getImgDetail = () => {
      if (!state.isFirst || !_.value) return
      const params = {
        search: _.value
      }
      getImgMsg(params).then((res: any) => {
        triggerChange(res.img_key, res.preview_img || '')
      }).finally(() => { state.isFirst = false })
    }

    onBeforeMount(() => {
      getImgDetail()
    })

    const onVisibleChange = (visible: boolean) => {
      if (visible === false) {
        state.previewVisible = false
      }
    }

    const imgLoadError = () => {
      message.error('选择的图片已不存在，请重新选择！')
      triggerChange('')
    }

    return {
      ...toRefs(state),
      chooseImg,
      onVisibleChange,
      imgLoadError,
      triggerChange
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-select-img {
  position: relative;
  width: 100px;
  height: 100px;
  font-size: 0;
  background: rgba(0, 0, 0, 0.02);
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  padding: 2px;
  &:hover {
    border-color: #1677ff;
  }
  ::v-deep(.ant-image) {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    overflow: hidden;
    .ant-image-mask {
      cursor: auto;
      font-size: 0;
      background-color: rgba(0, 0, 0, 0.3);
      .anticon {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.65);
        cursor: pointer;
        &:hover {
          color: #fff;
        }
      }
    }
  }
  .img-plus {
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 18px;
    color: #999;
  }
}
</style>
