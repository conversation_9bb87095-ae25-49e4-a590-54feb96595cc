<script lang="ts">export default defineComponent({ name: '<PERSON><PERSON><PERSON>' })</script>
<script setup lang="ts">
import { langKeyDetail } from '@/api/resource';

const props = withDefaults(defineProps<{
  langKey: string,
  i18n_name?: Array<{ content: string, key: string }>
}>(), {
  langKey: '',
  i18n_name: () => []
})

const langDetails = ref<Record<string, string>>({})
const loading = ref(false)

const getLangDetails = () => {
  if (JSON.stringify(langDetails.value) !== '{}') return
  loading.value = true
  langKeyDetail({ key: props.langKey }).then(res => {
    langDetails.value = res
  }).finally(() => loading.value = false)
}
</script>

<template>
  {{ (props.i18n_name.find(item => item.key === props.langKey) || {}).content || props.langKey || '-' }}
  <a-popover placement="rightTop" @openChange="getLangDetails" :overlayInnerStyle="{ 'max-height': '500px', 'overflow-y': 'auto' }" v-if="props.langKey">
    <template #content>
      <a-spin v-if="loading"></a-spin>
      <a-typography-text v-else-if="!loading && JSON.stringify(langDetails) === '[]'" type="secondary">无数据</a-typography-text>
      <a-descriptions v-else :column="1" size="small" bordered :labelStyle="{ width: '88px', 'white-space': 'nowrap' }" :contentStyle="{ 'max-width': '400px' }">
        <a-descriptions-item v-for="(value, key) in langDetails" :key="key" :label="key">{{ value }}</a-descriptions-item>
      </a-descriptions>
    </template>
    <ExclamationCircleOutlined />
  </a-popover>
</template>

<style lang="scss" scoped>

</style>
