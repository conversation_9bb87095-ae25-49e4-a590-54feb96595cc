<script lang="ts">export default defineComponent({ name: 'FilterCell' })</script>
<script setup lang="ts">
import { COMMON_TYPES } from '@/enum';
import { useConfigStore } from '@/store';

const { getConfItem } = useConfigStore()
const props = defineProps({
  record: {
    type: Object,
    default: () => ({})
  }
})
</script>

<template>
  <template v-if="props.record.is_filter === COMMON_TYPES.CLOSE">关闭</template>
  <template v-else>
    <a-tooltip placement="left" :color="'white'" destroyTooltipOnHide :overlayStyle="{ 'max-width': '350px' }">
      <template #title>
        <a-descriptions :column="1" size="small" bordered :labelStyle="{ width: '88px', 'white-space': 'nowrap' }">
          <a-descriptions-item label="操作系统">
            <template v-if="props.record.f_os === 'all'">ALL</template>
            <template v-else><a-tag v-for="item in props.record.f_os.split('|').filter((item: string) => item)" :key="item">{{ getConfItem('platform', item)?.label }}</a-tag></template>
          </a-descriptions-item>
          <a-descriptions-item label="语种">
            <template v-if="props.record.lang === 'all'">ALL</template>
            <template v-else><a-tag v-for="item in props.record.lang.split('|').filter((item: string) => item)" :key="item">{{ getConfItem('langs', item)?.label }}</a-tag></template>
          </a-descriptions-item>
          <a-descriptions-item label="渠道">
            <template v-if="props.record.f_channel === 'all'">ALL</template>
            <template v-else><a-tag v-for="item in props.record.f_channel.split('|').filter((item: string) => item)" :key="item">{{ getConfItem('channels', item)?.label }}</a-tag></template>
          </a-descriptions-item>
          <a-descriptions-item label="服务器">{{ record.f_s_ids }}</a-descriptions-item>
          <a-descriptions-item label="城堡等级">{{ record.f_lv_ids }}</a-descriptions-item>
          <a-descriptions-item label="uid 白名单" v-if="record.uids !== undefined">{{ record.uids || '-' }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <a-typography-link>开启</a-typography-link>
    </a-tooltip>
  </template>
</template>

<style lang="scss" scoped>

</style>