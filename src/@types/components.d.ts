// Vue 组件类型声明文件

import { DefineComponent } from 'vue'

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    // 全局注册的组件类型声明
    SelectDateTime: DefineComponent<{
      value?: [number, number]
      'onUpdate:value'?: (value: [number, number] | undefined) => void
    }>

    SelectLang: DefineComponent<{
      value?: string
      'onUpdate:value'?: (value: string) => void
    }>
  }
}

// 组件属性类型定义
export interface SelectDateTimeProps {
  value?: [number, number]
  'onUpdate:value'?: (value: [number, number] | undefined) => void
}

export interface SelectLangProps {
  value?: string
  'onUpdate:value'?: (value: string) => void
}

export {}
