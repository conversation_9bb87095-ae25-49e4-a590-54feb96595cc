// Store 类型声明文件

import type { ConfigState, TConfItem, TConfigKeys, GameChannelItem } from '@/store/config'
import type { UserStateT, IGameConfig, UserInfoT } from '@/store/user'
import type { PermissionState } from '@/store/permission'
import type { ISystemState } from '@/store/system'

// 重新导出所有 store 相关类型
export {
  // Config Store 类型
  ConfigState,
  TConfItem,
  TConfigKeys,
  GameChannelItem,

  // User Store 类型
  UserStateT,
  IGameConfig,
  UserInfoT,

  // Permission Store 类型
  PermissionState,

  // System Store 类型
  ISystemState
}

// Store 实例类型定义（用于 storeToRefs 等场景）
export interface ConfigStoreType {
  configState: ConfigState
  getConfItem: (key: TConfigKeys, val: string | number) => TConfItem | undefined
  FETCH_GLOBAL_CONFIG: (isForce?: boolean) => Promise<void>
  getGameChannel: (game_project: string) => TConfItem[]
}

export interface UserStoreType {
  userState: UserStateT
  login: (userInfo: UserInfoT) => void
  logout: () => void
  setTicket: (ticket: string | null) => void
  saveBtnPromis: (data: any[]) => void
  FETCH_PERMISSION: () => Promise<string>
  FETCH_GAME_PERMISSION: () => Promise<string>
}

export interface PermissionStoreType {
  permissionState: PermissionState
  setPermissionMap: (data: Record<string, string>) => void
  setRoutes: (routes: any[]) => void
  setBtnPromise: (routes: any[]) => void
  SET_ROUTES: (permissionList: any[]) => void
  setSubRoutes: (routes: any[]) => void
}

export interface SystemStoreType {
  systemState: ISystemState
  setAsideStatus: () => void
  setCrtEnv: (env: string) => void
}

// 联合类型，方便在模板中使用
export type AllStoreTypes = ConfigStoreType | UserStoreType | PermissionStoreType | SystemStoreType

// 常用的 store 状态类型映射
export interface StoreStateMap {
  config: ConfigState
  user: UserStateT
  permission: PermissionState
  system: ISystemState
}

// Store 名称枚举
export enum StoreNames {
  CONFIG = 'config',
  USER = 'user',
  PERMISSION = 'permission',
  SYSTEM = 'system'
}

export {}
