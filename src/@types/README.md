# TypeScript 智能提示增强说明

本项目已经配置了增强的 TypeScript 智能提示功能，包括：

## 功能特性

### 1. 悬浮提示 (Hover Information)

- **Command + 鼠标悬浮**：在方法、变量、组件上悬浮鼠标，可以看到详细的类型信息和文档说明
- 支持显示参数类型、返回值类型、JSDoc 注释等

### 2. 跳转到定义 (Go to Definition)

- **Command + 鼠标点击**：直接跳转到方法或变量的定义位置
- **F12**：键盘快捷键跳转到定义
- **Command + F12**：跳转到实现位置

### 3. 查找引用 (Find All References)

- **Shift + F12**：查找所有引用位置
- **Option + Shift + F12**：查找引用（在侧边栏显示）

### 4. 自动导入 (Auto Import)

- 智能提示时自动显示可导入的模块
- 支持路径别名 `@/` 的自动导入
- 支持 Vue、Ant Design Vue 组件的自动导入

## 已优化的文件类型

### API 文件

- `src/api/pointsStore.ts` - 积分商城相关 API，包含完整的类型定义和 JSDoc 注释
- 所有 API 方法都有详细的参数类型和返回值类型

### 工具函数

- `src/utils/index.ts` - 通用工具函数，包含类型安全的 `setDefaultFilter` 等方法

### Store 类型

- `src/store/config.ts` - 配置管理 Store，包含完整的类型定义
- `src/store/user.ts` - 用户管理 Store，包含用户状态和权限类型
- `src/store/permission.ts` - 权限管理 Store，包含路由和按钮权限类型
- `src/store/system.ts` - 系统管理 Store，包含系统状态类型
- `src/@types/store.d.ts` - Store 统一类型声明文件

### 组件类型

- `src/@types/components.d.ts` - Vue 组件的类型声明
- `src/@types/global.d.ts` - 全局类型定义

## 使用示例

### API 调用智能提示

```typescript
import { createProps, getPropsDetail } from '@/api/pointsStore'

// 悬浮时显示参数类型和文档
createProps({
  cost_coin: 100,
  gift_id: 1
  // ... 其他参数会有智能提示
})

// Command + 点击 getPropsDetail 跳转到定义
const detail = await getPropsDetail(123)
```

### 工具函数智能提示

```typescript
import { setDefaultFilter } from '@/utils'

// 悬浮时显示完整的类型信息和使用说明
setDefaultFilter(formState.value, ['f_s_ids'])
```

### Store 使用智能提示

```typescript
import { useConfigStore } from '@/store'
import { storeToRefs } from 'pinia'

// 完整的类型推断和智能提示
const { configState } = storeToRefs(useConfigStore())

// 方法调用时显示参数类型和返回值类型
const confItem = getConfItem('langs', 'zh_cn')

// Command + 点击跳转到 store 定义
const { FETCH_GLOBAL_CONFIG } = useConfigStore()
```

## 配置文件说明

### TypeScript 配置

- `tsconfig.json` - 增强的 TypeScript 配置，支持更好的类型检查和路径解析
- 启用了 `declaration`、`declarationMap`、`sourceMap` 等选项

### VSCode 配置

- `.vscode/settings.json` - 优化的 VSCode 设置，包括：
  - TypeScript 智能提示增强
  - 悬浮信息配置
  - 跳转行为配置
  - 自动导入配置

### Vite 配置

- `vite.config.ts` - 增强的自动导入配置，支持：
  - Vue 3 组合式 API 自动导入
  - Ant Design Vue 组件自动导入
  - 工具函数目录自动扫描

## 故障排除

如果智能提示功能不正常，请尝试：

1. **重启 TypeScript 服务**：

   - 在 VSCode 中按 `Command + Shift + P`
   - 输入 "TypeScript: Restart TS Server"

2. **重新加载窗口**：

   - 按 `Command + Shift + P`
   - 输入 "Developer: Reload Window"

3. **检查插件**：

   - 确保安装了 Vue Language Features (Volar)
   - 确保安装了 TypeScript Importer

4. **清理缓存**：
   ```bash
   npm run dev -- --force
   ```

## 最佳实践

1. **使用 JSDoc 注释**：为函数添加详细的文档注释
2. **明确的类型定义**：为复杂的数据结构定义 interface
3. **合理使用泛型**：为可重用的函数添加泛型约束
4. **路径别名**：使用 `@/` 而不是相对路径，获得更好的智能提示
