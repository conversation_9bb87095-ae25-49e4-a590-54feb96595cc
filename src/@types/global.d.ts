// 全局类型定义文件

declare global {
  // 扩展 Window 接口
  interface Window {
    // 添加可能的全局变量
    __APP_VERSION__?: string
  }
}

// Vue 组件相关类型
export interface BaseResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 通用分页参数
export interface PaginationParams {
  page?: number
  page_size?: number
}

// 通用分页响应
export interface PaginationResponse<T = any> {
  data: T[]
  total: number
  page: number
  page_size: number
}

// 表单状态接口
export interface FormState {
  [key: string]: any
}

// 选项数据结构
export interface OptionItem {
  label: string
  value: string | number
  disabled?: boolean
}

// 筛选器相关类型
export interface FilterFormData {
  is_filter?: number
  f_os?: string
  f_channel?: string
  lang?: string
  f_s_ids?: string
  f_lv_ids?: string
}

export {}
