import { createRouter, createWebHistory } from 'vue-router'
import permissionModules from './permissionModules'
import constantModules from './constantModules'
import { MenuItemRouter } from '@/@types/rotuer';

export const constantRoutes: Array<MenuItemRouter> = [
  ...constantModules
]

export const asyncRoutes: Array<MenuItemRouter> = [
  ...permissionModules.sort((prev, next) => (prev.index - next.index))
]

export function resetRouter () {
  const newRouter = router;
  (router as any).matcher = (newRouter as any).matcher // reset router
}

const router = createRouter({
  history: createWebHistory('/'),
  routes: constantRoutes
})

export default router
