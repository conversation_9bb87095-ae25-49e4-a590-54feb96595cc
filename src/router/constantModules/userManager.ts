import { RouteRecordRaw } from 'vue-router'
import Login from '@/views/login/Index.vue'
import Error from '@/components/Error.vue'
import Redirect from '@/components/Redirect.vue'

const UserManagerRouter: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { hidden: true, title: '登录' }
  },
  {
    path: '/redirect/:char(\\S+)+',
    name: 'Redirect',
    component: shallowRef(Redirect),
    meta: { hidden: true }
  },
  {
    path: '/error',
    redirect: '/403',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/403',
        name: 'Error',
        component: Error,
        meta: {
          hidden: true
        }
      }
    ]
  }
]
export default UserManagerRouter
