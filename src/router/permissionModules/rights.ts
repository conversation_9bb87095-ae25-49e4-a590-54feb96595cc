import { RouteRecordRaw } from 'vue-router'

const rightsRouter: Array<RouteRecordRaw> = [
  {
    index: 6,
    path: '/rights',
    name: 'Rights',
    redirect: '/rights/entrance',
    meta: {
      title: '权益配置',
      icon: 'RightSquareOutlined'
    },
    children: [
      {
        path: '/rights/entrance',
        name: 'RightsEntrance',
        component: () => import(/* webpackChunkName: 'rights' */ '@/views/rightAndInterests/entrance/Index.vue'),
        meta: {
          title: '权益入口',
          icon: ''
        }
      },
      {
        path: '/rights/gift',
        name: 'RightsGift',
        component: () => import(/* webpackChunkName: 'rights' */ '@/views/rightAndInterests/exclusiveGift/Index.vue'),
        meta: {
          title: '专属礼包',
          icon: '',
          not_funplus_zone: true
        }
      }
    ]
  }
]

export default rightsRouter
