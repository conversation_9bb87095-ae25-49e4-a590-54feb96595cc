import { RouteRecordRaw } from 'vue-router'

const dailyCheckRouter: Array<RouteRecordRaw> = [
  {
    index: 4,
    path: '/dailyCheck',
    name: 'DailyCheck',
    redirect: '/dailyCheck/list',
    meta: {
      title: '签到活动',
      icon: 'TagsOutlined',
      not_funplus_zone: true
    },
    children: [
      {
        path: '/dailyCheck/list',
        name: 'DailyCheckList',
        component: () => import(/* webpackChunkName: 'dailycheck' */ '@/views/dailyCheck/Index.vue'),
        meta: {
          title: '月签到（旧）'
        }
      },
      // 旧版本详情
      {
        path: '/dailyCheck/detail/:id',
        name: 'DailyCheckDetail',
        component: () => import(/* webpackChunkName: 'dailycheck' */ '@/views/dailyCheck/Detail.vue'),
        meta: {
          title: '奖励详情',
          hidden: true,
          icon: '',
          activeMenu: '/dailyCheck/list'
        }
      },
      {
        path: '/dailyCheck/new',
        name: 'DailyCheckNew',
        component: () => import(/* webpackChunkName: 'DailyCheckNew' */ '@/views/dailyCheck/new/Index.vue'),
        meta: {
          title: '月签到（新）'
        }
      },
      // 新版本详情
      {
        path: '/dailyCheck/new/detail/:id',
        name: 'DailyCheckNewDetail',
        component: () => import(/* webpackChunkName: 'DailyCheckNewDetail' */ '@/views/dailyCheck/new/Detail.vue'),
        meta: {
          title: '奖励详情',
          hidden: true,
          icon: '',
          activeMenu: '/dailyCheck/new'
        }
      },
      {
        path: '/dailyCheck/weekly',
        name: 'DailyCheckWeekly',
        component: () => import(/* webpackChunkName: 'DailyCheckWeekly' */ '@/views/dailyCheck/weekly/Index.vue'),
        meta: {
          title: '周签到'
        }
      },
      // 周签到详情
      {
        path: '/dailyCheck/weekly/detail/:id',
        name: 'DailyCheckWeeklyDetail',
        component: () => import(/* webpackChunkName: 'DailyCheckWeeklyDetail' */ '@/views/dailyCheck/weekly/Detail.vue'),
        meta: {
          title: '奖励详情',
          hidden: true,
          icon: '',
          activeMenu: '/dailyCheck/weekly'
        }
      },
      // 签到规则
      {
        path: '/dailyCheck/rule',
        name: 'DailyCheckRule',
        component: () => import(/* webpackChunkName: 'DailyCheckRule' */ '@/views/dailyCheck/rule/Index.vue'),
        meta: {
          title: '签到规则'
        }
      }
    ]
  }
]

export default dailyCheckRouter
