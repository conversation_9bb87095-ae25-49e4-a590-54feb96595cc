import { RouteRecordRaw } from 'vue-router'

const resourceRoutes: RouteRecordRaw[] = [
  {
    index: 8,
    path: '/resource',
    name: 'Resource',
    redirect: '/resource/language',
    meta: {
      title: '资源管理',
      icon: 'DropboxOutlined'
    },
    children: [
      {
        path: '/resource/language',
        name: 'ResourceLanguage',
        component: () => import(/* webpackChunkName: 'resource' */ '@/views/resourceManage/language/Index.vue'),
        meta: {
          title: '多语言管理',
          icon: ''
        }
      },
      // {
      //   path: '/resource/picture',
      //   name: 'ResourcePicture',
      //   component: () => import(/* webpackChunkName: 'resource' */ '@/views/resourceManage/picture/Index.vue'),
      //   meta: {
      //     title: '图片库管理',
      //     icon: ''
      //   }
      // },
      {
        path: '/resource/gift',
        name: 'ResourceGift',
        component: () => import(/* webpackChunkName: 'resource' */ '@/views/resourceManage/gift/Index.vue'),
        meta: {
          title: '礼包列表',
          icon: ''
        }
      },
      {
        path: '/resource/props',
        name: 'ResourceProps',
        component: () => import(/* webpackChunkName: 'resource' */ '@/views/resourceManage/props/Index.vue'),
        meta: {
          title: '道具表管理',
          icon: ''
        }
      }
    ]
  }
]

export default resourceRoutes
