import { RouteRecordRaw } from 'vue-router'

const minigameRouter: Array<RouteRecordRaw> = [
  {
    index: 9,
    path: '/user',
    name: 'User',
    redirect: '/user/list',
    meta: {
      title: '用户管理',
      icon: 'UsergroupAddOutlined'
    },
    children: [
      // 列表
      {
        path: '/user/list',
        name: 'UserList',
        component: () => import(/* webpackChunkName: 'user' */ '@/views/userManage/Index.vue'),
        meta: {
          title: '用户管理',
          icon: '',
          breadcrumb: false
        }
      }
    ]
  }
]

export default minigameRouter
