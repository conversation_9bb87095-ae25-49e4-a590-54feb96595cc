import { RouteRecordRaw } from 'vue-router'

const minigameRouter: Array<RouteRecordRaw> = [
  {
    index: 3,
    path: '/minigame',
    name: 'Minigame',
    redirect: '/minigame/list',
    meta: {
      title: '小游戏',
      icon: 'TrophyOutlined'
    },
    children: [
      // 列表
      {
        path: '/minigame/list',
        name: 'MinigameList',
        component: () => import(/* webpackChunkName: 'minigame' */ '@/views/minigame/Index.vue'),
        meta: {
          title: '小游戏',
          icon: '',
          breadcrumb: false
        }
      }
    ]
  }
]

export default minigameRouter
