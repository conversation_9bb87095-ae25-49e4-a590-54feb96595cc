import { RouteRecordRaw } from 'vue-router'

const pushMessageRouter: Array<RouteRecordRaw> = [
  {
    index: 5,
    path: '/pushmessage',
    name: 'Pushmessage',
    redirect: '/pushmessage/list',
    meta: {
      title: '消息推送',
      icon: 'MessageOutlined'
    },
    children: [
      {
        path: '/pushmessage/list',
        name: 'PushmessageList',
        component: () => import(/* webpackChunkName: 'pushMessage' */ '@/views/pushMessage/Index.vue'),
        meta: {
          title: '消息推送',
          icon: '',
          breadcrumb: false
        }
      }
    ]
  }
]

export default pushMessageRouter
