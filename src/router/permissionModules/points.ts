import { RouteRecordRaw } from 'vue-router'

const pointsRouter: Array<RouteRecordRaw> = [
  {
    index: 7,
    path: '/points',
    name: 'Points',
    redirect: '/points/task',
    meta: {
      title: '积分系统',
      icon: 'ProjectOutlined'
    },
    children: [
      // 列表
      {
        path: '/points/task',
        name: 'PointsTask',
        component: () => import(/* webpackChunkName: 'PointsTask' */ '@/views/pointsSystem/task/Index.vue'),
        meta: {
          title: '任务体系',
          icon: ''
        }
      },
      {
        path: '/points/store',
        name: 'PointsStore',
        component: () => import(/* webpackChunkName: 'pointsStore' */ '@/views/pointsSystem/store/Index.vue'),
        meta: {
          title: '积分商城',
          icon: ''
        }
      },
    ]
  }
]

export default pointsRouter
