import { RouteRecordRaw } from 'vue-router'

const dataManageRouter: Array<RouteRecordRaw> = [
  {
    index: 10,
    path: '/datamanage',
    name: 'DataManage',
    redirect: '/datamanage/points',
    meta: {
      title: '数据明细',
      icon: 'DatabaseOutlined'
    },
    children: [
      // SDK 金刚位
      {
        path: '/datamanage/points',
        name: 'DataManagePoints',
        component: () => import(/* webpackChunkName: 'datamanage' */ '@/views/dataManage/points/Index.vue'),
        meta: {
          title: '积分明细',
          icon: '',
        }
      },
      // H5 金刚区
      {
        path: '/datamanage/growth',
        name: 'DataManageGrowth',
        component: () => import(/* webpackChunkName: 'datamanage' */ '@/views/dataManage/growth/Index.vue'),
        meta: {
          title: '成长值明细',
          icon: '',
        }
      }
    ]
  }
]

export default dataManageRouter
