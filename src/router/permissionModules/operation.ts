import { RouteRecordRaw } from "vue-router";
import RouterComp from "@/layout/component/RouteComp.vue";

const operationRouter: Array<RouteRecordRaw> = [
  {
    index: 2,
    path: "/operation",
    name: "Operation",
    redirect: "/operation/sdkvajra",
    meta: {
      title: "运营位",
      icon: "FlagOutlined"
    },
    children: [
      // SDK 金刚位
      {
        path: "/operation/sdkvajra",
        name: "OperationSDKVajra",
        component: () => import(/* webpackChunkName: 'operation' */ "@/views/operation/SDKVajra/Index.vue"),
        meta: {
          title: "SDK金刚区",
          icon: ""
        }
      },
      // H5 金刚区
      {
        path: "/operation/h5vajra",
        name: "OperationH5Vajra",
        component: () => import(/* webpackChunkName: 'operation' */ "@/views/operation/H5Vajra/Index.vue"),
        meta: {
          title: "H5金刚区",
          icon: ""
        }
      },
      // banner组
      {
        path: "/operation/banner",
        name: "OperationBanner",
        component: shallowRef(RouterComp),
        redirect: "/operation/banner/group",
        meta: {
          title: "活动Banner",
          icon: ""
        },
        children: [
          {
            path: "/operation/banner/group",
            name: "OperationBannerList",
            component: () => import(/* webpackChunkName: 'operation' */ "@/views/operation/activityBanner/Index.vue"),
            meta: {
              title: "活动Banner",
              icon: "",
              breadcrumb: false
            }
          },
          // banner详情
          {
            path: "/operation/banner/detail/:id",
            name: "OperationBannerDetail",
            component: () => import(/* webpackChunkName: 'operation' */ "@/views/operation/activityBanner/Detail.vue"),
            meta: {
              title: "Banner详情",
              icon: "",
              hidden: true,
              permission: "OperationBannerList"
            }
          }
        ]
      },
      // SDK 特殊图标推送
      {
        path: "/operation/sdkiconspush",
        name: "OperationSDKIconsPush",
        component: () => import(/* webpackChunkName: 'operation' */ "@/views/operation/IconsPush/Index.vue"),
        meta: {
          title: "SDK特殊图标推送",
          icon: "",
          not_funplus_zone: true
        }
      },
      // SDK 广告位
      {
        path: "/operation/sdkads",
        name: "OperationSDKAds",
        component: () => import(/* webpackChunkName: 'operation' */ "@/views/operation/SDKAds/Index.vue"),
        meta: {
          title: "SDK广告位",
          icon: "",
          not_funplus_zone: true
        }
      }
    ]
  }
];

export default operationRouter;
