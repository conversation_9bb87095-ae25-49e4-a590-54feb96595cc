import { RouteRecordRaw } from "vue-router";

const platformConfRouter: Array<RouteRecordRaw> = [
  {
    index: 1,
    path: "/platform",
    name: "Platform",
    redirect: "/platform/entrance",
    meta: {
      title: "平台配置",
      icon: "GoldOutlined",
      funplus_zone: true
    },
    children: [
      // 游戏接入
      {
        path: "/platform/game",
        name: "PlatformGame",
        component: () => import(/* webpackChunkName: 'platform' */ "@/views/platformConfig/games/Index.vue"),
        meta: {
          title: "游戏接入",
          icon: ""
        }
      },
      // 入口管理
      {
        path: "/platform/entrance",
        name: "PlatformEntrance",
        component: () => import(/* webpackChunkName: 'platform' */ "@/views/platformConfig/entrance/Index.vue"),
        meta: {
          title: "入口管理",
          icon: ""
        }
      },
      // 首页内容
      // {
      //   path: '/platform/home',
      //   name: 'PlatformHome',
      //   component: () => import(/* webpackChunkName: 'platform' */ '@/views/platformConfig/homepage/Index.vue'),
      //   meta: {
      //     title: '首页内容',
      //     icon: '',
      //   }
      // },
      // 通用规则
      {
        path: "/platform/rule",
        name: "PlatformRule",
        component: () => import(/* webpackChunkName: 'platform' */ "@/views/platformConfig/commonConf/Index.vue"),
        meta: {
          title: "通用规则",
          icon: ""
        }
      },
      {
        path: "/platform/combinerule",
        name: "CombineRule",
        component: () => import("@/views/platformConfig/combinerule/Index.vue"),
        meta: {
          title: "融合开关",
          icon: ""
        }
      }
    ]
  }
];

export default platformConfRouter;
