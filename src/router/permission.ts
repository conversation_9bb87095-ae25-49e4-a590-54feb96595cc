import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import router from '@/router'
import { RouteLocationNormalized } from 'vue-router'
import { useConfigStore, usePermissionStore, useUserStore } from '../store'
import { storeToRefs } from 'pinia'
import { MenuItemRouter } from '../@types/rotuer'
import { visitorPageList } from '@/enum'
NProgress.configure({ showSpinner: false })

router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: any) => {
  // console.log('to -- ', to)
  // console.log('from -- ', from)
  // 指定游戏项目
  if (to.query.game_project) {
    localStorage.setItem('crtGame', to.query.game_project as string)
  }

  if (to.path === '/' && !localStorage.getItem('ticket')) {
    next({ path: 'login', replace: true })
    return
  }
  if (to.name === 'Login') {
    if (to.query.ticket) {
      localStorage.setItem('ticket', to.query.ticket as string)
    } else if (localStorage.getItem('ticket')) {
    } else return next()
  }
  if (to.name === 'Error' && from.name === 'Error') return next()
  const userStore = useUserStore()
  const { setTicket, FETCH_PERMISSION, FETCH_GAME_PERMISSION } = userStore
  const { userState } = storeToRefs(userStore)
  const permissionStore = usePermissionStore()
  const { permissionState } = storeToRefs(permissionStore)
  const { SET_ROUTES, setBtnPromise } = permissionStore
  const { FETCH_GLOBAL_CONFIG } = useConfigStore()

  // Start progress bar
  NProgress.start()

  if (localStorage.getItem('ticket')) {
    setTicket(localStorage.getItem('ticket'))
  }

  // 已登录
  if (userState.value.isLogin && to.path !== '/') {
    NProgress.done()
    return next()
  }

  // 没有 ticket 可访问的页面
  if (visitorPageList.indexOf(to.path) > -1 && !localStorage.getItem('ticket')) {
    NProgress.done()
    return next()
  }

  try {
    await FETCH_GAME_PERMISSION()
    await FETCH_PERMISSION()

    // 添加动态路由权限
    const permissionList = userState.value.userInfo.permission_list
    SET_ROUTES(permissionList as [])
    setBtnPromise(permissionList as MenuItemRouter[])
    permissionState.value.routes.forEach((route) => {
      router.addRoute(route)
    })
    NProgress.done()
    if (permissionState.value.dynamicRoutes.length === 0) {
      next({ path: '/403', replace: true })
    } else {
      FETCH_GLOBAL_CONFIG()
      if (to.path === '/login' || to.path === '/') {
        next({ path: permissionState.value.dynamicRoutes[0].path })
      } else {
        // 直接用 next() 无效, 重新跳一遍，确保使用新路由
        next({ ...to, replace: true })
      }
    }
  } catch (e) {
    if (e === 'no game infos') {
      // 没有游戏信息
      next({ path: '/403' })
    } else {
      localStorage.removeItem('ticket')
      next({ path: '/login' })
    }
    NProgress.done()
  }
})

router.afterEach((to) => {
  // console.log(to)
  // Finish progress bar
  NProgress.done()

  // title
  const titleNameList = to.matched
    .filter((item) => item.meta?.breadcrumb !== false && item.meta?.title)
    .map((item) => item.meta?.title)
    .reverse()
  document.title = (titleNameList.length ? titleNameList.join(' - ') + ' - ' : '') + '私域管理平台'
})
