import { PAGE_CONF } from '@/enum'
import Request from '@/server'
import type { ConfigState } from '@/store/config'

export const IMPORT_CONFIG = {
  [PAGE_CONF.PLATFORM_PROPS]: '/api/items/import', // 道具上传
  [PAGE_CONF.MULTILINGUAL]: '/api/lang/upload', // 多语言库
  [PAGE_CONF.DAILY_CHECK]: '/api/active_checkin/update_reward', // 签到奖励
  [PAGE_CONF.LADDERCONFIGS]: '/api/vip-right-config/upload-ladder-configs', // 权益入口-上传梯度配置
  [PAGE_CONF.RIGHTGIFT]: '/api/member-gift/import', // 专享礼包-批量上传
  [PAGE_CONF.TASKCONFIG]: '/api/task-config/import', // 积分任务-批量上传
  [PAGE_CONF.PRODUCTCONFIG]: '/api/points-mall-product/import', // 积分商城-批量上传
  [PAGE_CONF.TASKRULE]: '/api/task-config/upload-rule-desc', // 积分任务-任务说明
  [PAGE_CONF.GIFTCONFIG]: '/api/gift-package/import', // 礼包列表导出模版
  [PAGE_CONF.DAILY_CHECK_WEEKLY]: '/api/active-checkin-weekly/update-config', // 周签到-批量上传
  [PAGE_CONF.DAILY_CHECK_NEW]: '/api/active-checkin-v2/update-config' // 新版月签到-批量上传
}

/**
 * 上传文件 - 通过参数控制走不同的接口/业务逻辑
 * @param page 页面参数,通过参数分发不同的实际的上传接口
 * @param params 上传需要的参数
 * @returns Promise<any>
 */
export const importFile = (page: string, params: any): Promise<any> => {
  // 如果是周签到或者新版月签到，则需要将params转换为FormData
  if (page === PAGE_CONF.DAILY_CHECK_WEEKLY || page === PAGE_CONF.DAILY_CHECK_NEW) {
    const id = params.get('id')
    return Request.put(`${IMPORT_CONFIG[page]}/${id}`, params)
  }
  return Request.post(IMPORT_CONFIG[page], params)
}

/**
 * 获取日志列表
 * @param params 查询参数
 * @returns Promise<any>
 */
export const getLog = (params: any): Promise<any> => Request.get('/api/resource/log/list', params)

/**
 * 获取全局配置
 * @returns Promise<ConfigState> 全局配置数据
 */
export const getGlobalConfig = (): Promise<ConfigState> => Request.get('/api/common/public-enums')

/**
 * 下载模板文件
 * @param file_type 文件类型
 * @returns Promise<Blob>
 */
export const downloadTemp = (file_type: string): Promise<Blob> => Request.downfile('/api/common/downloadTemplate', { file_type })
