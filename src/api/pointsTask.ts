import Request from '@/server'

/** 积分任务 start */
// * 列表
export const getPropsList = (params: any) => Request.get('/api/task-config', params)
// * 创建
export const createProps = (params: any) => Request.post('/api/task-config', params)
// * 更新内容
export const updateProps = (id: number, params: any) => Request.put(`/api/task-config/${id}`, params)
// * 导出配置
export const downloadProps = () => Request.downfile('/api/task-config/export')
// * 获取详情
export const getPropsDetail = (id: number) => Request.get(`/api/task-config/${id}`)
// * 删除
export const delProps = ({ petId }: { petId: number }) => Request.delete(`/api/task-config/${petId}`)
// * 更新状态
export const updatePropsStatus = (id: number, status: number) => Request.put(`/api/task-config/switch-status/${id}`, { status })
// * 复制
export const copyProps = ({ id }: {id: number}) => Request.put(`/api/task-config/copy/${id}`)
// 导出列表
export const downloadList = () => Request.downfile(`/api/task-config/export`)
// 下载任务说明模板
export const exportTaskRule = (params: any) => Request.downfile(`/api/task-config/export-rule-desc`, params)
/** 积分任务 end */
