import Request from '@/server'

/** 礼包 start */
// *礼包列表
export const getPropsList = (params: any) => Request.get('/api/member-gift', params)
// *礼包列表 - 下拉搜索用
export const getPropsOpts = (search_key: string) => Request.get('/api/member-gift/search', { search_key })
// * 创建
export const createProps = (params: any) => Request.post('/api/member-gift', params)
// * 更新内容
export const updateProps = (id: number, params: any) => Request.put(`/api/member-gift/${id}`, params)
// * 导出配置
export const downloadProps = () => Request.downfile('/api/member-gift/export')
// * 获取详情
export const getPropsDetail = (id: number) => Request.get(`/api/member-gift/${id}`)
// * 删除
export const delProps = ({ petId }: { petId: number }) => Request.delete(`/api/member-gift/${petId}`)
// * 更新状态
// PUT/api/member-gift/set-status/{id}
export const updatePropsStatus = (id: number, status: number) => Request.put(`/api/member-gift/set-status/${id}`, { status })
// * 复制
export const copyProps = ({ id }: {id: number}) => Request.put(`/api/member-gift/copy/${id}`)
// 导出
export const downloadConf = (params: any) => Request.downfile('/api/member-gift/export', params)
/** 礼包 end */
