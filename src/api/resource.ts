import Request from '@/server'

/** 多语言 start */
// * 获取列表
export const LangGetData = (params: any) => Request.get('/api/lang', params)
// * 删除
export const LangDestroy = ({ id }: { id: number }) => Request.delete(`/api/lang/${id}`)
// * 导出配置
export const downloadLangConf = () => Request.downfile('/api/lang/export')
// * 多语言下拉搜索
export const langKeySearch = (params: any) => Request.get('/api/lang/keySearch', params)
// * 多语言详情
export const langKeyDetail = (params: any) => Request.get('/api/lang/keyDetail', params)
/** 多语言 end */

/** 图片 start */
// 上传单张图片
export const uploadImg = (params: any) => Request.post('/api/common/upload-img', params)
// !未实现图片管理模块
// * 获取图片列表
export const getImgList = (params: any) => Request.get('/api/resource/img/list', params)
// * 获取图片信息
export const getImgMsg = (params: any) => Request.get('/api/resource/img/first', params)
// * 上传图片前校验
export const uploadImgsCheck = (params: any) => Request.post('/api/resource/img/upload_check', params)
// * 上传图片
export const uploadImgs = (params: any) => Request.post('/api/resource/img/upload_batch', params)
// * 删除图片
export const delImgs = (params: any) => Request.post('/api/resource/img/delete', params)
/** 图片库 end */

/** 礼包 start */
// * 礼包列表
export const getGiftPkgList = (params: any) => Request.get('/api/gift-package', params)
// * 礼包列表-全量，不区分游戏和平台
export const getGiftPkgNumList = () => Request.get('/api/gift-package/enum')
// * 创建礼包
export const createGiftPkg = (params: any) => Request.post('/api/gift-package', params)
// * 更新礼包内容
export const updateGiftPkg = (id: number, params: any) => Request.put(`/api/gift-package/${id}`, params)
// * 获取礼包详情
export const getGiftPkgDetail = (id: number) => Request.get(`/api/gift-package/${id}`)
// * 删除礼包
export const delGiftPkg = ({ id }: { id: number }) => Request.delete(`/api/gift-package/${id}`)
// * 导出礼包列表
export const exportGiftPkg = () => Request.downfile(`/api/gift-package/export`)
/** 礼包 end */

/** 道具 start */
// * 游戏道具列表
export const getPropsList = (params: any) => Request.get('/api/item-i18n', params)
// * 游戏道具列表 - 下拉搜索用
export const getPropsOpts = ({ search_key, type, game_project }: { search_key: string; type: any; game_project: string }) =>
  Request.get('/api/item-i18n/search', { search_key, type, game_project })
// * 创建游戏道具
export const createProps = (params: any) => Request.post('/api/item-i18n', params)
// * 更新游戏道具内容
export const updateProps = (id: number, params: any) => Request.put(`/api/item-i18n/${id}`, params)
// * 导出配置
export const downloadProps = () => Request.downfile('/api/item-i18n/export')
// * 获取游戏道具详情
export const getPropsDetail = (id: number) => Request.get(`/api/item-i18n/${id}`)
// * 删除游戏道具
export const delProps = ({ id }: { id: number }) => Request.delete(`/api/item-i18n/${id}`)
/** 道具 end */

/** 三方道具 start */
// * 平台道具列表
export const getTripartitePropsList = (params: any) => Request.get('/api/item-i18n', params)
// * 创建游戏道具
export const createTripartiteProps = (params: any) => Request.post('/api/item-i18n', params)
// * 更新游戏道具内容
export const updateTripartiteProps = (id: number, params: any) => Request.put(`/api/item-i18n/${id}`, params)
// * 导出配置
export const downloadTripartiteProps = () => Request.downfile('/api/item-i18n/export')
// * 获取游戏道具详情
export const getTripartitePropsDetail = (id: number) => Request.get(`/api/item-i18n/${id}`)
// * 删除游戏道具
export const delTripartiteProps = ({ id }: { id: number }) => Request.delete(`/api/item-i18n/${id}`)
/** 道具 end */

/** 平台道具 start */
// * 平台道具列表
export const getPlatformPropsList = (params: any) => Request.get('/api/platform-item', params)
// * 获取平台道具详情
export const getPlatformPropsDetail = (id: number) => Request.get(`/api/platform-item/${id}`)
// * 创建平台道具
export const createPlatformProps = (params: any) => Request.post('/api/platform-item', params)
// * 更新平台道具内容
export const updatePlatformProps = (params: any) => Request.put(`/api/platform-item/${params.id}`, params)
// * 删除平台道具
export const delPlatformProps = ({ id }: { id: number }) => Request.delete(`/api/platform-item/${id}`)
// * 复制平台道具
export const copyPlatformProps = ({ id }: { id: number }) => Request.put(`/api/platform-item/copy/${id}`)
/** 平台道具 end */
