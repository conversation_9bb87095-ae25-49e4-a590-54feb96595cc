import Request from '@/server'

/** 签到 start */
// * 签到管理列表
export const getDLCKList = (params: any) => Request.get('/api/active_checkin', params)
// * 详情
export const getDLCKDetail = (id: number) => Request.get(`/api/active_checkin/check_detail`, { id })
// * 修改签到
export const updateDLCK = (params: any) => Request.post('/api/active_checkin/update', params)
// * 删除签到
export const delDLCK = (params: any) => Request.delete(`/api/active_checkin`, params)
// * 复制
export const copyDLCK = (params: any) => Request.get(`/api/active_checkin/copy`, params)
// * 修改状态
export const setDLCKStatus = (params: any) => Request.get(`/api/active_checkin/enable`, params)

// * 获取签到奖励详情列表
export const getDLCKGiftList = (params: any) => Request.get('/api/active_checkin/detail', params)
// * 导出配置
export const downloadDLCKGift = (params: any) => Request.downfile('/api/active_checkin/export', params)
/** 签到 end */

// 新版月签到
// 列表
export const getDLCKNewList = (params: any) => Request.get('/api/active-checkin-v2', params)
// 详情
export const getDLCKNewDetail = (id: number) => Request.get(`/api/active-checkin-v2/${id}`)
// 新增
export const addDLCKNew = (params: any) => Request.post('/api/active-checkin-v2', params)
// 修改
export const updateDLCKNew = (params: FormData) => {
  const id = params.get('id')
  return Request.put(`/api/active-checkin-v2/${id}`, params)
}
// 删除
export const delDLCKNew = (params: any) => Request.delete(`/api/active-checkin-v2/${params.id}`, params)
// 复制
export const copyDLCKNew = (params: any) => Request.put(`/api/active-checkin-v2/copy/${params.id}`, params)
// 修改状态
export const setDLCKNewStatus = (params: any) => Request.put(`/api/active-checkin-v2/enable/${params.id}`, params)
// 获取签到奖励详情列表
export const getDLCKNewGiftList = (params: any) => Request.get(`/api/active-checkin-v2/config-detail/${params.id}`, params)
// 导出配置
export const downloadDLCKNewGift = (params: any) => Request.downfileGet(`/api/active-checkin-v2/export/${params.id}`, params)

// 周签到
// 列表
export const getDLCKWeeklyList = (params: any) => Request.get('/api/active-checkin-weekly', params)
// 详情
export const getDLCKWeeklyDetail = (id: number) => Request.get(`/api/active-checkin-weekly/${id}`)
// 新增
export const addDLCKWeekly = (params: FormData) => Request.post('/api/active-checkin-weekly', params)
// 修改
export const updateDLCKWeekly = (params: FormData) => {
  const id = params.get('id')
  return Request.put(`/api/active-checkin-weekly/${id}`, params)
}
// 删除
export const delDLCKWeekly = (params: any) => Request.delete(`/api/active-checkin-weekly/${params.id}`, params)
// 复制
export const copyDLCKWeekly = (params: any) => Request.put(`/api/active-checkin-weekly/copy/${params.id}`, params)
// 修改状态
export const setDLCKWeeklyStatus = (params: any) => Request.put(`/api/active-checkin-weekly/enable/${params.id}`, params)
// 获取签到奖励详情列表
export const getDLCKWeeklyGiftList = (params: any) => Request.get(`/api/active-checkin-weekly/config-detail/${params.id}`, params)
// 导出配置
export const downloadDLCKWeeklyGift = (params: any) => Request.downfileGet(`/api/active-checkin-weekly/export/${params.id}`, params)
