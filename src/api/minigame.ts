import Request from '@/server'

/** 小游戏 start */
// * 小游戏管理列表
export const getMNGList = (params: any) => Request.get('/api/mini-game', params)
// * 新增小游戏
export const createMNG = (params: any) => Request.post('/api/mini-game', params)
// * 详情
export const getMNGDetail = (id: number) => Request.get(`/api/mini-game/${id}`)
// * 修改小游戏
export const updateMNG = (id: number, params: any) => Request.put(`/api/mini-game/${id}`, params)
// * 删除小游戏
export const delMNG = ({ id }: { id: number }) => Request.delete(`/api/mini-game/${id}`)
// 切换状态
export const switchMNGStatus = ({id, is_online}: {id: number, is_online: number}) => Request.put(`/api/mini-game/switch-status/${id}`, { is_online })
/** 小游戏 end */
