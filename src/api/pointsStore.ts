import Request from '@/server'

// Type definitions for Points Store API
export interface PointsStoreItem {
  id: number
  cycle_type: number | string
  cycle_shape: number | string
  cycle_times: number
  cost_coin: number
  vip: number | string
  vip_max: number | string
  game_growth: number
  role_growth: number
  gift_id: number | string | null
  status: number
  times: number[] | undefined
  is_time_limit: number
  use_stocks: number
  item_limit_type: number | string
  stocks_num: number
  use_desc_key?: string | undefined
  online_type: number // 1: 立即上线 2: 定时上线
  online_timing: number // 定时上线 时间戳
  online_timing_str: string // 定时上线 时间字符串
  product_type: number // 商品类型 1: 游戏 2: 平台
  is_filter: number
  f_s_ids: string
  [key: string]: any
}

export interface PointsStoreListParams {
  page?: number
  page_size?: number
  search_key?: string
  status?: number
}

export interface PointsStoreListResponse {
  data: PointsStoreItem[]
  total: number
  page: number
  page_size: number
}

/** 积分商城 start */

/**
 * 获取积分商城商品列表
 * @param params 查询参数
 * @returns Promise<PointsStoreListResponse>
 */
export const getPropsList = (params: PointsStoreListParams): Promise<PointsStoreListResponse> =>
  Request.get('/api/points-mall-product', params)

/**
 * 创建积分商城商品
 * @param params 创建参数
 * @returns Promise<PointsStoreItem>
 */
export const createProps = (params: Partial<PointsStoreItem>): Promise<PointsStoreItem> => Request.post('/api/points-mall-product', params)

/**
 * 更新积分商城商品
 * @param id 商品ID
 * @param params 更新参数
 * @returns Promise<PointsStoreItem>
 */
export const updateProps = (id: number, params: Partial<PointsStoreItem>): Promise<PointsStoreItem> =>
  Request.put(`/api/points-mall-product/${id}`, params)

/**
 * 导出积分商城配置
 * @returns Promise<Blob>
 */
export const downloadProps = (): Promise<Blob> => Request.downfile('/api/points-mall-product/export')

/**
 * 获取积分商城商品详情
 * @param id 商品ID
 * @returns Promise<PointsStoreItem>
 */
export const getPropsDetail = (id: number): Promise<PointsStoreItem> => Request.get(`/api/points-mall-product/${id}`)

/**
 * 删除积分商城商品
 * @param petId 商品ID
 * @returns Promise<void>
 */
export const delProps = ({ petId }: { petId: number }): Promise<void> => Request.delete(`/api/points-mall-product/${petId}`)

/**
 * 更新积分商城商品状态
 * @param id 商品ID
 * @param status 状态值
 * @returns Promise<void>
 */
export const updatePropsStatus = (id: number, status: number): Promise<void> =>
  Request.put(`/api/points-mall-product/set-status/${id}`, { status })

/**
 * 复制积分商城商品
 * @param id 商品ID
 * @returns Promise<PointsStoreItem>
 */
export const copyProps = ({ id }: { id: number }): Promise<PointsStoreItem> => Request.put(`/api/points-mall-product/copy/${id}`)

/**
 * 导出积分商城商品列表
 * @returns Promise<Blob>
 */
export const downloadList = (): Promise<Blob> => Request.downfile(`/api/points-mall-product/export`)

/**
 * 调整商品库存
 * @param params 调整参数
 * @returns Promise<void>
 */
export const adjustStock = (params: { id: number; now_stocks: number }): Promise<void> =>
  Request.put(`/api/points-mall-product/adjust-stock/${params.id}`, params)

/** 积分商城 end */
