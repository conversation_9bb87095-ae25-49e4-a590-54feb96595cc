import Request from '@/server'

/** 权益入口 start */
// *权益列表
export const getPropsList = (params: any) => Request.get('/api/vip-right-config', params)
// * 创建
export const createProps = (params: any) => Request.post('/api/vip-right-config', params)
// * 更新内容
export const updateProps = (id: number, params: any) => Request.put(`/api/vip-right-config/${id}`, params)
// * 导出配置
export const downloadProps = () => Request.downfile('/api/vip-right-config/export')
// * 获取详情
export const getPropsDetail = (id: number) => Request.get(`/api/vip-right-config/${id}`)
// * 删除
export const delProps = ({ id }: { id: number }) => Request.delete(`/api/vip-right-config/${id}`)
// * 更新状态
// PUT/api/vip-right-config/set-status/{id}
export const updatePropsStatus = (id: number, status: number) => Request.put(`/api/vip-right-config/set-status/${id}`, { status })
// * 复制
export const copyProps = ({ id }: {id: number}) => Request.put(`/api/vip-right-config/copy/${id}`)
// * 上传梯度配置
export const uploadConfigs = (params: any) => Request.post('/api/vip-right-config/upload-ladder-configs', params)
// * 导出梯度配置
export const exportConfigs = ({ id }: {id: number}) => Request.downfile(`/api/vip-right-config/export-ladders/${id}`)
/** 权益入口 end */
