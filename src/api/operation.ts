import Request from "@/server";

/** SDK金刚位 start */
// * 金刚位管理列表
export const getSDKVajraList = (params: any) => Request.get("/api/sdk-diamond-position", params);
// * 新增金刚位
export const createSDKVajra = (params: any) => Request.post("/api/sdk-diamond-position", params);
// * 详情
export const getSDKVajraDetail = (id: number) => Request.get(`/api/sdk-diamond-position/${id}`);
// * 复制
export const copySDKVajra = ({ id }: { id: number }) => Request.put(`/api/sdk-diamond-position/copy/${id}`);
// * 修改金刚位
export const updateSDKVajra = (id: number, params: any) => Request.put(`/api/sdk-diamond-position/${id}`, params);
// * 修改金刚位状态
export const setSDKVajraStatus = (id: number, status: number) => Request.put(`/api/sdk-diamond-position/set-status/${id}`, { status });
// * 修改金刚位位置
export const setSDKVajraPos = ({ id, type }: { id: number; type: "up" | "down" }) =>
  Request.put(`/api/sdk-diamond-position/set-listorder/${id}`, { type });
// * 删除金刚位
export const delSDKVajra = ({ id }: { id: number }) => Request.delete(`/api/sdk-diamond-position/${id}`);
/** SDK金刚位 end */

/** H5金刚位 start */
// * 金刚位管理列表
export const getH5VajraList = (params: any) => Request.get("/api/priv-diamond-position", params);
// * 新增金刚位
export const createH5Vajra = (params: any) => Request.post("/api/priv-diamond-position", params);
// * 详情
export const getH5VajraDetail = (id: number) => Request.get(`/api/priv-diamond-position/${id}`);
// * 复制
export const copyH5Vajra = ({ id }: { id: number }) => Request.put(`/api/priv-diamond-position/copy/${id}`);
// * 修改金刚位
export const updateH5Vajra = (id: number, params: any) => Request.put(`/api/priv-diamond-position/${id}`, params);
// * 修改金刚位状态
export const setH5VajraStatus = (id: number, status: number) => Request.put(`/api/priv-diamond-position/set-status/${id}`, { status });
// * 修改金刚位位置
export const setH5VajraPos = ({ id, type }: { id: number; type: "up" | "down" }) =>
  Request.put(`/api/priv-diamond-position/set-listorder/${id}`, { type });
// * 删除金刚位
export const delH5Vajra = ({ id }: { id: number }) => Request.delete(`/api/priv-diamond-position/${id}`);
/** H5金刚位 end */

/** 活动banner start */
// * banner组列表
export const getBannerList = (params: any) => Request.get("/api/activity-banner-group", params);
// * 新增banner
export const createBanner = (params: any) => Request.post("/api/activity-banner-group", params);
// * 详情
export const getBannerDetail = (id: number) => Request.get(`/api/activity-banner-group/${id}`);
// * 修改banner
export const updateBanner = (id: number, params: any) => Request.put(`/api/activity-banner-group/${id}`, params);
// * 修改banner内容状态
export const setBannerStatus = (id: number, status: number) => Request.put(`/api/activity-banner-group/set-status/${id}`, { status });
// * 复制
export const copyBanner = ({ id }: { id: number }) => Request.put(`/api/activity-banner-group/copy/${id}`);
// * 删除banner
export const delBanner = ({ id }: { id: number }) => Request.delete(`/api/activity-banner-group/${id}`);

// * banner内容列表
export const getBannerItemList = (params: any) => Request.get("/api/activity-banner-item", params);
// * 新增banner
export const createBannerItem = (params: any) => Request.post("/api/activity-banner-item", params);
// * 详情
export const getBannerItemDetail = (id: number) => Request.get(`/api/activity-banner-item/${id}`);
// * 修改banner内容
export const updateBannerItem = (id: number, params: any) => Request.put(`/api/activity-banner-item/${id}`, params);
// * 修改banner内容状态
export const setBannerItemStatus = (id: number, status: number) => Request.put(`/api/activity-banner-item/set-status/${id}`, { status });
// * 修改banner内容位置
export const setBannerItemPos = ({ id, params }: { id: number; params: any }) =>
  Request.put(`/api/activity-banner-item/set-listorder/${id}`, params);
// * 删除banner
export const delBannerItem = ({ id }: { id: number }) => Request.delete(`/api/activity-banner-item/${id}`);
/** 活动banner end */

/** SDK图标推送 start */
// * 列表
export const getSDKIconsPushList = (params: any) => Request.get("/api/corner-mark-config", params);
// * 新增
export const createSDKIconsPush = (params: any) => Request.post("/api/corner-mark-config", params);
// * 详情
export const getSDKIconsPushDetail = (id: number) => Request.get(`/api/corner-mark-config/${id}`);
// * 修改
export const updateSDKIconsPush = (id: number, params: any) => Request.put(`/api/corner-mark-config/${id}`, params);
// * 修改状态
export const setSDKIconsPushStatus = (id: number, status: number) => Request.put(`/api/corner-mark-config/set-status/${id}`, { status });
// * 删除
export const delSDKIconsPush = ({ id }: { id: number }) => Request.delete(`/api/corner-mark-config/${id}`);
/** SDK图标推送 end */

/** SDK广告配置 start */
// * 列表
export const getSDKAdsList = (params: any) => Request.get("/api/ad-config", params);
// * 新增
export const createSDKAds = (params: any) => Request.post("/api/ad-config", params);
// * 详情
export const getSDKAdsDetail = (id: number) => Request.get(`/api/ad-config/${id}`);
// * 修改
export const updateSDKAds = (id: number, params: any) => Request.put(`/api/ad-config/${id}`, params);
// * 修改状态
export const setSDKAdsStatus = (id: number, status: number) => Request.put(`/api/ad-config/set-status/${id}`, { status });
// * 删除
export const delSDKAds = ({ id }: { id: number }) => Request.delete(`/api/ad-config/${id}`);
// * 修改位置
export const setSDKAds = ({ id, type }: { id: number; type: "up" | "down" }) => Request.put(`/api/ad-config/set-listorder/${id}`, { type });
/** SDK广告配置 end */
