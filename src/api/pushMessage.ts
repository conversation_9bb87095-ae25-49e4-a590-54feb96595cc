import Request from '@/server'

/** 消息推送 start */
// *消息推送列表
export const getPropsList = (params: any) => Request.get('/api/system-message', params)
// * 创建
export const createProps = (params: any) => Request.post('/api/system-message', params)
// * 更新内容
export const updateProps = (id: number, params: any) => Request.put(`/api/system-message/${id}`, params)
// * 导出配置
export const downloadProps = () => Request.downfile('/api/system-message/export')
// * 获取详情
export const getPropsDetail = (id: number) => Request.get(`/api/system-message/${id}`)
// * 删除
export const delProps = ({ petId }: { petId: number }) => Request.delete(`/api/system-message/${petId}`)
// * 更新状态
// PUT/api/system-message/set-status/{id}
export const updatePropsStatus = (id: number, status: number) => Request.put(`/api/system-message/set-status/${id}`, { status })
// * 复制
export const copyProps = ({ id }: {id: number}) => Request.put(`/api/system-message/copy/${id}`)
// * 上传消息模版
export const uploadConfigs = (params: any) => Request.post('/api/system-message/upload-templates', params)
// * 导出消息配置
export const exportConfigs = (key: string) => Request.downfile(`/api/system-message/export-templates?key=${key}`, { key })
// * 发布
export const publish = (id: number) => Request.put(`/api/system-message/publish/${id}`)
/** 消息推送 end */
