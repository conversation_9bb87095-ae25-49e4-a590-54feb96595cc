import Request from "@/server";

/** 游戏接入 start */
// * 入口管理列表
export const getGPList = (params: any) => Request.get("/api/game-platform", params);
// * 新增入口
export const createGP = (params: any) => Request.post("/api/game-platform", params);
// * 修改入口
export const updateGP = (id: number, params: any) => Request.put(`/api/game-platform/${id}`, params);
// * 详情
export const getGPDetail = (id: number) => Request.get(`/api/game-platform/${id}`);
// * 删除入口
export const delGP = ({ id }: { id: number }) => Request.delete(`/api/game-platform/${id}`);
// * 修改状态
export const switchGPStatus = (id: number, status: number) => Request.put(`/api/game-platform/set-status/${id}`, { status });
/** 游戏接入 end */

/** 入口管理 start */
// * 入口管理列表
export const getMSList = (params: any) => Request.get("/api/master-switch", params);
// * 新增入口
export const createMS = (params: any) => Request.post("/api/master-switch", params);
// * 修改入口
export const updateMS = (id: number, params: any) => Request.put(`/api/master-switch/${id}`, params);
// * 详情
export const getMSDetail = (id: number) => Request.get(`/api/master-switch/${id}`);
// * 删除入口
export const delMS = ({ id }: { id: number }) => Request.delete(`/api/master-switch/${id}`);
// * 修改状态
export const switchMSStatus = (id: number, status: number) => Request.put(`/api/master-switch/set-status/${id}`, { status });
/** 入口管理 end */

/** 通用规则 start */
// * 列表
export const getCommonRule = (params: any) => Request.get("/api/rule", params);
// * 获取规则详情
export const getCommonRuleDetail = (key: string) => Request.get("/api/rule/keyDetail", { key });
// * 更新规则
export const updateCommonRule = (params: any) => Request.put("/api/rule/update", params);
// * 上传文件
export const updateRuleFile = (params: any) => Request.post("/api/rule/upload", params);
/** 通用规则 end */

/** 融合开关 start */
// * 列表
export const getCombineRuleList = (params: any) => Request.get("/api/merge-switch", params);
// * 新增
export const createCombineRule = (params: any) => Request.post("/api/merge-switch", params);
// * 修改
export const updateCombineRule = (id: number, params: any) => Request.put(`/api/merge-switch/${id}`, params);
// * 详情
export const getCombineRuleDetail = (id: number) => Request.get(`/api/merge-switch/${id}`);
// * 删除
export const delCombineRule = ({ id }: { id: number }) => Request.delete(`/api/merge-switch/${id}`);
// * 修改状态
export const switchCombineRuleStatus = (id: number, status: number) => Request.put(`/api/merge-switch/set-status/${id}`, { status });
/** 融合开关 end */
