{"name": "priv-admin-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:release": "vue-tsc && vite build --mode=release", "build:master": "vue-tsc && vite build --mode=master", "build:online": "vite build --mode=online", "build-only": "NODE_OPTIONS=--max_old_space_size=8192 vite build", "preview": "vite preview", "report": "rimraf dist && cross-env vite build"}, "dependencies": {"@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.4.0", "dayjs": "^1.10.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-kinesis": "^2.0.5", "yapi-download": "^1.0.1"}, "devDependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify-json/ep": "^1.1.12", "@types/lodash": "^4.14.202", "@types/lodash-es": "^4.17.12", "@types/node": "^20.5.3", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "ant-design-vue": "4.x", "cross-env": "^7.0.3", "increase-memory-limit": "^1.0.7", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.66.1", "sql-formatter": "^15.2.0", "tiny-emitter": "^2.1.0", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.6", "unplugin-icons": "^0.16.5", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-webpackchunkname": "^1.0.1", "vue-router": "4", "vue-tsc": "^1.8.5", "vue3-clipboard": "^1.0.0"}}